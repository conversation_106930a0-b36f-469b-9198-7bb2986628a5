// Ürün tipi tanımlaması
export interface Product {
    id: number;
    title: string;
    description: string;
    price: number;
    discountPercentage?: number;
    rating: number;
    stock: number;
    brand: string;
    category: string;
    thumbnail: string;
    images: string[];
    points: number;
}

// Yeni API için kategori tipi
export interface CategoryItem {
    id: number;
    name: string;
}

// Yeni API için alt kategori tipi
export interface SubCategoryItem {
    id: number;
    name: string;
}

// Ürün sıralama seçenekleri enum'u
export enum ProductSortOption {
    Default = 0,
    PriceAsc = 1,
    PriceDesc = 2,
    RatingDesc = 3,
}

// Ürün filtreleme API request tipi
export interface ProductFilterRequest {
    brandIds?: number[];
    categoryIds?: number[];
    subCategoryIds?: number[];
    featureValueIds?: number[];
    minPrice?: number;
    maxPrice?: number;
    sortBy?: ProductSortOption; // Backend enum ile uyumlu
}

// Ürün filtreleme API response tipi
export interface FilteredProduct {
    id: number;
    name: string;
    description: string;
    brandId: number;
    brandName: string;
    categoryId: number;
    categoryName: string;
    subCategoryId: number;
    subCategoryName: string;
    price: number;
    extraDiscount: number | null;
    cv: number | null;
    pv: number | null;
    sp: number | null;
    averageRating: number | null;
    totalReviewCount: number;
    mainImageUrl: string;
}

// Ürün filtreleme API response wrapper
export interface ProductFilterResponse {
    status: number;
    message: string;
    data: FilteredProduct[];
}

// Discount rate API response tipi
export interface DiscountRateResponse {
    discountRate: number | null;
}

// Reference data API için tipler
export interface BrandReference {
    id: number;
    name: string;
}

export interface FeatureDefinitionReference {
    id: number;
    name: string;
}

export interface FeatureValueReference {
    id: number;
    name: string;
    featureDefinitionId: number;
}

export interface ReferenceDataResponse {
    status: number;
    message: string;
    data: {
        brands: BrandReference[];
        featureDefinitions: FeatureDefinitionReference[];
        featureValues: FeatureValueReference[];
    };
}

// Kullanıcı tipi tanımlaması
export interface User {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    address?: Address;
    role: 'customer' | 'dealership' | 'admin';
    joinDate: string;
}

// Adres tipi tanımlaması - API şemasına uygun
export interface Address {
    id?: number;
    title: string;
    fullAddress: string;
    city: string;
    district: string;
    postalCode: string;
    isDefault: boolean;
}

// API'de kullanılacak adres oluşturma request tipi
export interface CreateAddressRequest {
    title: string;
    fullAddress: string;
    city: string;
    district: string;
    postalCode: string;
    isDefault: boolean;
}

// API'de kullanılacak adres silme request tipi
export interface DeleteAddressRequest {
    addressId: number;
    userId: number;
}

// Sepet ögesi tipi tanımlaması
export interface CartItem {
    id: number;
    title: string;
    price: number;
    thumbnail: string;
    brand: string;
    quantity: number;
    discountPercentage?: number;
    points: number;
}

// Sepet tipi tanımlaması
export interface Cart {
    id: number;
    userId: number;
    items: CartItem[];
    total: number;
}

// Sipariş durumu tanımlaması
export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

// Sipariş öğesi tipi tanımlaması
export interface OrderItem {
    id: number;
    productId: number;
    quantity: number;
    price: number;
    product: Product;
}

// Sipariş tipi tanımlaması
export interface Order {
    id: number;
    userId: number;
    orderDate: string;
    status: OrderStatus;
    items: OrderItem[];
    shippingAddress: Address;
    billingAddress: Address;
    shippingMethod: string;
    paymentMethod: string;
    subtotal: number;
    tax: number;
    shippingCost: number;
    total: number;
}

// Sipariş detayları için yeni tipler
export interface OrderDetailItem {
    id: number;
    name: string;
    brand: string;
    image: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    points: number;
}

export interface OrderPricing {
    subtotal: number;
    discount: number;
    shippingCost: number;
    tax: number;
    total: number;
}

export interface ShippingAddress {
    name: string;
    phone: string;
    address: string;
    district: string;
    city: string;
    postalCode: string;
}

export interface ShippingInfo {
    method: string;
    address: ShippingAddress;
}

export interface PaymentInfo {
    method: string;
    cardLast4?: string;
    paymentDate: string;
}

export interface TimelineStep {
    status: string;
    date: string;
    description: string;
}

export interface OrderDetails {
    id: string;
    orderDate: string;
    status: string;
    statusClass: string;
    deliveryDate?: string;
    trackingNumber?: string;
    items: OrderDetailItem[];
    pricing: OrderPricing;
    shippingInfo: ShippingInfo;
    paymentInfo: PaymentInfo;
    timeline: TimelineStep[];
}

export interface OrderDetailsMap {
    [key: string]: OrderDetails;
}

// Distribütör kazanç tipi tanımlaması
export interface DistributorEarning {
    id: number;
    distributorId: number;
    date: string;
    reference: string; // Kimden geldiği
    points: number;
    amount: number;
    level: number;
    percentage: number;
}

// Distribütör ekip üyesi tipi tanımlaması
export interface TeamMember {
    id: number;
    firstName: string;
    lastName: string;
    level: number;
    joinDate: string;
    points: number;
    isActive: boolean;
}

// Duyuru tipi tanımlaması
export interface Announcement {
    id: number;
    title: string;
    content: string;
    date: string;
    isImportant: boolean;
}

// Yorum tipi tanımlaması
export interface Review {
    id: number;
    productId: number;
    userId: number;
    userName: string;
    rating: number;
    comment: string;
    date: string;
}

// MembershipLevelIds enum - distribütör seviyelerini tanımlar
export enum MembershipLevelIds {
    None = 0,
    Baslangic = 1,
    Girisimci = 2,
    Bronz = 3,
    Gumus = 4,
    Altin = 5,
    Platin = 6,
    PlatinMax = 7
}

// Gender enum - Swagger API'ye uygun
export enum GenderType {
    Unspecified = 0,
    Male = 1,
    Female = 2,
    Other = 3
}

// Auth sistemi tipleri
export interface AuthUser {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    isActive: boolean;
    registeredAt: string;
    membershipLevelId: number;
    careerRankId: number;
    referenceId: number;
    roles: string[];
    // Computed properties
    role: 'admin' | 'dealership' | 'customer';
    membershipLevel: MembershipLevelIds;
    joinDate: string;
    isDealershipApproved?: boolean; // Satıcı onay durumu
}

export interface LoginCredentials {
    email: string;
    password: string;
}

export interface RegisterData {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    phoneNumber?: string;
    referansCode?: string;
    role?: 'admin' | 'dealership' | 'customer';
}

export interface AuthContextType {
    user: AuthUser | null;
    login: (credentials: LoginCredentials) => Promise<boolean>;
    register: (data: RegisterData) => Promise<boolean>;
    logout: () => Promise<void>;
    updateUserRole: (userId: number, newRole: 'admin' | 'dealership' | 'customer', isDealershipApproved?: boolean, applicationStatus?: 'approved' | 'rejected') => Promise<void>;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;
}

// Sepet için yeni type'lar
export interface CartContextType {
    items: CartItem[];
    addToCart: (product: any) => void;
    removeFromCart: (productId: number) => void;
    updateQuantity: (productId: number, quantity: number) => void;
    clearCart: () => void;
    getTotalItems: () => number;
    getTotalPrice: () => number;
    getTotalPoints: () => number;
}

// Distribütör paneli için dashboard tipi
export interface DistributorDashboard {
    totalEarnings: number;
    monthlyPoints: number;
    monthlyActivityPercentage: number;
    teamSize: number;
    monthlyBalance: number;
    totalBalance: number;
    totalPoints: number;
    organizationPoints: number;
    monthlyEarnings: MonthlyEarning[];
    monthlyPointsHistory: MonthlyPoints[];
    monthlyActivityTrend: MonthlyActivity[];
    nextLevelPoints: number;
    currentLevel: string;
    nextLevel: string;
}

export interface MonthlyEarning {
    month: string;
    earnings: number;
    activity: number;
}

export interface MonthlyPoints {
    month: string;
    points: number;
    target: number;
}

export interface MonthlyActivity {
    month: string;
    activityPercentage: number;
    teamSize: number;
    newMembers: number;
}

// Ekip ağacı için binary tree node yapısı
export interface TeamTreeNode {
    id: number;
    name: string;
    level: number;
    points: number;
    joinDate: string;
    isActive: boolean;
    parentId?: number;
    position?: 'left' | 'right';
    children?: {
        left?: TeamTreeNode;
        right?: TeamTreeNode;
    };
    sponsorId?: number;
    totalEarnings: number;
    monthlyPoints: number;
}

// Ekip ağacı istatistikleri
export interface TeamTreeStats {
    totalMembers: number;
    activeMembers: number;
    totalLevels: number;
    totalPoints: number;
    totalEarnings: number;
    monthlyGrowth: number;
}

// Admin paneli için tip tanımlamaları
export interface AdminStats {
    totalUsers: number;
    totalOrders: number;
    totalRevenue: number;
    totalProducts: number;
    newUsersThisMonth: number;
    ordersThisMonth: number;
    revenueThisMonth: number;
    monthlyStats: AdminMonthlyStats[];
}

export interface AdminMonthlyStats {
    month: string;
    users: number;
    orders: number;
    revenue: number;
    products: number;
}

export interface AdminUser {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    role: 'admin' | 'dealership' | 'customer';
    membershipLevel?: MembershipLevelIds;
    joinDate: string;
    isActive: boolean;
    lastLogin?: string;
    totalOrders: number;
    totalSpent: number;
}

export interface AdminOrder {
    id: string;
    userId: number;
    userName: string;
    userEmail: string;
    orderDate: string;
    status: OrderStatus;
    total: number;
    itemCount: number;
    shippingMethod: string;
    paymentMethod: string;
}

export interface AdminProduct {
    id: number;
    name: string;
    brandName: string;
    categoryName: string;
    price: number;
    stock: number;
    cv: number;
    pv: number;
    sp: number;
    salesCount: number;
    imageUrl: string | null;
    isActive: boolean;
    updatedAt: string;
    createdByUserId: number;
    createdByUserName: string;
    createdByUserRole: string;
    status: ProductStatus;
}

// Kullanıcıya ait ürün interface'i
export interface MyProduct {
    id: number;
    name: string;
    description: string;
    brandId: number;
    brandName: string;
    subCategoryId: number;
    subCategoryName: string;
    categoryId: number;
    categoryName: string;
    statusId: number;
    createdAt: string;
    isActive: boolean;
    totalStock: number;
    price: number;
    imageUrl: string | null;
}

// Dealership ürün detay interface'i
export interface DealershipProductDetail {
    id: number;
    name: string;
    description: string;
    brandName: string;
    subCategoryName: string;
    categoryName: string;
    createdByUserId: number;
    createdByUserName: string;
    createdByUserRole: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    status: number;
    variants: DealershipProductVariant[];
}

export interface DealershipProductVariant {
    id: number;
    stock: number;
    price: number;
    isActive: boolean;
    createdAt: string;
    features: DealershipProductFeature[];
    images: DealershipProductImage[];
}

export interface DealershipProductFeature {
    featureId: number; // Backend'den gelen yeni field
    featureName: string;
    featureValue: string;
}

export interface DealershipProductImage {
    id: number;
    url: string;
    isMain: boolean;
    sortOrder: number;
}

// Product Status Enum
export enum ProductStatus {
    Pending = 0,
    Accepted = 1,
    Rejected = 2
}

// Product Detail API Response Types
export interface ProductDetailResponse {
    id: number;
    name: string;
    description: string;
    brandId: number;
    brandName: string;
    categoryId: number;
    categoryName: string;
    subCategoryId: number;
    subCategoryName: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    createdByUserId: number;
    createdByUserName: string;
    createdByUserRole: string;
    status: ProductStatus;
    variants: ProductVariant[];
}

export interface ProductVariant {
    id: number;
    stock: number;
    price: number; // API'den 'price' olarak geliyor
    extraDiscount: number;
    pv: number;
    cv: number;
    sp: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    features: ProductVariantFeature[];
    images: ProductVariantImage[]; // Yeni format - ID'lerle birlikte
}

export interface ProductVariantImage {
    id: number;
    url: string;
    isMain: boolean;
    sortOrder: number;
}

export interface ProductVariantFeature {
    featureId: number; // Backend'den gelen yeni field
    featureName: string;
    featureValue: string;
}

// Ürün onay durumu
export type ProductApprovalStatus = 'pending' | 'approved' | 'rejected';

// Admin ürün istatistikleri
export interface AdminProductStatistics {
    totalProductCount: number;
    acceptedCount: number;
    rejectedCount: number;
    pendingCount: number;
    activeCount: number;
    lowStockCount: number;
    outOfStockCount: number;
}

// Admin kullanıcı yönetimi için tipler
export interface AdminUser {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    roleId: number | null;
    registeredAt: string;
    lastLoginAt: string | null;
    membershipLevelId: number;
    isActive: boolean;
    orderCount: number;
    totalSpent: number;
}

// Kullanıcı rol sayıları
export interface UserRoleStatistics {
    totalUserCount: number;
    adminCount: number;
    dealershipCount: number;
    customerCount: number;
}

// Kullanıcı ürün istatistikleri
export interface MyProductStatistics {
    totalProductCount: number;
    approvedCount: number;
    rejectedCount: number;
    pendingCount: number;
}

// Ürün admin notu
export interface ProductMessage {
    id: number;
    productId: number;
    productName: string;
    oldStatus: number;
    newStatus: number;
    changedAtUtc: string;
    message: string | null; // Null olabilir
    approvedByUser: {
        id: number;
        fullName: string;
        role: string;
    };
}

// Catalog Product Detail API Response Types (Public)
export interface CatalogProductDetailResponse {
    status: number;
    message: string;
    data: CatalogProductDetail;
}

export interface CatalogProductDetail {
    id: number;
    name: string;
    description: string;
    brandName: string;
    categoryName: string;
    averageRating: number | null;
    variants: CatalogProductVariant[];
}

export interface CatalogProductVariant {
    id: number;
    price: number;
    stockStatus: number; // 0: Stokta yok, 1: Az stok, 2: Stokta var
    extraDiscount: number;
    pv: number;
    cv: number;
    sp: number;
    images: CatalogProductImage[];
    features: CatalogProductFeature[];
}

export interface CatalogProductImage {
    id: number;
    url: string;
    isMain: boolean;
    sortOrder: number;
}

export interface CatalogProductFeature {
    featureDefinitionId: number;
    featureValueId: number;
    featureName: string;
    featureValue: string;
}

// Distribütör ürünü (onay bekleyen)
export interface PendingProduct {
    id: number;
    title: string;
    description: string;
    price: number;
    stock: number;
    category: string;
    brand: string;
    images: string[];
    thumbnail: string;
    points: number;
    discountPercentage?: number;
    distributorId: number;
    distributorName: string;
    submittedAt: string;
    status: ProductApprovalStatus;
    adminNotes?: string;
    reviewedAt?: string;
    reviewedBy?: number;
}

// Favorites sistemi için tipler
export interface FavoriteItem {
    id: number;
    productId: number;
    product: Product;
    addedAt: string;
}

export interface FavoritesContextType {
    favorites: FavoriteItem[];
    addToFavorites: (product: Product) => void;
    removeFromFavorites: (productId: number) => void;
    isFavorite: (productId: number) => boolean;
    getFavoritesCount: () => number;
}

// Satıcı başvuru formu için tip tanımları
export interface DealershipApplicationData {
    // Hesap bilgileri (mevcut kullanıcıdan gelecek)
    firstName: string;
    lastName: string;
    email: string;
    phone: string;

    // Ürün bilgileri
    mainProductCategory: string;
    subProductCategories?: string[];
    estimatedProductCount: string;
    sampleProductListUrl?: string;

    // Şirket bilgileri
    companyName: string;
    taxNumber: string;
    taxOffice: string;
    companyAddress: string;

    // Belgeler
    taxCertificate?: File | null;
    tradeRegistryGazette?: File | null;
    signatureCircular?: File | null;
    additionalDocuments?: File[];

    // Yetkili kişi bilgileri
    authorizedPersonName: string;
    authorizedPersonTcId: string;
    alternativeContactNumber: string;

    // Sözleşme onayları
    userAgreementAccepted: boolean;
    dealershipAgreementAccepted: boolean;
    privacyPolicyAccepted: boolean;
}

// Satıcı başvuru durumu
export type DealershipApplicationStatus = 'pending' | 'approved' | 'rejected';

// Satıcı başvurusu
export interface DealershipApplication {
    id: number;
    userId: number;
    userName: string;
    userEmail: string;
    applicationData: DealershipApplicationData;
    status: DealershipApplicationStatus;
    submittedAt: string;
    reviewedAt?: string;
    reviewedBy?: number;
    adminNotes?: string;
}

// Yeni ürün ekleme için API tipi tanımlamaları
export interface Brand {
    id: number;
    name: string;
    logoUrl?: string;
}

export interface Category {
    id: number;
    name: string;
    brandId: number;
}

export interface SubCategory {
    id: number;
    name: string;
    categoryId: number;
}

export interface FeatureDefinition {
    id: number;
    name: string;
    description?: string;
}

export interface FeatureValue {
    id: number;
    value: string;
    featureDefinitionId: number;
}

export interface SubCategoryFeature {
    id: number;
    subCategoryId: number;
    featureDefinitionId: number;
    featureDefinition: FeatureDefinition;
    featureValues: FeatureValue[];
}

export interface ProductImage {
    id?: number;
    url: string;
    isMain: boolean;
    sortOrder: number;
    file?: File;
}

export interface VariantFormDataOld {
    id?: number;
    stock: number;
    priceOverride?: number;
    pv: number;
    cv: number;
    sp: number;
    featureValueIds: number[];
    images: ProductImage[];
    selectedFeatures?: { [key: number]: number[] }; // FeatureDefinitionId -> FeatureValueId[]
}

export interface CreateProductRequest {
    name: string;
    description: string;
    brandId: number;
    subCategoryId: number;
    stock: number;
    price: number;
    extraDiscount: number;
    pv: number;
    cv: number;
    sp: number;
    featureValueIds: number[];
    isActive: boolean;
    images?: ProductImage[];
}

export interface CreateProductVariantRequest {
    productId: number;
    stock: number;
    priceOverride?: number;
    pv: number;
    cv: number;
    sp: number;
    featureValueIds: number[];
    images?: ProductImage[];
}

export interface ProductRatios {
    pvRatio: number;
    cvRatio: number;
    spRatio: number;
}

export interface ProductPoints {
    pv: number;
    cv: number;
    sp: number;
}

export interface ProductFormData {
    // Temel bilgiler
    name: string;
    description: string;

    // Marka ve kategori
    brandId: number;
    categoryId: number;
    subCategoryId: number;

    // Özellikler
    selectedFeatures: { [key: number]: number[] }; // FeatureDefinitionId -> FeatureValueId[]

    // Fiyat ve stok
    price: number;
    stock: number;
    extraDiscount: number;

    // Puan oranları
    ratios: ProductRatios;

    // Hesaplanan puanlar
    points: ProductPoints;

    // Variant bilgisi
    hasVariants: boolean;
    variants: VariantFormData[];

    // Görseller
    images: ProductImage[];

    // Durum
    isActive: boolean;
}

// Varyant için özellik detayları
export interface VariantFeatureDetail {
    featureDefinitionId: number;
    featureValueId: number;
    featureName: string;
    featureValue: string;
}

// Varyant için fiyat bilgisi
export interface VariantPricing {
    price: number;
    stock: number;
    extraDiscount: number;
    ratios: ProductRatios;
    points: ProductPoints;
}

// Varyant için form verileri
export interface VariantFormData {
    id?: number | string;
    name: string; // Varyant adı (örn: "Kırmızı - L")
    selectedFeatures: { [key: number]: number[] }; // FeatureDefinitionId -> FeatureValueId[]
    features: VariantFeatureDetail[]; // API'den gelen feature bilgileri
    featureDetails: VariantFeatureDetail[];
    pricing: VariantPricing;
    images: ProductImage[];
    isActive: boolean;
}

// Varyant oluşturma modalı için props
export interface VariantCreationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (variant: VariantFormData) => void;
    availableFeatures: SubCategoryFeature[];
    existingVariants: VariantFormData[];
    editingVariant?: VariantFormData;
    hidePvCvSp?: boolean; // Satıcılar için PV, CV, SP alanlarını gizle
    colorScheme?: 'red' | 'blue'; // Renk şeması: admin için kırmızı, satıcı için mavi
}

// Varyant validasyon hatası
export interface VariantValidationError {
    field: string;
    message: string;
}