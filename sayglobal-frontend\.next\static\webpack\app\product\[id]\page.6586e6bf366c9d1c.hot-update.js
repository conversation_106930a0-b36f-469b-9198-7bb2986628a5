"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _contexts_FavoritesContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/FavoritesContext */ \"(app-pages-browser)/./src/contexts/FavoritesContext.tsx\");\n/* harmony import */ var _components_AddToCartModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AddToCartModal */ \"(app-pages-browser)/./src/components/AddToCartModal.tsx\");\n/* harmony import */ var _components_FavoriteModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/FavoriteModal */ \"(app-pages-browser)/./src/components/FavoriteModal.tsx\");\n/* harmony import */ var _components_ImageModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ImageModal */ \"(app-pages-browser)/./src/components/ImageModal.tsx\");\n/* harmony import */ var _hooks_useCatalogProducts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useCatalogProducts */ \"(app-pages-browser)/./src/hooks/useCatalogProducts.ts\");\n/* harmony import */ var _hooks_useDiscountRate__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useDiscountRate */ \"(app-pages-browser)/./src/hooks/useDiscountRate.ts\");\n/* harmony import */ var _hooks_useCart__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useCart */ \"(app-pages-browser)/./src/hooks/useCart.ts\");\n/* harmony import */ var _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/stores/catalogProductDetailStore */ \"(app-pages-browser)/./src/stores/catalogProductDetailStore.ts\");\n/* harmony import */ var _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/stores/customerPriceStore */ \"(app-pages-browser)/./src/stores/customerPriceStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDetailPage() {\n    _s();\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [productId, setProductId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddToCartModal, setShowAddToCartModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastAddedProduct, setLastAddedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFavoriteModal, setShowFavoriteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favoriteModalIsAdded, setFavoriteModalIsAdded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Hooks\n    const { addToCart } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_5__.useCart)();\n    const { addToFavorites, removeFromFavorites, isFavorite } = (0,_contexts_FavoritesContext__WEBPACK_IMPORTED_MODULE_6__.useFavorites)();\n    const { data: discountRateData, isLoading: discountRateLoading } = (0,_hooks_useDiscountRate__WEBPACK_IMPORTED_MODULE_11__.useDiscountRate)();\n    const addToCartMutation = (0,_hooks_useCart__WEBPACK_IMPORTED_MODULE_12__.useAddToCart)();\n    // Global customer price state\n    const { isCustomerPrice } = (0,_stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_14__.useCustomerPriceStore)();\n    // Store actions\n    const { setSelectedVariant, setCurrentImage, increaseQuantity, decreaseQuantity, resetState } = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore)();\n    // Store selectors\n    const selectedVariant = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSelectedVariant)();\n    const currentImage = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImage)();\n    const variantImages = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useVariantImages)();\n    const quantity = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductQuantity)();\n    const slideDirection = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSlideDirection)();\n    const currentImageIndex = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImageIndex)();\n    // API call\n    const { data: productData, isLoading, error } = (0,_hooks_useCatalogProducts__WEBPACK_IMPORTED_MODULE_10__.useCatalogProductDetail)(productId);\n    // Initialize product ID from params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            if (id) {\n                const numericId = parseInt(id);\n                if (!isNaN(numericId)) {\n                    setProductId(numericId);\n                }\n            }\n        }\n    }[\"ProductDetailPage.useEffect\"], [\n        id\n    ]);\n    // Reset store when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            return ({\n                \"ProductDetailPage.useEffect\": ()=>{\n                    resetState();\n                }\n            })[\"ProductDetailPage.useEffect\"];\n        }\n    }[\"ProductDetailPage.useEffect\"], []); // Empty dependency array to run only on mount/unmount\n    // Loading state\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: \"\\xdcr\\xfcn Y\\xfckleniyor...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"\\xdcr\\xfcn detayları getiriliyor, l\\xfctfen bekleyin.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 13\n        }, this);\n    }\n    // Error state\n    if (error || !productData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-16 w-16 mx-auto text-gray-400 mb-4\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: \"\\xdcr\\xfcn Bulunamadı\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: (error === null || error === void 0 ? void 0 : error.message) || 'Aradığınız ürün bulunamadı veya artık mevcut değil.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/products\",\n                        className: \"inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300\",\n                        children: \"\\xdcr\\xfcnlere D\\xf6n\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 103,\n            columnNumber: 13\n        }, this);\n    }\n    const product = productData.data;\n    // Get stock status text based on API enum\n    const getStockStatusText = (stockStatus)=>{\n        switch(stockStatus){\n            case 0:\n                return {\n                    text: 'Stokta yok',\n                    color: 'text-red-600'\n                }; // OutOfStock\n            case 1:\n                return {\n                    text: 'Az stok',\n                    color: 'text-yellow-600'\n                }; // LowStock\n            case 2:\n                return {\n                    text: 'Stokta var',\n                    color: 'text-green-600'\n                }; // InStock\n            default:\n                return {\n                    text: 'Bilinmiyor',\n                    color: 'text-gray-600'\n                };\n        }\n    };\n    // Calculate points from ratios (pv, cv, sp are percentages) based on original price\n    const calculatePoints = (ratio, originalPrice)=>{\n        return Math.round(ratio / 100 * originalPrice);\n    };\n    // Calculate discounted price with membership discount and extra discount\n    const calculateDiscountedPrice = function(originalPrice, extraDiscount) {\n        let applyMembershipDiscount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n        let finalPrice = originalPrice;\n        const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n        // Müşteri fiyatları gösteriliyorsa membership discount uygulama\n        const shouldApplyMembershipDiscount = applyMembershipDiscount && !isCustomerPrice;\n        // Önce membership discount uygula (eğer varsa ve uygulanacaksa)\n        if (shouldApplyMembershipDiscount && discountRate && discountRate > 0) {\n            finalPrice = finalPrice * (1 - discountRate / 100);\n        }\n        // Sonra extra discount uygula\n        if (extraDiscount > 0) {\n            finalPrice = finalPrice * (1 - extraDiscount / 100);\n        }\n        return finalPrice;\n    };\n    // Check if there's any discount applied\n    const hasAnyDiscount = function(extraDiscount) {\n        let applyMembershipDiscount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n        return applyMembershipDiscount && discountRate && discountRate > 0 || extraDiscount > 0;\n    };\n    // Image navigation functions\n    const goToPreviousImage = (e)=>{\n        var _state_productData;\n        if (e) {\n            e.stopPropagation(); // Modal açılmasını engelle\n        }\n        // Explicitly set direction before calling prevImage\n        const { setCurrentImage } = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const state = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const currentVariant = (_state_productData = state.productData) === null || _state_productData === void 0 ? void 0 : _state_productData.data.variants[state.selectedVariantIndex];\n        const maxIndex = (currentVariant === null || currentVariant === void 0 ? void 0 : currentVariant.images.length) || 0;\n        if (maxIndex > 0) {\n            const prevIndex = state.currentImageIndex === 0 ? maxIndex - 1 : state.currentImageIndex - 1;\n            // Set direction first, then update image\n            _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.setState({\n                slideDirection: -1\n            });\n            setTimeout(()=>{\n                setCurrentImage(prevIndex);\n            }, 0);\n        }\n    };\n    const goToNextImage = (e)=>{\n        var _state_productData;\n        if (e) {\n            e.stopPropagation(); // Modal açılmasını engelle\n        }\n        // Explicitly set direction before calling nextImage\n        const { setCurrentImage } = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const state = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const currentVariant = (_state_productData = state.productData) === null || _state_productData === void 0 ? void 0 : _state_productData.data.variants[state.selectedVariantIndex];\n        const maxIndex = (currentVariant === null || currentVariant === void 0 ? void 0 : currentVariant.images.length) || 0;\n        if (maxIndex > 0) {\n            const nextIndex = (state.currentImageIndex + 1) % maxIndex;\n            // Set direction first, then update image\n            _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.setState({\n                slideDirection: 1\n            });\n            setTimeout(()=>{\n                setCurrentImage(nextIndex);\n            }, 0);\n        }\n    };\n    // Modal image navigation - senkron olarak ana sayfadaki fotoğrafı da değiştirir\n    const handleModalImageChange = (index)=>{\n        setCurrentImage(index);\n    };\n    const handleAddToCart = async ()=>{\n        if (product && selectedVariant && currentImage) {\n            try {\n                // API'ye sepete ekleme isteği gönder (mutation otomatik olarak cache'i yenileyecek)\n                await addToCartMutation.mutateAsync({\n                    productVariantId: selectedVariant.id,\n                    quantity: quantity,\n                    isCustomerPrice: isCustomerPrice\n                });\n                // Başarılı olduğunda modal için gerekli bilgileri hazırla\n                const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n                const membershipDiscount = !isCustomerPrice && discountRate && discountRate > 0 ? discountRate : 0;\n                const extraDiscount = selectedVariant.extraDiscount || 0;\n                const cartItem = {\n                    id: selectedVariant.id,\n                    title: product.name,\n                    price: selectedVariant.price,\n                    discountedPrice: calculateDiscountedPrice(selectedVariant.price, selectedVariant.extraDiscount, !isCustomerPrice),\n                    thumbnail: currentImage.url,\n                    brand: product.brandName,\n                    membershipDiscount: membershipDiscount,\n                    extraDiscount: extraDiscount,\n                    points: calculatePoints(selectedVariant.pv, selectedVariant.price),\n                    quantity: quantity\n                };\n                // Local sepete de ekle (UI için)\n                addToCart(cartItem);\n                // Modal için son eklenen ürünü ayarla ve modalı göster\n                setLastAddedProduct(cartItem);\n                setShowAddToCartModal(true);\n            } catch (error) {\n                console.error('Sepete ekleme sırasında hata:', error);\n            // Hata durumunda kullanıcıya bilgi verilebilir\n            }\n        }\n    };\n    // Favorileme fonksiyonu\n    const handleFavoriteClick = ()=>{\n        if (!product || !selectedVariant || !currentImage) return;\n        // Toplam indirim oranını hesapla\n        const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n        let totalDiscountPercentage = 0;\n        if (discountRate && discountRate > 0) {\n            totalDiscountPercentage += discountRate;\n        }\n        if (selectedVariant.extraDiscount && selectedVariant.extraDiscount > 0) {\n            if (totalDiscountPercentage > 0) {\n                totalDiscountPercentage = totalDiscountPercentage + selectedVariant.extraDiscount - totalDiscountPercentage * selectedVariant.extraDiscount / 100;\n            } else {\n                totalDiscountPercentage = selectedVariant.extraDiscount;\n            }\n        }\n        const favoriteItem = {\n            id: product.id,\n            title: product.name,\n            price: selectedVariant.price,\n            thumbnail: currentImage.url,\n            brand: product.brandName,\n            discountPercentage: totalDiscountPercentage,\n            points: calculatePoints(selectedVariant.pv, selectedVariant.price)\n        };\n        const isCurrentlyFavorite = isFavorite(product.id);\n        if (isCurrentlyFavorite) {\n            removeFromFavorites(product.id);\n            setFavoriteModalIsAdded(false);\n        } else {\n            addToFavorites(favoriteItem);\n            setFavoriteModalIsAdded(true);\n        }\n        setShowFavoriteModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/products\",\n                    className: \"inline-flex items-center text-gray-600 hover:text-purple-600 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 21\n                        }, this),\n                        \"\\xdcr\\xfcnlere D\\xf6n\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        className: \"lg:w-1/2\",\n                        initial: {\n                            opacity: 0,\n                            x: -30\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl overflow-hidden shadow-md mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-96 w-full cursor-pointer\",\n                                    onClick: ()=>setShowImageModal(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.AnimatePresence, {\n                                            mode: \"wait\",\n                                            children: currentImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                                initial: slideDirection !== 0 ? {\n                                                    x: slideDirection > 0 ? 300 : -300,\n                                                    opacity: 0\n                                                } : {\n                                                    opacity: 1\n                                                },\n                                                animate: {\n                                                    x: 0,\n                                                    opacity: 1\n                                                },\n                                                exit: slideDirection !== 0 ? {\n                                                    x: slideDirection > 0 ? -300 : 300,\n                                                    opacity: 0\n                                                } : {\n                                                    opacity: 0\n                                                },\n                                                transition: {\n                                                    type: \"tween\",\n                                                    ease: \"easeOut\",\n                                                    duration: slideDirection !== 0 ? 0.2 : 0,\n                                                    delay: slideDirection !== 0 ? 0.1 : 0\n                                                },\n                                                className: \"absolute inset-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: currentImage.url,\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, currentImageIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 29\n                                        }, this),\n                                        variantImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: (e)=>goToPreviousImage(e),\n                                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: (e)=>goToNextImage(e),\n                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        selectedVariant && selectedVariant.extraDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\",\n                                            children: [\n                                                \"%\",\n                                                selectedVariant.extraDiscount,\n                                                \" İndirim\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 33\n                                        }, this),\n                                        !isCustomerPrice && (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) && discountRateData.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute \".concat(selectedVariant && selectedVariant.extraDiscount > 0 ? 'top-12' : 'top-4', \" right-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\"),\n                                            children: [\n                                                \"%\",\n                                                discountRateData.discountRate,\n                                                \" \\xdcye İndirimi\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 33\n                                        }, this),\n                                        selectedVariant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 flex flex-col space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"PV\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: calculatePoints(selectedVariant.pv, selectedVariant.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"CV\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: calculatePoints(selectedVariant.cv, selectedVariant.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"SP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: calculatePoints(selectedVariant.sp, selectedVariant.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-5 gap-2\",\n                                children: variantImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                        className: \"relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 \".concat((currentImage === null || currentImage === void 0 ? void 0 : currentImage.id) === image.id ? \"border-purple-500\" : \"border-gray-200\"),\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        onClick: ()=>{\n                                            // Determine slide direction based on thumbnail position\n                                            const direction = index > currentImageIndex ? 1 : index < currentImageIndex ? -1 : 0;\n                                            _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.setState({\n                                                slideDirection: direction\n                                            });\n                                            setTimeout(()=>{\n                                                setCurrentImage(index);\n                                            }, 0);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-16 w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: image.url,\n                                                alt: \"\".concat(product.name, \" - \").concat(index + 1),\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, image.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 29\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        className: \"lg:w-1/2\",\n                        initial: {\n                            opacity: 0,\n                            x: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-md p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-800 mb-2\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-2\",\n                                                    children: product.brandName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 29\n                                        }, this),\n                                        product.averageRating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-yellow-50 px-3 py-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 text-yellow-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1 font-medium text-yellow-700\",\n                                                    children: product.averageRating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-100 pb-6 mb-6\",\n                                    children: [\n                                        product.variants && product.variants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-3 text-gray-700\",\n                                                    children: \"Varyant Se\\xe7imi\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 sm:grid-cols-3 gap-3\",\n                                                    children: product.variants.map((variant, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            onClick: ()=>setSelectedVariant(index),\n                                                            className: \"p-3 border-2 rounded-lg text-sm transition-all duration-200 \".concat((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.id) === variant.id ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 hover:border-purple-300'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-left\",\n                                                                children: [\n                                                                    variant.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-600\",\n                                                                            children: [\n                                                                                feature.featureName,\n                                                                                \": \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: feature.featureValue\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 84\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 57\n                                                                        }, this)),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold mt-1\",\n                                                                        children: hasAnyDiscount(variant.extraDiscount, !isCustomerPrice) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.id) === variant.id ? \"text-purple-700\" : \"text-gray-600\",\n                                                                                    children: [\n                                                                                        calculateDiscountedPrice(variant.price, variant.extraDiscount).toFixed(2),\n                                                                                        \" ₺\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-500 line-through text-xs ml-1\",\n                                                                                    children: [\n                                                                                        variant.price.toFixed(2),\n                                                                                        \" ₺\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.id) === variant.id ? \"text-purple-700\" : \"text-gray-600\",\n                                                                            children: [\n                                                                                variant.price.toFixed(2),\n                                                                                \" ₺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs mt-1 \".concat(getStockStatusText(variant.stockStatus).color),\n                                                                        children: getStockStatusText(variant.stockStatus).text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-1 mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: [\n                                                                                    \"PV \",\n                                                                                    calculatePoints(variant.pv, variant.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 57\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: [\n                                                                                    \"CV \",\n                                                                                    calculatePoints(variant.cv, variant.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 542,\n                                                                                columnNumber: 57\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: [\n                                                                                    \"SP \",\n                                                                                    calculatePoints(variant.sp, variant.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 545,\n                                                                                columnNumber: 57\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        }, variant.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 45\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 33\n                                        }, this),\n                                        selectedVariant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-baseline mb-4\",\n                                                    children: hasAnyDiscount(selectedVariant.extraDiscount, !isCustomerPrice) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-purple-700 mr-2\",\n                                                                children: [\n                                                                    calculateDiscountedPrice(selectedVariant.price, selectedVariant.extraDiscount).toFixed(2),\n                                                                    \" ₺\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg text-gray-500 line-through\",\n                                                                children: [\n                                                                    selectedVariant.price.toFixed(2),\n                                                                    \" ₺\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col gap-1 ml-3\",\n                                                                children: [\n                                                                    !isCustomerPrice && (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) && discountRateData.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                                        children: [\n                                                                            \"%\",\n                                                                            discountRateData.discountRate,\n                                                                            \" \\xdcye İndirimi\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    selectedVariant.extraDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                                        children: [\n                                                                            \"%\",\n                                                                            selectedVariant.extraDiscount,\n                                                                            \" İndirim\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-purple-700 mr-2\",\n                                                        children: [\n                                                            selectedVariant.price.toFixed(2),\n                                                            \" ₺\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Bu \\xfcr\\xfcn\\xfc satın alarak kazanacağınız puanlar:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-5 w-5\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"PV\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                calculatePoints(selectedVariant.pv, selectedVariant.price),\n                                                                                \" Puan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-5 w-5\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 601,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"CV\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                calculatePoints(selectedVariant.cv, selectedVariant.price),\n                                                                                \" Puan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-5 w-5\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"SP\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                calculatePoints(selectedVariant.sp, selectedVariant.price),\n                                                                                \" Puan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm flex items-center \".concat(getStockStatusText(selectedVariant.stockStatus).color),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5 mr-1\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                getStockStatusText(selectedVariant.stockStatus).text\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm flex items-center text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5 mr-1\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"Kategori: \",\n                                                                product.categoryName\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-center gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center border border-gray-200 rounded-md text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: decreaseQuantity,\n                                                        className: \"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 text-center\",\n                                                        children: quantity\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: increaseQuantity,\n                                                        className: \"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none\",\n                                                        children: \"+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.02\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                onClick: handleAddToCart,\n                                                className: \"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5 mr-2\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \"Sepete Ekle\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                onClick: handleFavoriteClick,\n                                                className: \"w-12 h-12 flex items-center justify-center border rounded-full transition-colors \".concat(isFavorite(product.id) ? 'border-red-500 text-red-500 bg-red-50' : 'border-gray-200 text-gray-400 hover:text-red-500 hover:border-red-500'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-6 w-6\",\n                                                    fill: isFavorite(product.id) ? 'currentColor' : 'none',\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-3 text-gray-700\",\n                                            children: \"\\xdcr\\xfcn A\\xe7ıklaması\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddToCartModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: showAddToCartModal,\n                onClose: ()=>setShowAddToCartModal(false),\n                product: lastAddedProduct,\n                quantity: quantity\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 739,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FavoriteModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showFavoriteModal,\n                onClose: ()=>setShowFavoriteModal(false),\n                product: selectedVariant && currentImage ? {\n                    id: product.id,\n                    title: product.name,\n                    price: selectedVariant.price,\n                    thumbnail: currentImage.url,\n                    brand: product.brandName,\n                    discountPercentage: selectedVariant.extraDiscount,\n                    points: calculatePoints(selectedVariant.pv, selectedVariant.price)\n                } : null,\n                isAdded: favoriteModalIsAdded\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 747,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showImageModal,\n                onClose: ()=>setShowImageModal(false),\n                images: variantImages,\n                currentImageIndex: variantImages.findIndex((img)=>img.id === (currentImage === null || currentImage === void 0 ? void 0 : currentImage.id)),\n                onImageChange: handleModalImageChange,\n                productName: product.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 763,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 307,\n        columnNumber: 9\n    }, this);\n}\n_s(ProductDetailPage, \"T23K+/DdSK8IK6O8thctjWU3bAg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_5__.useCart,\n        _contexts_FavoritesContext__WEBPACK_IMPORTED_MODULE_6__.useFavorites,\n        _hooks_useDiscountRate__WEBPACK_IMPORTED_MODULE_11__.useDiscountRate,\n        _hooks_useCart__WEBPACK_IMPORTED_MODULE_12__.useAddToCart,\n        _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_14__.useCustomerPriceStore,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSelectedVariant,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImage,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useVariantImages,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductQuantity,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSlideDirection,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImageIndex,\n        _hooks_useCatalogProducts__WEBPACK_IMPORTED_MODULE_10__.useCatalogProductDetail\n    ];\n});\n_c = ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/product/[id]/page.tsx\n"));

/***/ })

});