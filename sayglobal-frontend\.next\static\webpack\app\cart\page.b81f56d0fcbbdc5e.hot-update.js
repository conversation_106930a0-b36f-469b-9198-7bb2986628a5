"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cart/page",{

/***/ "(app-pages-browser)/./src/hooks/useCart.ts":
/*!******************************!*\
  !*** ./src/hooks/useCart.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDiscountedPrice: () => (/* binding */ calculateDiscountedPrice),\n/* harmony export */   calculatePoints: () => (/* binding */ calculatePoints),\n/* harmony export */   useCartCount: () => (/* binding */ useCartCount),\n/* harmony export */   useCartItems: () => (/* binding */ useCartItems),\n/* harmony export */   useDiscountRate: () => (/* binding */ useDiscountRate),\n/* harmony export */   useRemoveFromCart: () => (/* binding */ useRemoveFromCart),\n/* harmony export */   useUpdateCartQuantity: () => (/* binding */ useUpdateCartQuantity),\n/* harmony export */   useUpdateCartType: () => (/* binding */ useUpdateCartType)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n\n\n// Sepet içeriklerini getir\nconst useCartItems = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            'cartItems'\n        ],\n        queryFn: {\n            \"useCartItems.useQuery\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.getCartItems();\n                if (response.success) {\n                    return response.data.data; // API response'u data wrapper'ı içinde geliyor\n                }\n                throw new Error(response.error || 'Sepet içerikleri alınamadı');\n            }\n        }[\"useCartItems.useQuery\"],\n        staleTime: 30 * 1000,\n        refetchOnWindowFocus: true,\n        refetchOnMount: true\n    });\n};\n// Sepetteki ürün sayısını getir\nconst useCartCount = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            'cartCount'\n        ],\n        queryFn: {\n            \"useCartCount.useQuery\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.getCartCount();\n                if (response.success) {\n                    return response.data.data; // API response'u data wrapper'ı içinde geliyor\n                }\n                throw new Error(response.error || 'Sepet ürün sayısı alınamadı');\n            }\n        }[\"useCartCount.useQuery\"],\n        staleTime: 30 * 1000,\n        refetchOnWindowFocus: true,\n        refetchOnMount: true\n    });\n};\n// İndirim oranını getir\nconst useDiscountRate = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            'discountRate'\n        ],\n        queryFn: {\n            \"useDiscountRate.useQuery\": async ()=>{\n                try {\n                    const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.getDiscountRate();\n                    console.log('🔍 Discount Rate API Response:', response);\n                    if (response.success) {\n                        // API'den direkt {discountRate: 10} geliyor\n                        return response.data || {\n                            discountRate: 0\n                        };\n                    }\n                    // Hata durumunda 0 döndür, throw etme\n                    console.warn('İndirim oranı alınamadı:', response.error);\n                    return {\n                        discountRate: 0\n                    };\n                } catch (error) {\n                    // Network hatası vs. durumunda da 0 döndür\n                    console.warn('İndirim oranı alınırken hata:', error);\n                    return {\n                        discountRate: 0\n                    };\n                }\n            }\n        }[\"useDiscountRate.useQuery\"],\n        staleTime: 5 * 60 * 1000,\n        refetchOnWindowFocus: false,\n        refetchOnMount: true,\n        retry: false\n    });\n};\n// Sepetten ürün çıkarma mutation'ı\nconst useRemoveFromCart = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: {\n            \"useRemoveFromCart.useMutation\": async (productVariantId)=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.removeFromCart(productVariantId);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün sepetten çıkarılamadı');\n                }\n                return response.data;\n            }\n        }[\"useRemoveFromCart.useMutation\"],\n        onSuccess: {\n            \"useRemoveFromCart.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartCount'\n                    ]\n                });\n            }\n        }[\"useRemoveFromCart.useMutation\"],\n        onError: {\n            \"useRemoveFromCart.useMutation\": (error)=>{\n                console.error('Sepetten ürün çıkarma hatası:', error);\n            }\n        }[\"useRemoveFromCart.useMutation\"]\n    });\n};\n// Sepet ürün miktarını güncelleme mutation'ı\nconst useUpdateCartQuantity = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: {\n            \"useUpdateCartQuantity.useMutation\": async (param)=>{\n                let { productVariantId, quantity } = param;\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.updateCartQuantity(productVariantId, quantity);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün miktarı güncellenemedi');\n                }\n                return response.data;\n            }\n        }[\"useUpdateCartQuantity.useMutation\"],\n        onSuccess: {\n            \"useUpdateCartQuantity.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n            }\n        }[\"useUpdateCartQuantity.useMutation\"],\n        onError: {\n            \"useUpdateCartQuantity.useMutation\": (error)=>{\n                console.error('Sepet ürün miktarı güncelleme hatası:', error);\n            }\n        }[\"useUpdateCartQuantity.useMutation\"]\n    });\n};\n// Sepet tipini güncelleme mutation'ı (customer price toggle)\nconst useUpdateCartType = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: {\n            \"useUpdateCartType.useMutation\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.updateCartType();\n                if (!response.success) {\n                    throw new Error(response.error || 'Sepet tipi güncellenemedi');\n                }\n                return response.data;\n            }\n        }[\"useUpdateCartType.useMutation\"],\n        onSuccess: {\n            \"useUpdateCartType.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n            }\n        }[\"useUpdateCartType.useMutation\"],\n        onError: {\n            \"useUpdateCartType.useMutation\": (error)=>{\n                console.error('Sepet tipi güncelleme hatası:', error);\n            }\n        }[\"useUpdateCartType.useMutation\"]\n    });\n};\n// Puan hesaplama fonksiyonu\nconst calculatePoints = (ratio, price)=>{\n    return Math.round(ratio / 100 * price);\n};\n// Fiyat hesaplama fonksiyonu (indirim dahil)\nconst calculateDiscountedPrice = (originalPrice, discountRate, isCustomerPrice)=>{\n    if (isCustomerPrice || !discountRate || discountRate <= 0) {\n        return originalPrice;\n    }\n    return originalPrice * (1 - discountRate / 100);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useCart.ts\n"));

/***/ })

});