'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronRight, Check, Zap } from 'lucide-react';
import {
    Brand,
    Category,
    SubCategory,
    SubCategoryFeature,
    FeatureValue,
    VariantFormData,
    VariantFeatureDetail
} from '@/types';
import { productService } from '@/services/api';

interface ProductCategorySelectorProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (data: {
        brandId: number;
        categoryId: number;
        subCategoryId: number;
        brandName: string;
        categoryName: string;
        subCategoryName: string;
        generatedVariants: VariantFormData[];
        selectedFeatures: { [key: number]: number[] };
        selectedFeatureDetails: { featureName: string; featureValue: string }[];
    }) => void;
    initialData?: {
        brandId: number;
        categoryId: number;
        subCategoryId: number;
        selectedFeatures: { [key: number]: number[] };
    };
    colorScheme?: 'red' | 'blue'; // Renk şeması: admin iç<PERSON>ı<PERSON>z<PERSON>, satıcı için mavi
}

const ProductCategorySelector: React.FC<ProductCategorySelectorProps> = ({
    isOpen,
    onClose,
    onSelect,
    initialData,
    colorScheme = 'red'
}) => {
    const [step, setStep] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Renk sınıfları
    const colors = {
        primary: colorScheme === 'blue' ? 'blue' : 'red',
        spinner: colorScheme === 'blue' ? 'border-blue-600' : 'border-red-600',
        button: colorScheme === 'blue' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700',
        selected: colorScheme === 'blue' ? 'border-blue-500 bg-blue-50' : 'border-red-500 bg-red-50',
        checkButton: colorScheme === 'blue' ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-red-500 bg-red-50 text-red-700'
    };
    const [brands, setBrands] = useState<Brand[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [subCategories, setSubCategories] = useState<SubCategory[]>([]);
    const [subCategoryFeatures, setSubCategoryFeatures] = useState<SubCategoryFeature[]>([]);
    const [featureValues, setFeatureValues] = useState<{ [key: number]: FeatureValue[] }>({});
    const [selectedBrandId, setSelectedBrandId] = useState<number>(0);
    const [selectedCategoryId, setSelectedCategoryId] = useState<number>(0);
    const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<number>(0);
    const [selectedFeatures, setSelectedFeatures] = useState<{ [key: number]: number[] }>({});
    const [selectedBrandName, setSelectedBrandName] = useState<string>('');
    const [selectedCategoryName, setSelectedCategoryName] = useState<string>('');
    const [selectedSubCategoryName, setSelectedSubCategoryName] = useState<string>('');

    const handleCloseCallback = React.useCallback((isInitialReset = false) => {
        setStep(1);
        setSelectedBrandId(0);
        setSelectedCategoryId(0);
        setSelectedSubCategoryId(0);
        setSelectedFeatures({});
        setError(null);
        if (!isInitialReset) onClose();
    }, [onClose]);

    const loadBrandsCallback = React.useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await productService.getBrands();
            if (response.success) {
                setBrands(response.data);
            } else {
                setError(response.error || 'Markalar yüklenirken bir hata oluştu');
            }
        } catch {
            setError('Markalar yüklenirken bir hata oluştu');
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        if (isOpen) {
            const hasInitialData = initialData && initialData.brandId > 0;
            if (hasInitialData) {
                setSelectedBrandId(initialData.brandId);
                setSelectedCategoryId(initialData.categoryId);
                setSelectedSubCategoryId(initialData.subCategoryId);
                setSelectedFeatures(initialData.selectedFeatures || {});
                console.log('🎯 ProductCategorySelector initialData.selectedFeatures:', initialData.selectedFeatures);
                if (initialData.subCategoryId > 0) {
                    setStep(4);
                    // `selectedSubCategoryId` state'i değiştiğinde useEffect tetiklenecek
                    // loadSubCategoryFeatures(initialData.subCategoryId); // Bu satır kaldırıldı
                }
                else if (initialData.categoryId > 0) setStep(3);
                else setStep(2);
            } else {
                handleCloseCallback(true);
            }
            loadBrandsCallback();
        }
    }, [isOpen, initialData, handleCloseCallback, loadBrandsCallback]);

    const loadSubCategoryFeaturesCallback = React.useCallback(async (subCategoryId: number) => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await productService.getSubCategoryFeatures(subCategoryId);
            if (response.success) {
                setSubCategoryFeatures(response.data);
                // Her bir özellik tanımı için değerleri yükle
                response.data.forEach((feature: SubCategoryFeature) => {
                    loadFeatureValues(feature.featureDefinitionId);
                });
            }
        } catch (error) {
            console.error('Özellik tanımları yüklenirken hata:', error);
            setError('Özellik tanımları yüklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
        }
    }, []);

    const loadCategoriesCallback = React.useCallback(async (brandId: number) => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await productService.getCategoriesByBrand(brandId);
            if (response.success) {
                setCategories(response.data);
            } else {
                setError(response.error || 'Kategoriler yüklenirken bir hata oluştu');
            }
        } catch {
            setError('Kategoriler yüklenirken bir hata oluştu');
        } finally {
            setIsLoading(false);
        }
    }, []);

    const loadSubCategoriesCallback = React.useCallback(async (categoryId: number) => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await productService.getSubCategories(categoryId);
            if (response.success) {
                setSubCategories(response.data);
            } else {
                setError(response.error || 'Alt kategoriler yüklenirken bir hata oluştu');
            }
        } catch {
            setError('Alt kategoriler yüklenirken bir hata oluştu');
        } finally {
            setIsLoading(false);
        }
    }, []);

    // selectedSubCategoryId değiştiğinde subCategoryFeatures ve featureValues'ı yükle
    useEffect(() => {
        if (selectedSubCategoryId > 0) {
            loadSubCategoryFeaturesCallback(selectedSubCategoryId);
        }
    }, [selectedSubCategoryId, loadSubCategoryFeaturesCallback]);

    useEffect(() => {
        if (selectedBrandId > 0) loadCategoriesCallback(selectedBrandId);
    }, [selectedBrandId, loadCategoriesCallback]);

    useEffect(() => {
        if (selectedCategoryId > 0) loadSubCategoriesCallback(selectedCategoryId);
    }, [selectedCategoryId, loadSubCategoriesCallback]);

    useEffect(() => {
        if (isOpen) {
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        }
        return () => {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        };
    }, [isOpen]);



    const loadFeatureValues = async (definitionId: number) => {
        try {
            const response = await productService.getFeatureValues(definitionId);
            if (response.success) {
                setFeatureValues(prev => ({ ...prev, [definitionId]: response.data }));
            }
        } catch (error) {
            console.error('Özellik değerleri yüklenirken hata:', error);
        }
    };

    const handleBrandSelect = (brandId: number) => {
        const brand = brands.find(b => b.id === brandId);
        setSelectedBrandId(brandId);
        setSelectedBrandName(brand?.name || '');

        // Eğer initialData'dan gelen aynı marka seçiliyorsa, kategori, alt kategori ve selectedFeatures'ı koru
        // Aksi halde temizle
        const isInitialBrand = initialData && initialData.brandId === brandId;
        if (!isInitialBrand) {
            setSelectedCategoryId(0);
            setSelectedSubCategoryId(0);
            setSelectedFeatures({});
        }

        setStep(2);
    };

    const handleCategorySelect = (categoryId: number) => {
        const category = categories.find(c => c.id === categoryId);
        setSelectedCategoryId(categoryId);
        setSelectedCategoryName(category?.name || '');

        // Eğer initialData'dan gelen aynı kategori seçiliyorsa, alt kategori ve selectedFeatures'ı koru
        // Aksi halde temizle
        const isInitialCategory = initialData && initialData.categoryId === categoryId;
        if (!isInitialCategory) {
            setSelectedSubCategoryId(0);
            setSelectedFeatures({});
        }

        setStep(3);
    };

    const handleSubCategorySelect = async (subCategoryId: number) => {
        const subCategory = subCategories.find(sc => sc.id === subCategoryId);
        setSelectedSubCategoryId(subCategoryId);
        setSelectedSubCategoryName(subCategory?.name || '');

        // Eğer initialData'dan gelen aynı alt kategori seçiliyorsa, selectedFeatures'ı koru
        // Aksi halde temizle
        const isInitialSubCategory = initialData && initialData.subCategoryId === subCategoryId;
        if (!isInitialSubCategory) {
            setSelectedFeatures({});
        }

        setError(null); // Önceki hataları temizle

        setIsLoading(true); // Yükleme durumunu başlat
        const response = await productService.getSubCategoryFeatures(subCategoryId);
        setIsLoading(false); // Yükleme durumunu bitir

        if (response.success && response.data.length > 0) {
            setSubCategoryFeatures(response.data);
            response.data.forEach((feature: SubCategoryFeature) => {
                loadFeatureValues(feature.featureDefinitionId);
            });
            setStep(4);
        } else if (response.success && response.data.length === 0) {
            setError('Bu alt kategoriye ait özellik bulunmamaktadır.');
            setSubCategoryFeatures([]); // Özellikleri temizle
            setFeatureValues({}); // Özellik değerlerini de temizle
            setStep(3); // Adımı 3'te tut
        } else {
            setError(response.error || 'Özellikler yüklenirken bir hata oluştu.');
            setSubCategoryFeatures([]);
            setFeatureValues({});
            setStep(3); // Adımı 3'te tut
        }
    };

    const handleFeatureSelect = (featureDefId: number, featureValueId: number) => {
        setSelectedFeatures(prev => {
            const current = prev[featureDefId] || [];
            const isSelected = current.includes(featureValueId);
            return { ...prev, [featureDefId]: isSelected ? current.filter(id => id !== featureValueId) : [...current, featureValueId] };
        });
    };

    const generateVariantCombinations = (): VariantFormData[] => {
        const selectedFeatureEntries = Object.entries(selectedFeatures).filter(([, values]) => values.length > 0);

        if (selectedFeatureEntries.length === 0) {
            return [{
                id: Date.now(), name: 'Default', selectedFeatures: {}, featureDetails: [], features: [],
                pricing: { price: 0, stock: 0, extraDiscount: 0, ratios: { pvRatio: 0, cvRatio: 0, spRatio: 0 }, points: { pv: 0, cv: 0, sp: 0 } },
                images: [], isActive: true
            }];
        }

        type FeatureValuePair = { featureDefinitionId: number; valueId: number };

        const cartesian = (arrays: FeatureValuePair[][]): FeatureValuePair[][] => {
            return arrays.reduce((acc, val) =>
                acc.flatMap(d => val.map(e => [...d, e])),
                [[]] as FeatureValuePair[][]
            );
        };

        const featureValueArrays = selectedFeatureEntries.map(([featureDefId, valueIds]) =>
            valueIds.map(valueId => ({ featureDefinitionId: parseInt(featureDefId), valueId }))
        );

        const combinations = cartesian(featureValueArrays);

        return combinations.map((combo, index) => {
            const featureDetails: VariantFeatureDetail[] = combo.map(c => {
                const feature = subCategoryFeatures.find(f => f.featureDefinitionId === c.featureDefinitionId);
                const value = featureValues[c.featureDefinitionId]?.find(v => v.id === c.valueId);
                return {
                    featureDefinitionId: c.featureDefinitionId,
                    featureValueId: c.valueId,
                    featureName: feature?.featureDefinition?.name || '',
                    featureValue: value?.value || ''
                };
            });

            const variantName = featureDetails.map(f => f.featureValue).join(' - ');
            const selectedFeaturesMap: { [key: number]: number[] } = {};
            featureDetails.forEach(detail => {
                selectedFeaturesMap[detail.featureDefinitionId] = [detail.featureValueId];
            });

            return {
                id: Date.now() + index,
                name: variantName,
                selectedFeatures: selectedFeaturesMap,
                featureDetails,
                features: featureDetails,
                pricing: {
                    price: 0,
                    stock: 0,
                    extraDiscount: 0,
                    ratios: { pvRatio: 0, cvRatio: 0, spRatio: 0 },
                    points: { pv: 0, cv: 0, sp: 0 }
                },
                images: [],
                isActive: true
            };
        });
    };

    const handleComplete = () => {
        const generatedVariants = generateVariantCombinations();

        // selectedFeatureDetails'ı oluştur
        const selectedFeatureDetails: { featureName: string; featureValue: string }[] = [];
        Object.entries(selectedFeatures).forEach(([featureDefId, valueIds]) => {
            const feature = subCategoryFeatures.find(f => f.featureDefinitionId === parseInt(featureDefId));
            valueIds.forEach(valueId => {
                const value = featureValues[parseInt(featureDefId)]?.find(v => v.id === valueId);
                if (feature && value) {
                    selectedFeatureDetails.push({
                        featureName: feature.featureDefinition?.name || '',
                        featureValue: value.value
                    });
                }
            });
        });

        onSelect({
            brandId: selectedBrandId,
            categoryId: selectedCategoryId,
            subCategoryId: selectedSubCategoryId,
            brandName: selectedBrandName,
            categoryName: selectedCategoryName,
            subCategoryName: selectedSubCategoryName,
            generatedVariants,
            selectedFeatures,
            selectedFeatureDetails
        });
        onClose();
    };



    const getStepTitle = () => step === 1 ? 'Marka Seçin' : step === 2 ? 'Kategori Seçin' : step === 3 ? 'Alt Kategori Seçin' : 'Varyant Oluştur';

    const totalCombinations = (() => {
        const counts = Object.values(selectedFeatures).map(v => v.length).filter(l => l > 0);
        if (counts.length === 0) return 1;
        return counts.reduce((acc, count) => acc * count, 1);
    })();

    const renderStepContent = () => {
        if (isLoading) return <div className="flex justify-center items-center p-12"><div className={`animate-spin rounded-full h-8 w-8 border-b-2 ${colors.spinner}`}></div></div>;

        const renderList = <T extends { id: number; name: string }>(items: T[], selectedId: number, handler: (id: number) => void, nameKey: keyof T = 'name') => (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {items.map(item => (
                    <button key={item.id} onClick={() => handler(item.id)} className={`p-4 border rounded-lg text-left hover:bg-gray-50 transition-colors text-black ${selectedId === item.id ? colors.selected : 'border-gray-200'}`}>
                        <div className="flex items-center justify-between">
                            <h3 className="font-medium text-gray-900">{String(item[nameKey])}</h3>
                            <ChevronRight className="h-5 w-5 text-gray-400" />
                        </div>
                    </button>
                ))}
            </div>
        );

        switch (step) {
            case 1: return renderList(brands, selectedBrandId, handleBrandSelect);
            case 2: return renderList(categories, selectedCategoryId, handleCategorySelect);
            case 3: return renderList(subCategories, selectedSubCategoryId, handleSubCategorySelect);
            case 4: {
                return (
                    <div className="space-y-6">
                        {/* Talimatlar Bölümü */}
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-start space-x-3">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-sm font-medium text-blue-900 mb-2">Varyant Oluşturma Talimatları</h3>
                                    <div className="text-sm text-blue-800 space-y-1">
                                        <p>• <strong>Her özellik grubundan</strong> istediğiniz kadar seçenek seçebilirsiniz</p>
                                        <p>• <strong>Seçtiğiniz tüm kombinasyonlar</strong> otomatik olarak varyant haline gelecek</p>
                                        <p>• <strong>Örnek:</strong> 2 renk + 3 beden = 6 farklı varyant oluşur</p>
                                        <p>• <strong>Hiçbir özellik seçmezseniz</strong> tek bir varsayılan varyant oluşturulur</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Özellik Seçimi */}
                        {subCategoryFeatures.map(feature => (
                            <div key={feature.id} className="border rounded-lg p-4">
                                <div className="flex items-center justify-between mb-3">
                                    <h3 className="font-medium text-gray-900">{feature.featureDefinition?.name || 'Özellik'}</h3>
                                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                        {selectedFeatures[feature.featureDefinitionId]?.length || 0} seçili
                                    </span>
                                </div>
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                    {featureValues[feature.featureDefinitionId]?.map(value => (
                                        <button
                                            key={value.id}
                                            onClick={() => handleFeatureSelect(feature.featureDefinitionId, value.id)}
                                            className={`p-2 border rounded text-sm transition-colors flex items-center justify-between ${selectedFeatures[feature.featureDefinitionId]?.includes(value.id)
                                                ? colors.checkButton
                                                : 'border-gray-200 hover:bg-gray-50 text-black'
                                                }`}
                                        >
                                            <span>{value.value}</span>
                                            {selectedFeatures[feature.featureDefinitionId]?.includes(value.id) &&
                                                <Check className="h-4 w-4" />
                                            }
                                        </button>
                                    ))}
                                </div>
                            </div>
                        ))}

                        {/* Varyant Önizleme */}
                        {Object.values(selectedFeatures).some(arr => arr.length > 0) && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h3 className="text-sm font-medium text-green-900 mb-2">Oluşturulacak Varyantlar Önizlemesi</h3>
                                <div className="text-sm text-green-800">
                                    <p className="mb-2">
                                        <strong>{totalCombinations} adet varyant</strong> oluşturulacak:
                                    </p>
                                    <div className="space-y-1">
                                        {Object.entries(selectedFeatures)
                                            .filter(([, values]) => values.length > 0)
                                            .map(([featureDefId, valueIds]) => {
                                                const feature = subCategoryFeatures.find(f => f.featureDefinitionId === parseInt(featureDefId));
                                                const selectedValues = valueIds.map(valueId =>
                                                    featureValues[parseInt(featureDefId)]?.find(v => v.id === valueId)?.value
                                                ).filter(Boolean);
                                                return (
                                                    <p key={featureDefId}>
                                                        <strong>{feature?.featureDefinition?.name || 'Özellik'}:</strong> {selectedValues.join(', ')}
                                                    </p>
                                                );
                                            })
                                        }
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                );
            }
            default: return null;
        }
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
                    <motion.div className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col" initial={{ opacity: 0, scale: 0.95 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.95 }}>
                        <div className="flex items-center justify-between p-6 border-b border-gray-200">
                            <h2 className="text-2xl font-semibold text-gray-900">{getStepTitle()}</h2>
                            <button onClick={() => handleCloseCallback()} className="text-gray-400 hover:text-gray-600"><X className="h-6 w-6" /></button>
                        </div>

                        {error && <div className="p-4 bg-red-50 border-b border-red-200"><p className="text-red-800 text-sm">{error}</p></div>}

                        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">{renderStepContent()}</div>

                        <div className="p-6 border-t border-gray-200 mt-auto bg-gray-50">
                            <div className="flex items-center justify-between">
                                <div>
                                    {step > 1 && <button onClick={() => setStep(step - 1)} className="px-4 py-2 border border-gray-300 rounded-lg text-black hover:bg-gray-50">Geri</button>}
                                </div>
                                <div className="flex items-center space-x-3">
                                    {step === 4 && (
                                        <div className="flex items-center text-sm text-blue-600 font-medium">
                                            <Zap className="h-5 w-5 mr-2" />
                                            {Object.values(selectedFeatures).every(arr => arr.length === 0)
                                                ? <span>Varyant seçilmedi</span>
                                                : <span>{totalCombinations} varyant oluşturulacak</span>
                                            }
                                        </div>
                                    )}
                                    <button onClick={() => handleCloseCallback()} className="px-4 py-2 border border-gray-300 rounded-lg text-black hover:bg-gray-50">İptal</button>
                                    {step === 4 ? (
                                        <button
                                            onClick={handleComplete}
                                            disabled={Object.values(selectedFeatures).every(arr => arr.length === 0)}
                                            className={`px-4 py-2 ${colors.button} text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed`}
                                        >
                                            Seçimi Tamamla
                                        </button>
                                    ) : (
                                        <button onClick={() => setStep(step + 1)} disabled={step === 1 ? !selectedBrandId : step === 2 ? !selectedCategoryId : !selectedSubCategoryId} className={`px-4 py-2 ${colors.button} text-white rounded-lg disabled:opacity-50`}>Devam Et</button>
                                    )}
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default ProductCategorySelector; 