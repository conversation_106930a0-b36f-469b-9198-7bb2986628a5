'use client';

import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useBankingModal, useBankingData, useModalActions } from '@/stores/modalStore';

// 🏦 Banking Modal - Modal Store entegrasyonu ile
export default function BankingModal() {
    // 🏪 Modal Store hooks
    const isOpen = useBankingModal();
    const bankingInfo = useBankingData();
    const { closeBankingModal } = useModalActions();
    const [formData, setFormData] = useState({
        accountHolderName: '',
        bankName: '',
        iban: '',
        branchCode: '',
        branchName: ''
    });

    // 🚀 Stabilize bankingInfo to prevent infinite loops
    const stableBankingInfo = useMemo(() => {
        if (!bankingInfo) return null;
        return {
            accountHolderName: bankingInfo.accountHolderName || '',
            bankName: bankingInfo.bankName || '',
            iban: bankingInfo.iban || '',
            branchCode: bankingInfo.branchCode || '',
            branchName: bankingInfo.branchName || ''
        };
    }, [
        bankingInfo?.accountHolderName,
        bankingInfo?.bankName,
        bankingInfo?.iban,
        bankingInfo?.branchCode,
        bankingInfo?.branchName
    ]);

    // 🔄 Only update form when stable banking info changes
    useEffect(() => {
        if (stableBankingInfo) {
            setFormData(stableBankingInfo);
        }
    }, [stableBankingInfo]);

    // Modal açıkken body scroll'unu engelle ve titreme önle
    useEffect(() => {
        if (isOpen) {
            // Scrollbar genişliğini hesapla
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

            // Body'yi kilitle ve scrollbar genişliği kadar padding ekle
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            // Normal duruma döndür
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        }

        // Cleanup function - component unmount olduğunda
        return () => {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        };
    }, [isOpen]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Burada API çağrısı yapılacak
        console.log('IBAN Bilgileri:', formData);
        closeBankingModal();
    };

    const formatIban = (value: string) => {
        // IBAN formatını düzenle
        const cleaned = value.replace(/\s/g, '').toUpperCase();
        if (cleaned.startsWith('TR')) {
            return cleaned.replace(/(.{4})/g, '$1 ').trim();
        }
        return cleaned;
    };

    const handleIbanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const formatted = formatIban(e.target.value);
        setFormData(prev => ({
            ...prev,
            iban: formatted
        }));
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <>
                    {/* Backdrop */}
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={closeBankingModal}
                        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
                    />

                    {/* Modal */}
                    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.95, y: 20 }}
                            className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
                        >
                            {/* Header */}
                            <div className="flex items-center justify-between p-6 border-b border-gray-200">
                                <h2 className="text-xl font-semibold text-gray-800">
                                    IBAN Bilgilerini Düzenle
                                </h2>
                                <motion.button
                                    onClick={closeBankingModal}
                                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </motion.button>
                            </div>

                            {/* Form */}
                            <form onSubmit={handleSubmit} className="p-6 space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="accountHolderName" className="block text-sm font-medium text-gray-700 mb-2">
                                            Hesap Sahibi Adı <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            id="accountHolderName"
                                            name="accountHolderName"
                                            value={formData.accountHolderName}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                            placeholder="Hesap sahibinin tam adını girin"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-2">
                                            Banka Adı <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            id="bankName"
                                            name="bankName"
                                            value={formData.bankName}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                            required
                                        >
                                            <option value="">Banka seçiniz</option>
                                            <option value="akbank">Akbank</option>
                                            <option value="garanti">Garanti BBVA</option>
                                            <option value="isbank">Türkiye İş Bankası</option>
                                            <option value="yapi-kredi">Yapı Kredi</option>
                                            <option value="halkbank">Halkbank</option>
                                            <option value="ziraat">Ziraat Bankası</option>
                                            <option value="vakifbank">VakıfBank</option>
                                            <option value="teb">TEB</option>
                                            <option value="enpara">Enpara.com</option>
                                            <option value="denizbank">DenizBank</option>
                                            <option value="other">Diğer</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="iban" className="block text-sm font-medium text-gray-700 mb-2">
                                        IBAN <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="iban"
                                        name="iban"
                                        value={formData.iban}
                                        onChange={handleIbanChange}
                                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono"
                                        placeholder="TR00 0000 0000 0000 0000 0000 00"
                                        maxLength={32}
                                        required
                                    />
                                    <p className="text-xs text-gray-500 mt-1">IBAN numaranızı TR ile başlayarak girin</p>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="branchCode" className="block text-sm font-medium text-gray-700 mb-2">
                                            Şube Kodu
                                        </label>
                                        <input
                                            type="text"
                                            id="branchCode"
                                            name="branchCode"
                                            value={formData.branchCode}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                            placeholder="Şube kodunu girin (ör: 444)"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="branchName" className="block text-sm font-medium text-gray-700 mb-2">
                                            Şube Adı
                                        </label>
                                        <input
                                            type="text"
                                            id="branchName"
                                            name="branchName"
                                            value={formData.branchName}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                            placeholder="Şube adını girin"
                                        />
                                    </div>
                                </div>

                                {/* Güvenlik Uyarısı */}
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div className="flex">
                                        <div className="flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-blue-800">
                                                Güvenlik Bilgilendirmesi
                                            </h3>
                                            <div className="mt-2 text-sm text-blue-700">
                                                <p>IBAN bilgileriniz sadece size ödeme yapmak için kullanılır ve güvenli şekilde saklanır.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Buttons */}
                                <div className="flex justify-end space-x-3 pt-4">
                                    <motion.button
                                        type="button"
                                        onClick={closeBankingModal}
                                        className="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        İptal
                                    </motion.button>
                                    <motion.button
                                        type="submit"
                                        className="px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg font-medium hover:shadow-lg transition duration-300"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        Bilgileri Kaydet
                                    </motion.button>
                                </div>
                            </form>
                        </motion.div>
                    </div>
                </>
            )}
        </AnimatePresence>
    );
} 