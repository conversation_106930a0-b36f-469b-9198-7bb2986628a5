'use client';

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { useAuth } from "@/components/auth/AuthContext";
import { authService, UpdateProfileRequest } from "@/services/authService";
import { imageService, ImageProcessingResult } from "@/services/imageService";
import ProfilePictureMenu from "@/components/ProfilePictureMenu";
import { GenderType } from "@/types";
import { useQueryClient } from "@tanstack/react-query";
import { authKeys, useProfileInfo } from "@/hooks/useAuth";

export default function EditProfilePage() {
    const router = useRouter();
    const { user, isLoading } = useAuth();
    const { data: profileInfo, isLoading: isProfileLoading, refetch: refetchProfile } = useProfileInfo();
    const queryClient = useQueryClient();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        phone: '',
        location: '',
        birthDate: '',
        gender: ''
    });

    const [originalData, setOriginalData] = useState({
        firstName: '',
        lastName: '',
        phone: '',
        location: '',
        birthDate: '',
        gender: ''
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [previewImage, setPreviewImage] = useState<string | null>(null);
    const [isProcessingImage, setIsProcessingImage] = useState(false);
    const [imageProcessingResult, setImageProcessingResult] = useState<ImageProcessingResult | null>(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
    const [isProfilePictureMarkedForDeletion, setIsProfilePictureMarkedForDeletion] = useState(false);

    useEffect(() => {
        // Giriş yapmamış kullanıcıyı login sayfasına yönlendir
        if (!isLoading && !user) {
            router.push('/login');
            return;
        }

        // ProfileInfo'dan form verilerini doldur
        if (profileInfo) {
            const userData = {
                firstName: profileInfo.firstName || '',
                lastName: profileInfo.lastName || '',
                phone: user?.phoneNumber || '',
                location: profileInfo.location || '',
                birthDate: profileInfo.dateOfBirth ? profileInfo.dateOfBirth.split('T')[0] : '',
                gender: profileInfo.gender !== undefined && profileInfo.gender !== null ?
                    (profileInfo.gender === 1 ? 'erkek' :
                        profileInfo.gender === 2 ? 'kadın' :
                            profileInfo.gender === 3 ? 'diğer' : '') : ''
            };
            setFormData(userData);
            setOriginalData({ ...userData }); // Kopya oluştur
        }
    }, [user, isLoading, profileInfo, router]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Hatayı temizle
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Menu functions
    const handleProfilePictureMenuToggle = () => {
        setIsProfileMenuOpen(!isProfileMenuOpen);
    };

    const handleProfilePictureMenuClose = () => {
        setIsProfileMenuOpen(false);
    };

    const handleImageSelect = async (file: File) => {
        try {
            // Dosya validasyonu
            const validation = imageService.validateImageFile(file, 10);
            if (!validation.isValid) {
                setErrors(prev => ({
                    ...prev,
                    image: validation.error!
                }));
                return;
            }

            // Hata varsa temizle
            if (errors.image) {
                setErrors(prev => ({
                    ...prev,
                    image: ''
                }));
            }

            setIsProcessingImage(true);

            // WebP'ye dönüştür (profil fotoğrafı için optimize edilmiş)
            const result = await imageService.convertProfilePictureToWebP(file);

            setSelectedImage(result.file);
            setImageProcessingResult(result);

            // Preview için URL oluştur
            const previewUrl = await imageService.createPreviewUrl(result.file);
            setPreviewImage(previewUrl);

        } catch (error: any) {
            setErrors(prev => ({
                ...prev,
                image: error.message || 'Resim işlenirken hata oluştu'
            }));
        } finally {
            setIsProcessingImage(false);
        }
    };

    const handleProfilePictureDeleteSuccess = () => {
        // Edit modunda profil fotoğrafını silmek için işaretle
        setIsProfilePictureMarkedForDeletion(true);

        // Clear selected image and preview
        setSelectedImage(null);
        setPreviewImage(null);
        setImageProcessingResult(null);

        // Show temporary message
        setSuccessMessage('Profil fotoğrafınız silmek üzere işaretlendi. Değişiklikleri kaydetmeyi unutmayın!');

        // Auto-clear success message after 3 seconds
        setTimeout(() => {
            setSuccessMessage(null);
        }, 3000);
    };

    const handleProfilePictureDeleteError = (error: string) => {
        setErrors(prev => ({
            ...prev,
            image: error
        }));
    };

    const validateForm = () => {
        const newErrors: { [key: string]: string } = {};

        if (!formData.firstName.trim()) {
            newErrors.firstName = 'Ad alanı zorunludur';
        }

        if (!formData.lastName.trim()) {
            newErrors.lastName = 'Soyad alanı zorunludur';
        }

        if (formData.phone && !/^\+?\d{10,15}$/.test(formData.phone.replace(/\s+/g, ''))) {
            newErrors.phone = 'Geçerli bir telefon numarası girin';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const hasChanges = () => {
        // Mevcut veriler ile orijinal veriler arasında fark olup olmadığını kontrol et
        const hasDataChanges = formData.firstName !== originalData.firstName ||
            formData.lastName !== originalData.lastName ||
            formData.phone !== originalData.phone ||
            formData.location !== originalData.location ||
            formData.birthDate !== originalData.birthDate ||
            formData.gender !== originalData.gender;



        return hasDataChanges || selectedImage !== null || isProfilePictureMarkedForDeletion;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        if (!hasChanges()) {
            // Değişiklik yoksa direkt hesap sayfasına dön
            router.push('/account');
            return;
        }

        setIsSubmitting(true);

        try {
            // Clear previous messages
            setErrors({});
            setSuccessMessage(null);

            const promises: Promise<any>[] = [];

            // Değişen alanları kontrol et ve API'ye gönder
            const hasProfileDataChanges = formData.firstName !== originalData.firstName ||
                formData.lastName !== originalData.lastName ||
                formData.phone !== originalData.phone ||
                formData.location !== originalData.location ||
                formData.birthDate !== originalData.birthDate ||
                formData.gender !== originalData.gender;

            if (hasProfileDataChanges) {
                const updateData: UpdateProfileRequest = {};

                // Tüm dolu alanları gönder (backend tüm bilgileri bekliyor)
                if (formData.firstName && formData.firstName.trim()) {
                    updateData.firstName = formData.firstName.trim();
                }

                if (formData.lastName && formData.lastName.trim()) {
                    updateData.lastName = formData.lastName.trim();
                }

                if (formData.phone && formData.phone.trim()) {
                    updateData.phoneNumber = formData.phone.trim();
                }

                if (formData.location && formData.location.trim()) {
                    updateData.location = formData.location.trim();
                }

                if (formData.birthDate) {
                    updateData.dateOfBirth = new Date(formData.birthDate).toISOString();
                }

                if (formData.gender) {
                    const genderMap: { [key: string]: number } = {
                        'erkek': GenderType.Male,
                        'kadın': GenderType.Female,
                        'diğer': GenderType.Other
                    };
                    if (genderMap[formData.gender]) {
                        updateData.gender = genderMap[formData.gender];
                    }
                }

                // Dolu alanlar varsa API çağrısı yap
                if (Object.keys(updateData).length > 0) {
                    promises.push(authService.updateProfile(updateData));
                }
            }

            // Fotoğraf seçildiyse fotoğraf yükleme API çağrısı yap
            if (selectedImage) {
                promises.push(authService.updateProfilePicture(selectedImage));
            }

            // Profil fotoğrafı silmek üzere işaretlendiyse silme API çağrısı yap
            if (isProfilePictureMarkedForDeletion) {
                promises.push(authService.deleteProfilePicture());
            }

            // Tüm API çağrılarını paralel olarak yap
            await Promise.all(promises);

            // Cache'leri güncelle - hem user hem de profileInfo
            queryClient.invalidateQueries({ queryKey: authKeys.user() });
            queryClient.invalidateQueries({ queryKey: authKeys.profileInfo() });

            // Fresh data'yı getir
            await Promise.all([
                queryClient.refetchQueries({ queryKey: authKeys.user() }),
                queryClient.refetchQueries({ queryKey: authKeys.profileInfo() })
            ]);

            // Show success message based on what was updated
            if (selectedImage || isProfilePictureMarkedForDeletion) {
                if (hasProfileDataChanges) {
                    setSuccessMessage('Profil bilgileriniz ve fotoğrafınız başarıyla güncellendi!');
                } else {
                    setSuccessMessage('Profil fotoğrafınız başarıyla güncellendi!');
                }
            } else {
                setSuccessMessage('Profil bilgileriniz başarıyla güncellendi!');
            }

            // Clear form states
            setSelectedImage(null);
            setPreviewImage(null);
            setImageProcessingResult(null);
            setIsProfilePictureMarkedForDeletion(false);

            // Auto-clear success message after 3 seconds
            setTimeout(() => {
                setSuccessMessage(null);
            }, 3000);

            // Redirect after a short delay
            setTimeout(() => {
                router.push('/account');
            }, 1500);

        } catch (error: any) {
            setErrors({
                submit: error.response?.data?.message || 'Profil güncellenirken bir hata oluştu'
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    // Loading durumu
    if (isLoading || isProfileLoading) {
        return (
            <div className="container mx-auto px-4 py-12">
                <div className="max-w-4xl mx-auto">
                    <div className="bg-white rounded-2xl shadow-lg overflow-hidden p-8">
                        <div className="animate-pulse">
                            <div className="h-8 bg-gray-300 rounded mb-6 w-64"></div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="h-10 bg-gray-300 rounded"></div>
                                <div className="h-10 bg-gray-300 rounded"></div>
                                <div className="h-10 bg-gray-300 rounded"></div>
                                <div className="h-10 bg-gray-300 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // User bilgisi yoksa (giriş yapmamış)
    if (!user) {
        return null; // useEffect'te zaten redirect yapılıyor
    }

    // Varsayılan avatar placeholder
    const getInitials = (firstName: string, lastName: string) => {
        return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
    };

    // Profil fotoğrafı URL'si - önce preview, sonra mevcut profil fotoğrafı (silmek üzere işaretlenmemişse), son olarak fallback
    const displayImageUrl = previewImage || (!isProfilePictureMarkedForDeletion ? profileInfo?.profilePictureUrl : null);

    return (
        <div className="container mx-auto px-4 py-12">
            <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div className="p-6 md:p-8">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-8 pb-6 border-b border-gray-200">
                            <div>
                                <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
                                    Profili Düzenle
                                </h1>
                                <p className="text-gray-600">
                                    Profil bilgilerinizi güncelleyebilirsiniz
                                </p>
                            </div>
                            <Link href="/account">
                                <motion.button
                                    className="text-gray-500 hover:text-gray-700 transition-colors flex items-center"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Geri Dön
                                </motion.button>
                            </Link>
                        </div>

                        {/* Messages */}
                        {errors.submit && (
                            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div className="flex items-center">
                                    <svg className="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span className="text-red-700">{errors.submit}</span>
                                </div>
                            </div>
                        )}

                        {successMessage && (
                            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div className="flex items-center">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span className="text-green-700">{successMessage}</span>
                                </div>
                            </div>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-8">
                            {/* Profil Fotoğrafı */}
                            <div className="flex flex-col items-center space-y-4">
                                <div className="relative">
                                    <div className="w-32 h-32 rounded-full bg-gradient-to-br from-purple-600 to-indigo-600 flex items-center justify-center overflow-hidden">
                                        {displayImageUrl ? (
                                            <img
                                                src={displayImageUrl}
                                                alt="Profile"
                                                className="w-full h-full object-cover"
                                                onError={(e) => {
                                                    // Fotoğraf yüklenemezse initials göster
                                                    (e.currentTarget as HTMLImageElement).style.display = 'none';
                                                    (e.currentTarget.nextElementSibling as HTMLElement).style.display = 'flex';
                                                }}
                                            />
                                        ) : null}
                                        <span
                                            className={`text-white text-3xl font-bold ${displayImageUrl ? 'hidden' : 'flex'} items-center justify-center w-full h-full`}
                                        >
                                            {getInitials(profileInfo?.firstName || user.firstName, profileInfo?.lastName || user.lastName)}
                                        </span>
                                    </div>
                                    <div className="relative">
                                        <motion.button
                                            type="button"
                                            onClick={handleProfilePictureMenuToggle}
                                            disabled={isProcessingImage}
                                            className={`absolute bottom-0 right-0 rounded-full p-2 shadow-lg transition-all ${isProcessingImage
                                                ? 'bg-gray-400 cursor-not-allowed'
                                                : 'bg-purple-600 hover:bg-purple-700'
                                                } text-white`}
                                            whileHover={!isProcessingImage ? { scale: 1.1 } : {}}
                                            whileTap={!isProcessingImage ? { scale: 0.9 } : {}}
                                        >
                                            {isProcessingImage ? (
                                                <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                            ) : (
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            )}
                                        </motion.button>

                                        <ProfilePictureMenu
                                            isOpen={isProfileMenuOpen}
                                            onClose={handleProfilePictureMenuClose}
                                            onFileSelect={handleImageSelect}
                                            hasProfilePicture={!!displayImageUrl && !selectedImage && !isProfilePictureMarkedForDeletion}
                                            onDeleteSuccess={handleProfilePictureDeleteSuccess}
                                            onDeleteError={handleProfilePictureDeleteError}
                                            isDeleting={isProcessingImage}
                                            mode="edit"
                                        />
                                    </div>
                                </div>
                                <div className="text-center">
                                    <p className="text-sm text-gray-600">
                                        {isProcessingImage ? 'Resim WebP formatına dönüştürülüyor...' : 'Profil fotoğrafını değiştirmek için tıklayın'}
                                    </p>
                                    {errors.image && (
                                        <p className="text-sm text-red-600 mt-1">{errors.image}</p>
                                    )}
                                </div>
                            </div>

                            {/* Temel Bilgiler */}
                            <div>
                                <h2 className="text-xl font-semibold text-gray-800 mb-4">Temel Bilgiler</h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                                            Ad *
                                        </label>
                                        <input
                                            type="text"
                                            id="firstName"
                                            name="firstName"
                                            value={formData.firstName}
                                            onChange={handleInputChange}
                                            className={`w-full px-4 py-2 rounded-lg border transition text-black ${errors.firstName
                                                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                                                : 'border-gray-300 focus:ring-purple-500 focus:border-transparent'
                                                } focus:outline-none focus:ring-2`}
                                            required
                                        />
                                        {errors.firstName && (
                                            <p className="text-sm text-red-600 mt-1">{errors.firstName}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                                            Soyad *
                                        </label>
                                        <input
                                            type="text"
                                            id="lastName"
                                            name="lastName"
                                            value={formData.lastName}
                                            onChange={handleInputChange}
                                            className={`w-full px-4 py-2 rounded-lg border transition text-black ${errors.lastName
                                                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                                                : 'border-gray-300 focus:ring-purple-500 focus:border-transparent'
                                                } focus:outline-none focus:ring-2`}
                                            required
                                        />
                                        {errors.lastName && (
                                            <p className="text-sm text-red-600 mt-1">{errors.lastName}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                            Telefon
                                        </label>
                                        <input
                                            type="tel"
                                            id="phone"
                                            name="phone"
                                            value={formData.phone}
                                            onChange={handleInputChange}
                                            className={`w-full px-4 py-2 rounded-lg border transition text-black ${errors.phone
                                                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                                                : 'border-gray-300 focus:ring-purple-500 focus:border-transparent'
                                                } focus:outline-none focus:ring-2`}
                                            placeholder="+90 ************"
                                        />
                                        {errors.phone && (
                                            <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Kişisel Bilgiler */}
                            <div>
                                <h2 className="text-xl font-semibold text-gray-800 mb-4">Kişisel Bilgiler</h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 mb-1">
                                            Doğum Tarihi
                                        </label>
                                        <input
                                            type="date"
                                            id="birthDate"
                                            name="birthDate"
                                            value={formData.birthDate}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
                                            Cinsiyet
                                        </label>
                                        <select
                                            id="gender"
                                            name="gender"
                                            value={formData.gender}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        >
                                            <option value="">Seçiniz</option>
                                            <option value="erkek">Erkek</option>
                                            <option value="kadın">Kadın</option>
                                            <option value="diğer">Diğer</option>
                                        </select>
                                    </div>
                                    <div className="md:col-span-2">
                                        <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                                            Konum
                                        </label>
                                        <input
                                            type="text"
                                            id="location"
                                            name="location"
                                            value={formData.location}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                            placeholder="Şehir, Ülke"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                                <motion.button
                                    type="submit"
                                    disabled={isSubmitting || !hasChanges()}
                                    className={`flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-300 ${isSubmitting || !hasChanges()
                                        ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                                        : 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:shadow-lg'
                                        }`}
                                    whileHover={!isSubmitting && hasChanges() ? { scale: 1.02 } : {}}
                                    whileTap={!isSubmitting && hasChanges() ? { scale: 0.98 } : {}}
                                >
                                    {isSubmitting ? 'Kaydediliyor...' : 'Değişiklikleri Kaydet'}
                                </motion.button>

                                <Link href="/account" className="flex-1">
                                    <motion.button
                                        type="button"
                                        disabled={isSubmitting}
                                        className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-300 ${isSubmitting
                                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                            }`}
                                        whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                                        whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                                    >
                                        İptal
                                    </motion.button>
                                </Link>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
} 