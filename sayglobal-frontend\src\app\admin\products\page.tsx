'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { useAdminProducts, useAdminProductStatistics, useDeleteProduct, useProductCacheRefresh } from '@/hooks/useProducts';
import { useProductFilterStore } from '@/stores/productFilterStore';
import { useModalActions } from '@/stores/modalStore';
import { AdminProduct } from '@/types';
import ProductDeleteConfirmationModal from '@/components/ProductDeleteConfirmationModal';
import AdminProductDetailModalNew from '@/components/AdminProductDetailModalNew';
import {
    Package,
    Search,
    Filter,
    Eye,
    Edit,
    Trash2,
    Plus,
    ArrowLeft,
    Shield,
    TrendingUp,
    AlertTriangle,
    CheckCircle,
    XCircle,
    ChevronLeft,
    ChevronRight,
    Loader2,
    RefreshCw
} from 'lucide-react';
import Link from 'next/link';

const AdminProductsPage = () => {
    const { user, isLoading: authLoading } = useAuth();
    const router = useRouter();
    const queryClient = useQueryClient();
    const { openProductDeleteConfirmation } = useModalActions();
    const {
        searchTerm,
        categoryFilter,
        statusFilter,
        setSearchTerm,
        setCategoryFilter,
        setStatusFilter
    } = useProductFilterStore();

    const [page, setPage] = useState(1);
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
    const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
    const [showDetailModal, setShowDetailModal] = useState(false);



    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setPage(1);
        }, 300);

        return () => {
            clearTimeout(handler);
        };
    }, [searchTerm]);

    useEffect(() => {
        if (!authLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, authLoading, router]);

    // Sayfa yüklendiğinde istatistikleri ve ürün listesini yenile
    useEffect(() => {
        if (user && user.role === 'admin') {
            queryClient.invalidateQueries({ queryKey: ['adminProductStatistics'] });
            queryClient.invalidateQueries({ queryKey: ['adminProducts'] });
            console.log('📊 Ürün yönetimi sayfası yüklendi, istatistikler ve ürün listesi yenileniyor...');
        }
    }, [user, queryClient]);

    const { data: statisticsData, isLoading: statsLoading } = useAdminProductStatistics();
    const { data: paginatedProducts, isLoading: productsLoading, isFetching } = useAdminProducts(page, debouncedSearchTerm);

    const deleteProductMutation = useDeleteProduct();

    // Cache yenileme fonksiyonları
    const { refreshProductLists } = useProductCacheRefresh();

    const handleDeleteProduct = (productId: number, productName: string, brandName: string, imageUrl?: string) => {
        openProductDeleteConfirmation({
            productId,
            productName,
            brandName,
            imageUrl,
            onConfirm: () => {
                deleteProductMutation.mutate(productId);
            }
        });
    };

    const handleShowDetail = (productId: number) => {
        setSelectedProductId(productId);
        setShowDetailModal(true);
    };

    const handleCloseDetailModal = () => {
        setShowDetailModal(false);
        setSelectedProductId(null);

        // Modal kapandığında istatistikleri yenile (ürün düzenlenmiş olabilir)
        queryClient.invalidateQueries({ queryKey: ['adminProductStatistics'] });
        console.log('🔄 Modal kapandı, istatistik cache temizlendi');
    };

    const products = paginatedProducts || [];

    const filteredProducts = products.filter((product: AdminProduct) => {
        const matchesCategory = categoryFilter === 'all' || product.categoryName === categoryFilter;
        const matchesStatus = statusFilter === 'all' ||
            (statusFilter === 'active' && product.isActive) ||
            (statusFilter === 'inactive' && !product.isActive) ||
            (statusFilter === 'low-stock' && product.stock > 0 && product.stock < 20) ||
            (statusFilter === 'out-of-stock' && product.stock === 0);
        return matchesCategory && matchesStatus;
    });

    const isLoading = authLoading || statsLoading || (productsLoading && !isFetching);

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('tr-TR');
    };

    // Ürün ismini belirli karakter sınırında kısaltma fonksiyonu
    const truncateProductName = (name: string, maxLength: number = 22) => {
        if (name.length <= maxLength) return name;
        return name.substring(0, maxLength) + '...';
    };

    const getStockStatus = (stock: number) => {
        if (stock === 0) {
            return { text: 'Stokta Yok', color: 'bg-red-100 text-red-800', icon: <XCircle className="h-4 w-4" /> };
        } else if (stock < 20) {
            return { text: 'Az Stok', color: 'bg-yellow-100 text-yellow-800', icon: <AlertTriangle className="h-4 w-4" /> };
        } else {
            return { text: 'Stokta', color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-4 w-4" /> };
        }
    };

    const categories = [...new Set(products.map(p => p.categoryName))];

    // API'den gelen istatistik verileri
    const totalProducts = statisticsData?.totalProductCount || 0;
    const activeProducts = statisticsData?.activeCount || 0;
    const lowStockProducts = statisticsData?.lowStockCount || 0;
    const outOfStockProducts = statisticsData?.outOfStockCount || 0;

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/admin"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Admin Paneli
                            </Link>
                            <span className="text-gray-300">/</span>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Ürün Yönetimi
                            </h1>
                        </div>
                        <div className="flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg">
                            <Shield className="h-5 w-5 text-red-600" />
                            <span className="text-red-800 font-medium">Admin Erişimi</span>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Ürün</p>
                                <p className="text-2xl font-bold text-gray-900">{totalProducts}</p>
                            </div>
                            <Package className="h-8 w-8 text-blue-600" />
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Aktif Ürün</p>
                                <p className="text-2xl font-bold text-gray-900">{activeProducts}</p>
                            </div>
                            <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Az Stok</p>
                                <p className="text-2xl font-bold text-gray-900">{lowStockProducts}</p>
                            </div>
                            <AlertTriangle className="h-8 w-8 text-yellow-600" />
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Stokta Yok</p>
                                <p className="text-2xl font-bold text-gray-900">{outOfStockProducts}</p>
                            </div>
                            <XCircle className="h-8 w-8 text-red-600" />
                        </div>
                    </motion.div>
                </div>

                {/* Filters */}
                <motion.div
                    className="bg-white rounded-xl shadow-lg p-6 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                >
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <Search className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                <input
                                    type="text"
                                    placeholder="Ürün ara..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-black"
                                />
                                {isFetching && <Loader2 className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin" />}
                            </div>

                            <select
                                value={categoryFilter}
                                onChange={(e) => setCategoryFilter(e.target.value)}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600"
                            >
                                <option value="all">Tüm Kategoriler</option>
                                {categories.map(category => (
                                    <option key={category} value={category}>{category}</option>
                                ))}
                            </select>

                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600"
                            >
                                <option value="all">Tüm Durumlar</option>
                                <option value="active">Aktif</option>
                                <option value="inactive">Pasif</option>
                                <option value="low-stock">Az Stok</option>
                                <option value="out-of-stock">Stokta Yok</option>
                            </select>
                        </div>

                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => {
                                    console.log('🔄 Manuel cache yenileme başlatıldı...');
                                    refreshProductLists();
                                }}
                                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                                title="Verileri yenile"
                            >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Yenile
                            </button>
                            <Link
                                href="/admin/products/add"
                                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <Plus className="h-4 w-4 mr-2" />
                                Yeni Ürün
                            </Link>
                        </div>
                    </div>
                </motion.div>

                {/* Products Table */}
                <motion.div
                    className={`bg-white rounded-xl shadow-lg overflow-hidden transition-opacity ${isFetching ? 'opacity-70' : ''}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                >
                    <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 className="text-lg font-semibold text-gray-900">
                            Ürünler
                        </h3>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Ürün
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Kategori
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Fiyat
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stok
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Satış
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Durum
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Son Güncelleme
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredProducts.map((product: AdminProduct) => {
                                    const stockStatus = getStockStatus(product.stock);
                                    return (
                                        <tr
                                            key={product.id}
                                            className="hover:bg-gray-50"
                                        >
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="h-12 w-12 flex-shrink-0">
                                                        {product.imageUrl ? (
                                                            <img src={product.imageUrl} alt={product.name} className="h-12 w-12 rounded-lg object-cover" />
                                                        ) : (
                                                            <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                                                <Package className="h-6 w-6 text-gray-400" />
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900" title={product.name}>
                                                            {truncateProductName(product.name)}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {product.brandName}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    {product.categoryName}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {formatCurrency(product.price)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stockStatus.color}`}>
                                                    {stockStatus.icon}
                                                    <span className="ml-1">{product.stock} - {stockStatus.text}</span>
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div className="flex items-center">
                                                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                                                    {product.salesCount} adet
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${product.isActive
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                                    }`}>
                                                    {product.isActive ? 'Aktif' : 'Pasif'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatDate(product.updatedAt)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex items-center space-x-2">
                                                    <button
                                                        onClick={() => handleShowDetail(product.id)}
                                                        className="text-blue-600 hover:text-blue-900"
                                                        title="Detayları Görüntüle"
                                                    >
                                                        <Eye className="h-4 w-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => router.push(`/admin/products/edit/${product.id}?from=products`)}
                                                        className="text-green-600 hover:text-green-900"
                                                        title="Ürünü Düzenle"
                                                    >
                                                        <Edit className="h-4 w-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDeleteProduct(product.id, product.name, product.brandName, product.imageUrl || undefined)}
                                                        disabled={deleteProductMutation.isPending}
                                                        className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    >
                                                        {deleteProductMutation.isPending ? (
                                                            <Loader2 className="h-4 w-4 animate-spin" />
                                                        ) : (
                                                            <Trash2 className="h-4 w-4" />
                                                        )}
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>

                    {filteredProducts.length === 0 && !isFetching && (
                        <div className="px-6 py-12 text-center">
                            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Ürün bulunamadı</h3>
                            <p className="text-gray-500">Arama veya filtreleme kriterlerinizi değiştirerek tekrar deneyin.</p>
                        </div>
                    )}

                    {/* Pagination Controls */}
                    <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <button
                            onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                            disabled={page === 1}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <ChevronLeft className="h-4 w-4 mr-2" />
                            Önceki
                        </button>
                        <span className="text-sm text-gray-700">
                            Sayfa <span className="font-bold">{page}</span>
                        </span>
                        <button
                            onClick={() => setPage(prev => products.length === 10 ? prev + 1 : prev)}
                            disabled={products.length < 10}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Sonraki
                            <ChevronRight className="h-4 w-4 ml-2" />
                        </button>
                    </div>
                </motion.div>
            </div>

            {/* Product Delete Confirmation Modal */}
            <ProductDeleteConfirmationModal />

            {/* Product Detail Modal */}
            <AdminProductDetailModalNew
                productId={selectedProductId}
                isOpen={showDetailModal}
                onClose={handleCloseDetailModal}
            />
        </div>
    );
};

export default AdminProductsPage; 