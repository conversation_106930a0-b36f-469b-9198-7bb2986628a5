'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Check, Zap, Info, AlertTriangle } from 'lucide-react';
import {
    SubCategoryFeature,
    FeatureValue,
    VariantFormData,
    VariantFeatureDetail,
    VariantPricing
} from '@/types';
import { productService } from '@/services/api';

interface ProductVariantSetupModalProps {
    isOpen: boolean;
    onClose: () => void;
    onGenerateVariants: (variants: VariantFormData[]) => void;
    availableFeatures: SubCategoryFeature[];
}

const ProductVariantSetupModal: React.FC<ProductVariantSetupModalProps> = ({
    isOpen,
    onClose,
    onGenerateVariants,
    availableFeatures
}) => {
    const [featureValues, setFeatureValues] = useState<{ [key: number]: FeatureValue[] }>({});
    const [selectedValues, setSelectedValues] = useState<{ [featureDefId: number]: number[] }>({});
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Load feature values when modal opens
    useEffect(() => {
        if (isOpen && availableFeatures.length > 0) {
            loadAllFeatureValues();
        }
    }, [isOpen, availableFeatures]);

    // Reset when modal closes
    useEffect(() => {
        if (!isOpen) {
            setSelectedValues({});
            setError(null);
        }
    }, [isOpen]);

    // Modal açıkken body scroll'unu engelle ve titreme önle
    useEffect(() => {
        if (isOpen) {
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        }

        return () => {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        };
    }, [isOpen]);

    const loadAllFeatureValues = async () => {
        try {
            setIsLoading(true);
            const promises = availableFeatures.map(async (feature) => {
                const response = await productService.getFeatureValues(feature.featureDefinitionId);
                if (response.success) {
                    return {
                        featureDefinitionId: feature.featureDefinitionId,
                        values: response.data
                    };
                }
                return null;
            });

            const results = await Promise.all(promises);
            const featureValuesMap: { [key: number]: FeatureValue[] } = {};

            results.forEach(result => {
                if (result) {
                    featureValuesMap[result.featureDefinitionId] = result.values;
                }
            });

            setFeatureValues(featureValuesMap);
        } catch (error) {
            console.error('Feature values loading error:', error);
            setError('Özellik değerleri yüklenirken hata oluştu');
        } finally {
            setIsLoading(false);
        }
    };

    const handleValueToggle = (featureDefId: number, valueId: number) => {
        setSelectedValues(prev => {
            const currentValues = prev[featureDefId] || [];
            const isSelected = currentValues.includes(valueId);

            if (isSelected) {
                // Remove value
                return {
                    ...prev,
                    [featureDefId]: currentValues.filter(id => id !== valueId)
                };
            } else {
                // Add value
                return {
                    ...prev,
                    [featureDefId]: [...currentValues, valueId]
                };
            }
        });
    };

    const generateVariantCombinations = (): VariantFormData[] => {
        const selectedFeatureEntries = Object.entries(selectedValues).filter(([_, values]) => values.length > 0);

        if (selectedFeatureEntries.length === 0) {
            return [];
        }

        // Generate all possible combinations
        const combinations: { [featureDefId: number]: number }[] = [];

        const generateCombos = (index: number, currentCombo: { [featureDefId: number]: number }) => {
            if (index === selectedFeatureEntries.length) {
                combinations.push({ ...currentCombo });
                return;
            }

            const [featureDefId, valueIds] = selectedFeatureEntries[index];
            for (const valueId of valueIds) {
                generateCombos(index + 1, {
                    ...currentCombo,
                    [parseInt(featureDefId)]: valueId
                });
            }
        };

        generateCombos(0, {});

        // Convert combinations to VariantFormData
        return combinations.map((combo, index) => {
            const featureDetails: VariantFeatureDetail[] = Object.entries(combo).map(([featureDefId, valueId]) => {
                const feature = availableFeatures.find(f => f.featureDefinitionId === parseInt(featureDefId));
                const value = featureValues[parseInt(featureDefId)]?.find(v => v.id === valueId);

                return {
                    featureDefinitionId: parseInt(featureDefId),
                    featureValueId: valueId,
                    featureName: feature?.featureDefinition?.name || 'Özellik',
                    featureValue: value?.value || 'Değer'
                };
            });

            const variantName = featureDetails.map(f => f.featureValue).join(' - ');

            // Convert single values to arrays for consistency
            const selectedFeaturesArray: { [key: number]: number[] } = {};
            Object.entries(combo).forEach(([featureDefId, valueId]) => {
                selectedFeaturesArray[parseInt(featureDefId)] = [valueId];
            });

            return {
                id: Date.now() + index,
                name: variantName,
                selectedFeatures: selectedFeaturesArray,
                featureDetails,
                pricing: {
                    price: 0,
                    stock: 0,
                    extraDiscount: 0,
                    ratios: {
                        pvRatio: 0,
                        cvRatio: 0,
                        spRatio: 0
                    },
                    points: {
                        pv: 0,
                        cv: 0,
                        sp: 0
                    }
                },
                images: [],
                isActive: true
            };
        });
    };

    const handleGenerateVariants = () => {
        const totalSelectedFeatures = Object.values(selectedValues).reduce((sum, values) => sum + values.length, 0);

        if (totalSelectedFeatures === 0) {
            setError('En az bir özellik değeri seçmelisiniz');
            return;
        }

        const variants = generateVariantCombinations();

        if (variants.length === 0) {
            setError('Varyant oluşturulamadı');
            return;
        }

        if (variants.length > 50) {
            setError(`Çok fazla varyant oluşacak (${variants.length} adet). Lütfen daha az özellik değeri seçin.`);
            return;
        }

        onGenerateVariants(variants);
        onClose();
    };

    const getTotalCombinations = (): number => {
        const selectedCounts = Object.values(selectedValues).map(values => Math.max(values.length, 1));
        return selectedCounts.reduce((total, count) => total * count, 1);
    };

    const totalCombinations = getTotalCombinations();
    const canGenerate = totalCombinations > 1;

    const hasSelections = Object.values(selectedValues).some(values => values.length > 0);

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 bg-black/20 backdrop-blur-sm bg-opacity-50 flex items-center justify-center z-50">
                    <motion.div
                        className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.2 }}
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between p-6 border-b border-gray-200">
                            <div>
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    Varyant Kurulumu
                                </h2>
                                <p className="text-gray-600 mt-1">
                                    Hangi özellik değerlerini kullanmak istiyorsunuz? Seçtiğiniz değerlerin tüm kombinasyonları otomatik oluşturulacak.
                                </p>
                            </div>
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <X className="h-6 w-6" />
                            </button>
                        </div>

                        {/* Error Display */}
                        {error && (
                            <div className="p-4 bg-red-50 border-b border-red-200">
                                <p className="text-red-800 text-sm">{error}</p>
                            </div>
                        )}

                        {/* Content */}
                        <div className="p-6 overflow-y-auto max-h-[calc(90vh-250px)]">
                            {isLoading ? (
                                <div className="flex items-center justify-center py-12">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                                    <span className="ml-3 text-gray-600">Özellikler yükleniyor...</span>
                                </div>
                            ) : (
                                <div className="space-y-6">
                                    {/* Info Box */}
                                    <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                                        <div className="flex items-start">
                                            <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                                            <div>
                                                <p className="text-blue-800 font-medium">Nasıl Çalışır?</p>
                                                <p className="text-blue-700 text-sm mt-1">
                                                    Örneğin "Renk" için Kırmızı ve Mavi, "Beden" için S ve M seçerseniz,
                                                    4 varyant oluşur: Kırmızı-S, Kırmızı-M, Mavi-S, Mavi-M
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Feature Selection */}
                                    {availableFeatures.map((feature) => (
                                        <div key={feature.id} className="border rounded-lg p-4">
                                            <h3 className="font-medium text-gray-900 mb-3">
                                                {feature.featureDefinition?.name || 'Özellik'}
                                                {feature.featureDefinition?.description && (
                                                    <span className="text-sm text-gray-500 ml-2">
                                                        ({feature.featureDefinition.description})
                                                    </span>
                                                )}
                                            </h3>
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                                {featureValues[feature.featureDefinitionId]?.map((value) => {
                                                    const isSelected = selectedValues[feature.featureDefinitionId]?.includes(value.id) || false;
                                                    return (
                                                        <button
                                                            key={value.id}
                                                            onClick={() => handleValueToggle(feature.featureDefinitionId, value.id)}
                                                            className={`p-3 border rounded-lg text-sm transition-colors flex items-center justify-between ${isSelected
                                                                ? 'border-red-500 bg-red-50 text-red-700'
                                                                : 'border-gray-200 hover:bg-gray-50 text-black'
                                                                }`}
                                                        >
                                                            <span>{value.value}</span>
                                                            {isSelected && (
                                                                <Check className="h-4 w-4" />
                                                            )}
                                                        </button>
                                                    );
                                                })}
                                            </div>
                                            {selectedValues[feature.featureDefinitionId]?.length > 0 && (
                                                <p className="text-sm text-gray-600 mt-2">
                                                    {selectedValues[feature.featureDefinitionId].length} değer seçili
                                                </p>
                                            )}
                                        </div>
                                    ))}

                                    {/* Preview */}
                                    {hasSelections && (
                                        canGenerate ? (
                                            <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                                                <div className="flex items-center">
                                                    <Zap className="h-5 w-5 text-green-600 mr-2" />
                                                    <div>
                                                        <p className="text-green-800 font-medium">
                                                            {totalCombinations} varyant oluşturulacak
                                                        </p>
                                                        <p className="text-green-700 text-sm mt-1">
                                                            Her varyant için fiyat, stok ve puan oranları ayrı ayrı belirlenecek
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                                                <div className="flex items-center">
                                                    <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                                                    <div>
                                                        <p className="text-yellow-800 font-medium">
                                                            Varyant Oluşturulamıyor
                                                        </p>
                                                        <p className="text-yellow-700 text-sm mt-1">
                                                            Varyant oluşturmak için birden fazla seçenek veya kombinasyon gereklidir.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            )}
                        </div>

                        {/* Footer */}
                        <div className="flex items-center justify-between p-6 border-t border-gray-200">
                            <div className="text-sm text-gray-600">
                                {hasSelections && (
                                    <span>{totalCombinations} varyant oluşturulacak</span>
                                )}
                            </div>
                            <div className="flex space-x-3">
                                <button
                                    onClick={onClose}
                                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                                >
                                    İptal
                                </button>
                                <button
                                    onClick={handleGenerateVariants}
                                    disabled={isLoading || !canGenerate}
                                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                                >
                                    <Zap className="h-4 w-4 mr-2" />
                                    Varyantları Oluştur
                                </button>
                            </div>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default ProductVariantSetupModal; 