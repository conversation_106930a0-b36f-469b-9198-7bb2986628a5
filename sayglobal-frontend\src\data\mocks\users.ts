import { User, Address } from '../../types';

// Örnek adresler
const addresses: Address[] = [
    {
        id: 1,
        title: 'Ev Adresi',
        addressLine: 'Atatürk Caddesi No: 123 Daire: 5',
        city: 'İstanbul',
        state: 'Kadıköy',
        postalCode: '34700',
        isDefault: true
    },
    {
        id: 2,
        title: 'İş Adresi',
        addressLine: 'Bağdat Caddesi No: 456 Kat: 3',
        city: 'İstanbul',
        state: 'Maltepe',
        postalCode: '34840',
        isDefault: false
    }
];

// Örnek kullanıcılar
export const users: User[] = [
    {
        id: 1,
        firstName: 'Ahmet',
        lastName: 'Y<PERSON>lma<PERSON>',
        email: '<EMAIL>',
        phone: '05551234567',
        address: addresses[0],
        role: 'customer',
        joinDate: '2023-01-15'
    },
    {
        id: 2,
        firstName: 'Ayşe',
        lastName: '<PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '05559876543',
        address: addresses[1],
        role: 'distributor',
        joinDate: '2022-11-20'
    },
    {
        id: 3,
        firstName: 'Mehmet',
        lastName: 'Demir',
        email: '<EMAIL>',
        phone: '05553456789',
        role: 'admin',
        joinDate: '2022-09-10'
    }
]; 