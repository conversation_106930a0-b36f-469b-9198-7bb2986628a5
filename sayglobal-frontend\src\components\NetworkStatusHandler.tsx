'use client';

import { useEffect, useState, useRef } from 'react';
import { useNetworkStore } from '@/stores/networkStore';

// Konfigürasyon sabitleri
const NETWORK_CONFIG = {
    INITIAL_DELAY: 1000,      // İlk deneme gecikmesi (1 saniye)
    MAX_DELAY: 30000,         // Maksimum gecikme (30 saniye)
    MAX_ATTEMPTS: 10,         // Maksimum deneme sayısı
    BACKOFF_MULTIPLIER: 2,    // Exponential backoff çarpanı
};

const ReconnectingBanner = () => (
    <div className="network-status-banner">
        <div className="network-status-content">
            <div className="network-status-icon">
                <span className="network-status-spinner"></span>
            </div>
            <div className="network-status-text">
                <div className="network-status-title">
                    İnternet bağlantınızda sorun yaşanıyor
                </div>
                <div className="network-status-subtitle">
                    Bağlantı kontrol ediliyor...
                </div>
            </div>
        </div>
        <style jsx>{`
            .network-status-banner {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
                color: #212529;
                z-index: 9999;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                animation: slideDown 0.3s ease-out;
            }

            .network-status-content {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 12px 20px;
                gap: 12px;
            }

            .network-status-icon {
                display: flex;
                align-items: center;
            }

            .network-status-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #212529;
                border-top: 2px solid transparent;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            .network-status-text {
                text-align: center;
            }

            .network-status-title {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 2px;
            }

            .network-status-subtitle {
                font-size: 12px;
                font-weight: 400;
                opacity: 0.8;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            @keyframes slideDown {
                from {
                    transform: translateY(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            @media (max-width: 768px) {
                .network-status-content {
                    padding: 10px 16px;
                    gap: 8px;
                }
                
                .network-status-title {
                    font-size: 13px;
                }
                
                .network-status-subtitle {
                    font-size: 11px;
                }
            }
        `}</style>
    </div>
);

export const NetworkStatusHandler = () => {
    const { status, setStatus } = useNetworkStore();

    const [attempt, setAttempt] = useState(0);
    const [nextRetryIn, setNextRetryIn] = useState(0);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const countdownRef = useRef<NodeJS.Timeout | null>(null);

    // Component mount olduğunda network durumunu kontrol et
    useEffect(() => {
        const checkInitialNetworkStatus = async () => {
            if (navigator.onLine) {
                // Browser online diyor, kontrol et
                console.log('🌐 Browser online - status kontrol ediliyor');
                if (status !== 'online') {
                    const isConnected = await checkConnectivity();
                    if (isConnected) {
                        console.log('✅ Network connectivity confirmed - status online yapılıyor');
                        setStatus('online');
                    }
                }
            } else {
                console.log('🌐 Browser offline - status offline yapılıyor');
                setStatus('offline');
            }
        };

        checkInitialNetworkStatus();
    }, []); // Sadece component mount olduğunda çalışır

    // Browser network event'lerini dinle
    useEffect(() => {
        const handleOnline = () => {
            console.log('🌐 Browser online event detected');
            setStatus('online');
        };

        const handleOffline = () => {
            console.log('🌐 Browser offline event detected');
            setStatus('offline');
        };

        // Network event listener'ları ekle
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        // Cleanup
        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, [setStatus]);

    // Exponential backoff ile gecikme hesaplama
    const calculateDelay = (attemptNumber: number): number => {
        const delay = NETWORK_CONFIG.INITIAL_DELAY * Math.pow(NETWORK_CONFIG.BACKOFF_MULTIPLIER, attemptNumber);
        return Math.min(delay, NETWORK_CONFIG.MAX_DELAY);
    };

    // Hafif connectivity check - auth gerektirmez
    const checkConnectivity = async (): Promise<boolean> => {
        try {
            // Basit HEAD request ile connectivity check
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            // Root path'e basit bir HEAD request at
            const response = await fetch(window.location.origin, {
                method: 'HEAD',
                signal: controller.signal,
                cache: 'no-cache',
            });

            clearTimeout(timeoutId);
            return response.ok || response.status < 500; // 4xx kodları da kabul et
        } catch {
            return false;
        }
    };

    // Geri sayım sayacı
    const startCountdown = (seconds: number) => {
        setNextRetryIn(seconds);

        const countdown = () => {
            setNextRetryIn(prev => {
                if (prev <= 1) {
                    return 0;
                }
                countdownRef.current = setTimeout(countdown, 1000);
                return prev - 1;
            });
        };

        countdownRef.current = setTimeout(countdown, 1000);
    };

    // Yeniden bağlanma denemesi
    const attemptReconnect = async () => {
        const currentAttempt = attempt + 1;
        setAttempt(currentAttempt);

        console.log(`🔄 Yeniden bağlanma denemesi ${currentAttempt}/${NETWORK_CONFIG.MAX_ATTEMPTS}`);

        try {
            const isConnected = await checkConnectivity();

            if (isConnected) {
                console.log('✅ Bağlantı başarılı, online moda geçiliyor.');
                setStatus('online');
                setAttempt(0);
                return;
            }

            // Maksimum deneme sayısına ulaştıysak dur
            if (currentAttempt >= NETWORK_CONFIG.MAX_ATTEMPTS) {
                console.log('❌ Maksimum deneme sayısına ulaşıldı, offline modda kalınıyor.');
                setStatus('offline');
                setAttempt(0);
                return;
            }

            // Bir sonraki deneme için gecikme hesapla
            const delay = calculateDelay(currentAttempt);
            const delayInSeconds = Math.ceil(delay / 1000);

            console.log(`❌ Bağlantı denemesi başarısız, ${delayInSeconds}s sonra yeniden denenecek.`);

            startCountdown(delayInSeconds);
            timeoutRef.current = setTimeout(attemptReconnect, delay);

        } catch (error) {
            console.log('❌ Bağlantı kontrol hatası:', error);

            // Hata durumunda da retry logic'i uygula
            if (currentAttempt < NETWORK_CONFIG.MAX_ATTEMPTS) {
                const delay = calculateDelay(currentAttempt);
                const delayInSeconds = Math.ceil(delay / 1000);

                startCountdown(delayInSeconds);
                timeoutRef.current = setTimeout(attemptReconnect, delay);
            } else {
                setStatus('offline');
                setAttempt(0);
            }
        }
    };

    useEffect(() => {
        // Cleanup function
        const cleanup = () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                timeoutRef.current = null;
            }
            if (countdownRef.current) {
                clearTimeout(countdownRef.current);
                countdownRef.current = null;
            }
        };

        // Eğer durum 'reconnecting' ise denemeyi başlat
        if (status === 'reconnecting') {
            setAttempt(0);
            attemptReconnect();
        } else {
            cleanup();
            setAttempt(0);
            setNextRetryIn(0);
        }

        // Cleanup on unmount or dependency change
        return cleanup;
    }, [status]); // isAuthenticated dependency'sini kaldırdık

    // Banner'ı sadece 'reconnecting' durumunda göster
    if (status !== 'reconnecting') {
        return null;
    }

    return <ReconnectingBanner />;
}; 