'use client';

import { motion } from 'framer-motion';
import { Check } from 'lucide-react';

interface CustomCheckboxProps {
    checked: boolean;
    onChange: (checked: boolean) => void;
    label: string;
    disabled?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export default function CustomCheckbox({
    checked,
    onChange,
    label,
    disabled = false,
    size = 'md',
    className = ''
}: CustomCheckboxProps) {
    const sizeClasses = {
        sm: {
            checkbox: 'w-4 h-4',
            text: 'text-xs',
            icon: 'w-2.5 h-2.5'
        },
        md: {
            checkbox: 'w-5 h-5',
            text: 'text-sm',
            icon: 'w-3 h-3'
        },
        lg: {
            checkbox: 'w-6 h-6',
            text: 'text-base',
            icon: 'w-4 h-4'
        }
    };

    const currentSize = sizeClasses[size];

    return (
        <motion.label
            className={`flex items-center space-x-3 cursor-pointer select-none ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
            whileHover={!disabled ? { scale: 1.01 } : {}}
            whileTap={!disabled ? { scale: 0.99 } : {}}
        >
            <div className="relative">
                {/* Hidden native checkbox for accessibility */}
                <input
                    type="checkbox"
                    checked={checked}
                    onChange={(e) => !disabled && onChange(e.target.checked)}
                    disabled={disabled}
                    className="sr-only"
                />

                {/* Custom checkbox */}
                <motion.div
                    className={`
                        ${currentSize.checkbox}
                        rounded-md
                        border-2
                        flex
                        items-center
                        justify-center
                        transition-all
                        duration-200
                        ${checked
                            ? 'bg-gradient-to-br from-purple-500 to-purple-700 border-purple-600 shadow-lg shadow-purple-500/25'
                            : 'bg-white border-gray-300 hover:border-purple-400'
                        }
                        ${!disabled && 'hover:shadow-md'}
                    `}
                    initial={false}
                    animate={{
                        scale: checked ? 1.05 : 1,
                        boxShadow: checked
                            ? '0 10px 25px -5px rgba(147, 51, 234, 0.25), 0 4px 6px -2px rgba(147, 51, 234, 0.05)'
                            : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
                    }}
                    transition={{ duration: 0.2, ease: "easeInOut" }}
                >
                    {/* Check icon */}
                    <motion.div
                        initial={false}
                        animate={{
                            scale: checked ? 1 : 0,
                            opacity: checked ? 1 : 0,
                        }}
                        transition={{ duration: 0.15, ease: "easeInOut" }}
                    >
                        <Check
                            className={`${currentSize.icon} text-white stroke-[3]`}
                        />
                    </motion.div>
                </motion.div>
            </div>

            {/* Label */}
            <span
                className={`
                    ${currentSize.text} 
                    text-gray-700 
                    font-medium 
                    truncate 
                    flex-1
                    ${!disabled && 'group-hover:text-gray-900'}
                `}
            >
                {label}
            </span>
        </motion.label>
    );
}
