'use client';

import React, { useState } from 'react';
import { distributorEarnings, distributorDashboard } from '@/data/mocks/distributor';
import {
    TrendingUp,
    Calendar,
    Filter,
    Download,
    ArrowUpDown,
    Eye,
    Search,
    DollarSign,
    Star,
    User
} from 'lucide-react';

const EarningsPage = () => {
    const [filteredEarnings, setFilteredEarnings] = useState(distributorEarnings);
    const [sortBy, setSortBy] = useState<'date' | 'amount' | 'points'>('date');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
    const [searchTerm, setSearchTerm] = useState('');
    const [dateFilter, setDateFilter] = useState('all');

    // Filtreleme ve sıralama fonksiyonu
    const handleSort = (field: 'date' | 'amount' | 'points') => {
        const newOrder = sortBy === field && sortOrder === 'desc' ? 'asc' : 'desc';
        setSortBy(field);
        setSortOrder(newOrder);

        const sorted = [...filteredEarnings].sort((a, b) => {
            let aValue: any, bValue: any;

            switch (field) {
                case 'date':
                    aValue = new Date(a.date);
                    bValue = new Date(b.date);
                    break;
                case 'amount':
                    aValue = a.amount;
                    bValue = b.amount;
                    break;
                case 'points':
                    aValue = a.points;
                    bValue = b.points;
                    break;
                default:
                    return 0;
            }

            if (newOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        setFilteredEarnings(sorted);
    };

    // Arama ve filtreleme
    const handleSearch = (term: string) => {
        setSearchTerm(term);
        let filtered = distributorEarnings;

        if (term) {
            filtered = filtered.filter(earning =>
                earning.reference.toLowerCase().includes(term.toLowerCase())
            );
        }

        if (dateFilter !== 'all') {
            const now = new Date();
            const filterDate = new Date();

            switch (dateFilter) {
                case 'week':
                    filterDate.setDate(now.getDate() - 7);
                    break;
                case 'month':
                    filterDate.setMonth(now.getMonth() - 1);
                    break;
                case 'quarter':
                    filterDate.setMonth(now.getMonth() - 3);
                    break;
            }

            filtered = filtered.filter(earning =>
                new Date(earning.date) >= filterDate
            );
        }

        setFilteredEarnings(filtered);
    };

    const handleDateFilter = (filter: string) => {
        setDateFilter(filter);
        handleSearch(searchTerm);
    };

    // Toplam kazanç hesaplama (filtrelenmiş veriler için)
    const totalFilteredEarnings = filteredEarnings.reduce((sum, earning) => sum + earning.amount, 0);
    const totalFilteredPoints = filteredEarnings.reduce((sum, earning) => sum + earning.points, 0);

    return (
        <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                Kazanç Detayları
                            </h1>
                            <p className="text-gray-600">
                                Tüm kazanç geçmişinizi detaylı olarak görüntüleyin
                            </p>
                        </div>
                        <div className="mt-4 sm:mt-0">
                            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <Download className="h-4 w-4 mr-2" />
                                Rapor İndir
                            </button>
                        </div>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Kazanç</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    ₺{distributorDashboard.totalEarnings.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                                </p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-full">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Filtrelenmiş Kazanç</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    ₺{totalFilteredEarnings.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                                </p>
                            </div>
                            <div className="bg-blue-100 p-3 rounded-full">
                                <TrendingUp className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Puan</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {totalFilteredPoints.toLocaleString('tr-TR')}
                                </p>
                            </div>
                            <div className="bg-purple-100 p-3 rounded-full">
                                <Star className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">İşlem Sayısı</p>
                                <p className="text-2xl font-bold text-gray-900">{filteredEarnings.length}</p>
                            </div>
                            <div className="bg-orange-100 p-3 rounded-full">
                                <Eye className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters and Search */}
                <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4">

                        {/* Search */}
                        <div className="flex-1 max-w-md">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Referans ara..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 placeholder-gray-500 text-black"
                                />
                            </div>
                        </div>

                        {/* Date Filter */}
                        <div className="flex items-center space-x-2">
                            <Calendar className="h-5 w-5 text-gray-400" />
                            <select
                                value={dateFilter}
                                onChange={(e) => handleDateFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-500 "
                            >
                                <option value="all">Tüm Zamanlar</option>
                                <option value="week">Son 7 Gün</option>
                                <option value="month">Son 30 Gün</option>
                                <option value="quarter">Son 3 Ay</option>
                            </select>
                        </div>

                        {/* Reset Filters */}
                        <button
                            onClick={() => {
                                setSearchTerm('');
                                setDateFilter('all');
                                setFilteredEarnings(distributorEarnings);
                            }}
                            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                        >
                            Filtreleri Temizle
                        </button>
                    </div>
                </div>

                {/* Earnings Table */}
                <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Kazanç Geçmişi</h3>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('date')}
                                    >
                                        <div className="flex items-center space-x-1">
                                            <span>Tarih</span>
                                            <ArrowUpDown className="h-4 w-4" />
                                        </div>
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center space-x-1">
                                            <User className="h-4 w-4" />
                                            <span>Referans</span>
                                        </div>
                                    </th>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('points')}
                                    >
                                        <div className="flex items-center space-x-1">
                                            <Star className="h-4 w-4" />
                                            <span>Puan</span>
                                            <ArrowUpDown className="h-4 w-4" />
                                        </div>
                                    </th>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('amount')}
                                    >
                                        <div className="flex items-center space-x-1">
                                            <DollarSign className="h-4 w-4" />
                                            <span>Miktar</span>
                                            <ArrowUpDown className="h-4 w-4" />
                                        </div>
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Seviye
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Yüzde
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredEarnings.map((earning) => (
                                    <tr key={earning.id} className="hover:bg-gray-50 transition-colors">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {new Date(earning.date).toLocaleDateString('tr-TR', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric'
                                                })}
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {new Date(earning.date).toLocaleDateString('tr-TR', {
                                                    weekday: 'long'
                                                })}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                                                    <span className="text-white font-semibold text-xs">
                                                        {earning.reference.split(' ').map(n => n[0]).join('')}
                                                    </span>
                                                </div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {earning.reference}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <Star className="h-4 w-4 text-yellow-500 mr-1" />
                                                <span className="text-sm font-medium text-gray-900">
                                                    {earning.points}
                                                </span>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-bold text-green-600">
                                                ₺{earning.amount.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${earning.level === 1
                                                ? 'bg-green-100 text-green-800'
                                                : earning.level === 2
                                                    ? 'bg-blue-100 text-blue-800'
                                                    : 'bg-gray-100 text-gray-800'
                                                }`}>
                                                Seviye {earning.level}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                %{earning.percentage}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {filteredEarnings.length === 0 && (
                        <div className="text-center py-12">
                            <div className="text-gray-500">
                                <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    Kazanç bulunamadı
                                </h3>
                                <p className="text-gray-500">
                                    Seçilen kriterlere uygun kazanç kaydı bulunmuyor.
                                </p>
                            </div>
                        </div>
                    )}

                    {/* Table Footer */}
                    {filteredEarnings.length > 0 && (
                        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <div className="text-sm text-gray-700">
                                    Toplam <span className="font-medium">{filteredEarnings.length}</span> kazanç kaydı gösteriliyor
                                </div>
                                <div className="mt-2 sm:mt-0 text-sm font-medium text-gray-900">
                                    Toplam: ₺{totalFilteredEarnings.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                                    <span className="text-gray-500 ml-2">
                                        • {totalFilteredPoints} puan
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Back to Panel */}
                <div className="mt-8 text-center">
                    <a
                        href="/panel"
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
                    >
                        Distribütör Paneline Dön
                    </a>
                </div>

            </div>
        </div>
    );
};

export default EarningsPage; 