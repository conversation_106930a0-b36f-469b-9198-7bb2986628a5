'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    X,
    User,
    Calendar,
    Star,
    Award,
    TrendingUp,
    Target,
    Mail,
    Phone,
    MapPin,
    Clock,
    Activity,
    Users,
    DollarSign,
    Trophy,
    Zap,
    ChevronRight
} from 'lucide-react';
import { TeamMember } from '@/types';

interface TeamMemberDetailModalProps {
    member: TeamMember | null;
    isOpen: boolean;
    onClose: () => void;
}

const TeamMemberDetailModal: React.FC<TeamMemberDetailModalProps> = ({ member, isOpen, onClose }) => {
    const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'contact'>('overview');

    // Modal açıldığında body scroll'unu engelle
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        // Cleanup function - component unmount olduğunda da temizle
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    if (!isOpen || !member) return null;

    // Örnek performans verileri (gerçek uygulamada bu veriler API'den gelecek)
    const performanceData = {
        monthlyPoints: Math.floor(Math.random() * 200) + 50,
        weeklyActivity: Math.floor(Math.random() * 40) + 60,
        totalSales: Math.floor(Math.random() * 5000) + 1000,
        teamMembers: Math.floor(Math.random() * 10) + 1,
        monthlyGrowth: Math.floor(Math.random() * 30) + 5,
        achievements: [
            { title: 'İlk Satış', date: '2023-02-15', icon: Trophy },
            { title: 'Yüksek Performans', date: '2023-03-10', icon: Star },
            { title: 'Ekip Lideri', date: '2023-04-05', icon: Users }
        ],
        recentActivity: [
            { action: 'Yeni ürün satışı', date: '2024-01-15', points: 25 },
            { action: 'Ekip üyesi ekledi', date: '2024-01-12', points: 50 },
            { action: 'Hedef tamamlandı', date: '2024-01-10', points: 100 }
        ]
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const memberSince = Math.floor((new Date().getTime() - new Date(member.joinDate).getTime()) / (1000 * 60 * 60 * 24));

    const getLevelInfo = (level: number) => {
        switch (level) {
            case 1:
                return { name: 'Bronz Üye', color: 'from-amber-400 to-yellow-600', bgColor: 'bg-amber-100', textColor: 'text-amber-800' };
            case 2:
                return { name: 'Gümüş Üye', color: 'from-gray-400 to-gray-600', bgColor: 'bg-gray-100', textColor: 'text-gray-800' };
            case 3:
                return { name: 'Altın Üye', color: 'from-yellow-400 to-yellow-600', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800' };
            default:
                return { name: 'Üye', color: 'from-blue-400 to-blue-600', bgColor: 'bg-blue-100', textColor: 'text-blue-800' };
        }
    };

    const levelInfo = getLevelInfo(member.level);

    return (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50">
            <motion.div
                className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
            >
                {/* Modal Header */}
                <div className="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <span className="text-white font-bold text-xl">
                                    {member.firstName.charAt(0)}{member.lastName.charAt(0)}
                                </span>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-white">
                                    {member.firstName} {member.lastName}
                                </h2>
                                <p className="text-purple-100">
                                    {levelInfo.name}
                                </p>
                                <div className="flex items-center mt-1">
                                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${member.isActive
                                        ? 'bg-green-500 bg-opacity-90 text-white border-green-400'
                                        : 'bg-red-500 bg-opacity-90 text-white border-red-400'
                                        }`}>
                                        {member.isActive ? 'Aktif' : 'Pasif'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-white/20 hover:bg-opacity-20 rounded-lg transition-colors text-white"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                {/* Tab Navigation */}
                <div className="border-b border-gray-200">
                    <nav className="flex px-6">
                        <button
                            onClick={() => setActiveTab('overview')}
                            className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors ${activeTab === 'overview'
                                ? 'border-purple-500 text-purple-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            Genel Bakış
                        </button>
                        <button
                            onClick={() => setActiveTab('performance')}
                            className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors ${activeTab === 'performance'
                                ? 'border-purple-500 text-purple-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            Performans
                        </button>
                        <button
                            onClick={() => setActiveTab('contact')}
                            className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors ${activeTab === 'contact'
                                ? 'border-purple-500 text-purple-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            İletişim
                        </button>
                    </nav>
                </div>

                {/* Modal Content */}
                <div className="p-6 max-h-[60vh] overflow-y-auto">
                    {activeTab === 'overview' && (
                        <div className="space-y-6">
                            {/* Stats Grid */}
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-purple-600">Toplam Puan</p>
                                            <p className="text-2xl font-bold text-purple-900">{member.points.toLocaleString('tr-TR')}</p>
                                        </div>
                                        <Star className="h-8 w-8 text-purple-500" />
                                    </div>
                                </div>

                                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">Seviye</p>
                                            <p className="text-2xl font-bold text-blue-900">{member.level}</p>
                                        </div>
                                        <Award className="h-8 w-8 text-blue-500" />
                                    </div>
                                </div>

                                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-green-600">Üyelik Süresi</p>
                                            <p className="text-2xl font-bold text-green-900">{memberSince}</p>
                                            <p className="text-xs text-green-600">gün</p>
                                        </div>
                                        <Calendar className="h-8 w-8 text-green-500" />
                                    </div>
                                </div>

                                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-orange-600">Ekip Boyutu</p>
                                            <p className="text-2xl font-bold text-orange-900">{performanceData.teamMembers}</p>
                                        </div>
                                        <Users className="h-8 w-8 text-orange-500" />
                                    </div>
                                </div>
                            </div>

                            {/* Member Info */}
                            <div className="bg-gray-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Üye Bilgileri</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-4">
                                        <div className="flex items-center">
                                            <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Katılım Tarihi</p>
                                                <p className="text-sm text-gray-600">{formatDate(member.joinDate)}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center">
                                            <Award className="h-5 w-5 text-gray-400 mr-3" />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Distribütör Seviyesi</p>
                                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${levelInfo.bgColor} ${levelInfo.textColor} mt-1`}>
                                                    {levelInfo.name}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="space-y-4">
                                        <div className="flex items-center">
                                            <Activity className="h-5 w-5 text-gray-400 mr-3" />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Durum</p>
                                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 ${member.isActive
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                                    }`}>
                                                    {member.isActive ? 'Aktif Üye' : 'Pasif Üye'}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex items-center">
                                            <Star className="h-5 w-5 text-gray-400 mr-3" />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Toplam Puan</p>
                                                <p className="text-sm text-gray-600">{member.points.toLocaleString('tr-TR')} puan</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Hızlı İşlemler</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <button className="flex items-center justify-center px-4 py-3 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors">
                                        <Mail className="h-5 w-5 mr-2" />
                                        Mesaj Gönder
                                    </button>
                                    <button className="flex items-center justify-center px-4 py-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                                        <Phone className="h-5 w-5 mr-2" />
                                        Telefon Et
                                    </button>
                                    <button className="flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                                        <TrendingUp className="h-5 w-5 mr-2" />
                                        Rapor Görüntüle
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'performance' && (
                        <div className="space-y-6">
                            {/* Performance Stats */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">Aylık Puan</p>
                                            <p className="text-xl font-bold text-blue-900">{performanceData.monthlyPoints}</p>
                                        </div>
                                        <Zap className="h-6 w-6 text-blue-500" />
                                    </div>
                                </div>

                                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-green-600">Haftalık Aktivite</p>
                                            <p className="text-xl font-bold text-green-900">{performanceData.weeklyActivity}%</p>
                                        </div>
                                        <Activity className="h-6 w-6 text-green-500" />
                                    </div>
                                </div>

                                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-purple-600">Toplam Satış</p>
                                            <p className="text-xl font-bold text-purple-900">₺{performanceData.totalSales.toLocaleString('tr-TR')}</p>
                                        </div>
                                        <DollarSign className="h-6 w-6 text-purple-500" />
                                    </div>
                                </div>

                                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-orange-600">Aylık Büyüme</p>
                                            <p className="text-xl font-bold text-orange-900">%{performanceData.monthlyGrowth}</p>
                                        </div>
                                        <TrendingUp className="h-6 w-6 text-orange-500" />
                                    </div>
                                </div>
                            </div>

                            {/* Achievements */}
                            <div className="bg-gray-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Başarılar</h3>
                                <div className="space-y-3">
                                    {performanceData.achievements.map((achievement, index) => (
                                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
                                            <div className="flex items-center">
                                                <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                                    <achievement.icon className="h-5 w-5 text-yellow-600" />
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{achievement.title}</p>
                                                    <p className="text-sm text-gray-500">{formatDate(achievement.date)}</p>
                                                </div>
                                            </div>
                                            <Trophy className="h-5 w-5 text-yellow-500" />
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Recent Activity */}
                            <div className="bg-gray-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Son Aktiviteler</h3>
                                <div className="space-y-3">
                                    {performanceData.recentActivity.map((activity, index) => (
                                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
                                            <div className="flex items-center">
                                                <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{activity.action}</p>
                                                    <p className="text-sm text-gray-500">{formatDate(activity.date)}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center">
                                                <span className="text-sm font-medium text-green-600">+{activity.points} puan</span>
                                                <ChevronRight className="h-4 w-4 text-gray-400 ml-1" />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'contact' && (
                        <div className="space-y-6">
                            {/* Contact Info */}
                            <div className="bg-gray-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">İletişim Bilgileri</h3>
                                <div className="space-y-4">
                                    <div className="flex items-start">
                                        <Mail className="h-5 w-5 text-gray-400 mr-3 mt-1" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">E-posta</p>
                                            <p className="text-sm text-gray-600">{member.firstName.toLowerCase()}.{member.lastName.toLowerCase()}@sayglobal.com</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start">
                                        <Phone className="h-5 w-5 text-gray-400 mr-3 mt-1" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">Telefon</p>
                                            <p className="text-sm text-gray-600">+90 5XX XXX XX XX</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start">
                                        <MapPin className="h-5 w-5 text-gray-400 mr-3 mt-1" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">Adres</p>
                                            <p className="text-sm text-gray-600">İstanbul, Türkiye</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Communication Options */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">İletişim Seçenekleri</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <button className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <Mail className="h-5 w-5 mr-2" />
                                        E-posta Gönder
                                    </button>
                                    <button className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                        <Phone className="h-5 w-5 mr-2" />
                                        Telefon Et
                                    </button>
                                    <button className="flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                        <User className="h-5 w-5 mr-2" />
                                        Profil Detayları
                                    </button>
                                    <button className="flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                                        <Target className="h-5 w-5 mr-2" />
                                        Hedef Belirle
                                    </button>
                                </div>
                            </div>

                            {/* Recent Interactions */}
                            <div className="bg-gray-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Son İletişim Geçmişi</h3>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                                        <div className="flex items-center">
                                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <Mail className="h-4 w-4 text-blue-600" />
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900">E-posta gönderildi</p>
                                                <p className="text-sm text-gray-500">2 gün önce</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                                        <div className="flex items-center">
                                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <Phone className="h-4 w-4 text-green-600" />
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900">Telefon görüşmesi</p>
                                                <p className="text-sm text-gray-500">1 hafta önce</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </motion.div>
        </div>
    );
};

export default TeamMemberDetailModal;
