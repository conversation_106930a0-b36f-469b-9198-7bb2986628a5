'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { FavoriteItem, FavoritesContextType, Product } from '@/types';
import { mockFavorites } from '@/data/mocks/products';

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

export function FavoritesProvider({ children }: { children: React.ReactNode }) {
    const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
    const [isInitialized, setIsInitialized] = useState(false);

    // LocalStorage'dan favorileri yükle (gerçek uygulamada API'den gelir)
    useEffect(() => {
        if (typeof window !== 'undefined' && !isInitialized) {
            const savedFavorites = localStorage.getItem('sayGlobalFavorites');
            if (savedFavorites) {
                try {
                    const parsedFavorites = JSON.parse(savedFavorites);
                    setFavorites(parsedFavorites);
                } catch (error) {
                    console.error('Favori verileri yüklenirken hata:', error);
                    // Hata durumunda mock verileri kullan
                    setFavorites(mockFavorites);
                }
            } else {
                // İlk kez giriş yapıyorsa mock verileri yükle
                setFavorites(mockFavorites);
            }
            setIsInitialized(true);
        }
    }, [isInitialized]); // isInitialized değiştiğinde çalış

    // Favori değişikliklerini LocalStorage'a kaydet
    useEffect(() => {
        if (typeof window !== 'undefined' && isInitialized) {
            localStorage.setItem('sayGlobalFavorites', JSON.stringify(favorites));
        }
    }, [favorites, isInitialized]);

    const addToFavorites = useCallback((product: Product) => {
        setFavorites(currentFavorites => {
            const existingFavorite = currentFavorites.find(fav => fav.productId === product.id);

            if (existingFavorite) {
                // Zaten favorilerde var, bir şey yapma
                return currentFavorites;
            }

            // Yeni favori ekle
            const newFavorite: FavoriteItem = {
                id: Date.now(), // Basit ID üretimi
                productId: product.id,
                product: product,
                addedAt: new Date().toISOString()
            };

            return [...currentFavorites, newFavorite];
        });
    }, []);

    const removeFromFavorites = useCallback((productId: number) => {
        setFavorites(currentFavorites =>
            currentFavorites.filter(fav => fav.productId !== productId)
        );
    }, []);

    const isFavorite = useCallback((productId: number) => {
        return favorites.some(fav => fav.productId === productId);
    }, [favorites]);

    const getFavoritesCount = useCallback(() => {
        return favorites.length;
    }, [favorites]);

    const value: FavoritesContextType = useMemo(() => ({
        favorites,
        addToFavorites,
        removeFromFavorites,
        isFavorite,
        getFavoritesCount
    }), [favorites, addToFavorites, removeFromFavorites, isFavorite, getFavoritesCount]);

    return (
        <FavoritesContext.Provider value={value}>
            {children}
        </FavoritesContext.Provider>
    );
}

export function useFavorites() {
    const context = useContext(FavoritesContext);
    if (context === undefined) {
        throw new Error('useFavorites must be used within a FavoritesProvider');
    }
    return context;
} 