'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { PendingProduct, AdminProduct } from '@/types';

interface ProductApprovalModalProps {
    product: PendingProduct | AdminProduct | null;
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (adminNotes: string) => void;
    action: 'approve' | 'reject';
    isLoading?: boolean;
}

const ProductApprovalModal: React.FC<ProductApprovalModalProps> = ({
    product,
    isOpen,
    onClose,
    onConfirm,
    action,
    isLoading = false
}) => {
    const [adminNotes, setAdminNotes] = useState('');
    const [validationError, setValidationError] = useState('');

    if (!isOpen || !product) return null;

    const handleConfirm = () => {
        // Reddetme işleminde not zorunlu kontrolü
        if (action === 'reject' && !adminNotes.trim()) {
            setValidationError('Red sebebi zorunludur.');
            return;
        }

        setValidationError('');
        onConfirm(adminNotes);
        setAdminNotes('');
    };

    const handleClose = () => {
        setAdminNotes('');
        setValidationError('');
        onClose();
    };

    return (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50">
            <motion.div
                className="bg-white rounded-lg max-w-md w-full p-6"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
            >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {action === 'approve' ? 'Ürünü Onayla' : 'Ürünü Reddet'}
                </h3>

                <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">
                        <strong>{'title' in product ? product.title : product.name}</strong> adlı ürünü {action === 'approve' ? 'onaylamak' : 'reddetmek'} istediğinizden emin misiniz?
                    </p>
                </div>

                <div className="mb-4">
                    <label htmlFor="adminNotes" className="block text-sm font-medium text-gray-700 mb-2">
                        {action === 'approve' ? 'Onay Notu (İsteğe bağlı)' : 'Red Sebebi *'}
                    </label>
                    <textarea
                        id="adminNotes"
                        value={adminNotes}
                        onChange={(e) => {
                            setAdminNotes(e.target.value);
                            if (validationError) setValidationError('');
                        }}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:border-transparent text-black ${validationError
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-red-500'
                            }`}
                        placeholder={action === 'approve' ? 'Onay ile ilgili notunuz...' : 'Red sebebinizi açıklayın...'}
                        required={action === 'reject'}
                    />
                    {validationError && (
                        <p className="mt-1 text-sm text-red-600">{validationError}</p>
                    )}
                </div>

                <div className="flex space-x-3">
                    <button
                        onClick={handleClose}
                        disabled={isLoading}
                        className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-md transition-colors"
                    >
                        İptal
                    </button>
                    <button
                        onClick={handleConfirm}
                        disabled={isLoading}
                        className={`flex-1 px-4 py-2 text-white rounded-md transition-colors flex items-center justify-center ${action === 'approve'
                            ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-400'
                            : 'bg-red-600 hover:bg-red-700 disabled:bg-red-400'
                            }`}
                    >
                        {isLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                İşleniyor...
                            </>
                        ) : (
                            action === 'approve' ? 'Onayla' : 'Reddet'
                        )}
                    </button>
                </div>
            </motion.div>
        </div>
    );
};

export default ProductApprovalModal; 