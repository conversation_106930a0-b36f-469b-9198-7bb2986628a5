'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCategories, useSubCategories } from '@/hooks/useProductsPage';
import { useProductsPageStore } from '@/stores/productsPageStore';
import { ChevronDown, Grid3X3, Smartphone, Laptop, Home, Shirt, Book, Gamepad2, Car, Heart, Menu, RotateCcw } from 'lucide-react';

// Kategori ikonları mapping
const categoryIcons: { [key: string]: any } = {
    'Elektronik': Smartphone,
    'Bilgisayar': Laptop,
    'Ev & Yaşam': Home,
    'Giyim': Shirt,
    'Kitap': Book,
    'Oyun': Gamepad2,
    'Otomotiv': Car,
    'Sağlık': Heart,
    'default': Grid3X3
};

export default function CategoryNavigation() {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [hoveredCategoryId, setHoveredCategoryId] = useState<number | null>(null);
    const [hoveredButtonId, setHoveredButtonId] = useState<number | null>(null);
    const [expandedCategoryId, setExpandedCategoryId] = useState<number | null>(null);
    const [isMobile, setIsMobile] = useState(false);
    const [mobileSubCategoriesFor, setMobileSubCategoriesFor] = useState<number | null>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Store
    const { selectedCategoryId, selectedSubCategoryId } = useProductsPageStore();
    const { setSelectedCategory, setSelectedSubCategory, resetFilters } = useProductsPageStore();

    // Hooks
    const { data: categories = [], isLoading: categoriesLoading } = useCategories();
    const { data: hoveredSubCategories = [] } = useSubCategories(hoveredCategoryId);
    const { data: selectedSubCategories = [] } = useSubCategories(selectedCategoryId);
    const { data: mobileSubCategories = [] } = useSubCategories(mobileSubCategoriesFor);

    // Helper functions
    const getCategoryIcon = (categoryName: string) => {
        const IconComponent = categoryIcons[categoryName] || categoryIcons.default;
        return IconComponent;
    };

    const getSelectedCategoryName = () => {
        if (selectedSubCategoryId) {
            const subCategory = selectedSubCategories.find(sub => sub.id === selectedSubCategoryId);
            return subCategory?.name || 'Alt Kategori';
        }
        if (selectedCategoryId) {
            const category = categories.find(cat => cat.id === selectedCategoryId);
            return category?.name || 'Kategori';
        }
        return 'Tüm Kategoriler';
    };

    // Event handlers
    const handleDropdownToggle = () => {
        setIsDropdownOpen(!isDropdownOpen);
        setHoveredCategoryId(null);
    };

    const handleCategoryHover = (categoryId: number) => {
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
        }
        // Daha uzun delay ile hover'ı daha az hassas yap
        hoverTimeoutRef.current = setTimeout(() => {
            setHoveredCategoryId(categoryId);
        }, 500); // 500ms delay - daha az hassas
    };

    const handleCategoryLeave = () => {
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
        }
        // Alt kategorilere geçiş için çok daha uzun süre ver
        hoverTimeoutRef.current = setTimeout(() => {
            setHoveredCategoryId(null);
        }, 800); // 800ms delay - alt kategorilere rahat geçiş
    };

    const handleSubCategoryAreaEnter = () => {
        // Alt kategori alanına girildiğinde timeout'u iptal et
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
        }
    };

    const handleCategoryClick = (categoryId: number) => {
        if (selectedCategoryId === categoryId) {
            setSelectedCategory(null);
            setSelectedSubCategory(null);
        } else {
            setSelectedCategory(categoryId);
            setSelectedSubCategory(null);
        }
        setIsDropdownOpen(false);
        setHoveredCategoryId(null);
    };

    const handleSubCategoryClick = (subCategoryId: number) => {
        // Alt kategori seçildiğinde ana kategoriyi de ayarla
        const parentCategoryId = isMobile ? mobileSubCategoriesFor : hoveredCategoryId;
        if (parentCategoryId) {
            setSelectedCategory(parentCategoryId);
        }
        setSelectedSubCategory(subCategoryId);
        setIsDropdownOpen(false);
        setHoveredCategoryId(null);
        setExpandedCategoryId(null);
        setMobileSubCategoriesFor(null);
    };

    const handleAllCategoriesClick = () => {
        setSelectedCategory(null);
        setSelectedSubCategory(null);
        setIsDropdownOpen(false);
        setHoveredCategoryId(null);
    };

    // Mobile detection
    useEffect(() => {
        const checkMobile = () => {
            const mobile = window.innerWidth < 768;
            console.log('Mobile detection:', mobile, 'Width:', window.innerWidth);
            setIsMobile(mobile);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);

        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // Mobile category toggle
    const handleMobileCategoryToggle = (categoryId: number) => {
        console.log('Mobile category toggle:', categoryId, 'Current expanded:', expandedCategoryId);
        if (expandedCategoryId === categoryId) {
            // İkinci tıklama: Kategoriyi seç ve dropdown'ı kapat
            setSelectedCategory(categoryId);
            setSelectedSubCategory(null);
            setExpandedCategoryId(null);
            setMobileSubCategoriesFor(null);
            setIsDropdownOpen(false);
        } else {
            // İlk tıklama: Sadece alt kategorileri aç, kategoriyi seçme
            setExpandedCategoryId(categoryId);
            setMobileSubCategoriesFor(categoryId);
        }
    };

    // Click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
                buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
                setHoveredCategoryId(null);
                setExpandedCategoryId(null);
                setMobileSubCategoriesFor(null);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            if (hoverTimeoutRef.current) {
                clearTimeout(hoverTimeoutRef.current);
            }
        };
    }, []);

    if (categoriesLoading) {
        return (
            <div className="mb-8">
                <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
                    <div className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="mb-8 relative">
            {/* Modern Dropdown Button */}
            <motion.div
                className="bg-white rounded-xl shadow-lg border border-gray-100 relative z-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.1 } }}
                whileHover={{
                    scale: 1.02,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
                transition={{
                    duration: 0.2,
                    ease: "easeOut"
                }}
            >
                <motion.button
                    ref={buttonRef}
                    onClick={handleDropdownToggle}
                    whileTap={{ scale: 0.98 }}
                    transition={{
                        duration: 0.2,
                        ease: "easeOut"
                    }}
                    className="w-full flex items-center justify-between px-6 py-4 text-left rounded-xl"
                >
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                            <Menu className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                            <span className="text-sm text-gray-500 block">Kategori Seçin</span>
                            <span className="font-semibold text-gray-900 text-lg">
                                {getSelectedCategoryName()}
                            </span>
                        </div>
                    </div>
                    <motion.div
                        animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                    >
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                    </motion.div>
                </motion.button>
            </motion.div>

            {/* Modern Mega Dropdown */}
            <AnimatePresence>
                {isDropdownOpen && (
                    <motion.div
                        ref={dropdownRef}
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-gray-100 z-50 overflow-hidden"
                    >
                        <div className="max-h-96 overflow-y-auto">
                            {/* Tüm Kategoriler Seçeneği */}
                            <motion.button
                                onClick={handleAllCategoriesClick}
                                whileHover={{ backgroundColor: '#f8fafc' }}
                                className={`w-full flex items-center px-6 py-4 text-left transition-all duration-200 border-b border-gray-100 ${!selectedCategoryId && !selectedSubCategoryId
                                    ? 'bg-purple-50 text-purple-700'
                                    : 'text-gray-700 hover:bg-gray-50'
                                    }`}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className={`p-2 rounded-lg ${!selectedCategoryId && !selectedSubCategoryId
                                        ? 'bg-purple-100'
                                        : 'bg-gray-100'
                                        }`}>
                                        <Grid3X3 className={`h-5 w-5 ${!selectedCategoryId && !selectedSubCategoryId
                                            ? 'text-purple-600'
                                            : 'text-gray-600'
                                            }`} />
                                    </div>
                                    <div>
                                        <span className="font-medium">Tüm Kategoriler</span>
                                        <span className="text-sm text-gray-500 block">Tüm ürünleri görüntüle</span>
                                    </div>
                                </div>
                            </motion.button>

                            {/* Kategori Listesi */}
                            <div className="py-2">
                                {categories.map((category) => {
                                    const IconComponent = getCategoryIcon(category.name);
                                    const isSelected = selectedCategoryId === category.id;

                                    return (
                                        <div key={category.id} className="relative">
                                            <motion.button
                                                onClick={() => {
                                                    console.log('Category clicked:', category.id, 'isMobile:', isMobile);
                                                    if (isMobile) {
                                                        handleMobileCategoryToggle(category.id);
                                                    } else {
                                                        handleCategoryClick(category.id);
                                                    }
                                                }}
                                                onMouseEnter={() => {
                                                    if (!isMobile) {
                                                        handleCategoryHover(category.id);
                                                        setHoveredButtonId(category.id);
                                                    }
                                                }}
                                                onMouseLeave={() => {
                                                    if (!isMobile) {
                                                        handleCategoryLeave();
                                                        setHoveredButtonId(null);
                                                    }
                                                }}
                                                initial={{
                                                    backgroundColor: isSelected ? '#f3f4f6' : 'transparent'
                                                }}
                                                animate={{
                                                    backgroundColor: isSelected ? '#f3f4f6' : 'transparent'
                                                }}
                                                whileHover={{
                                                    backgroundColor: '#e9d5ff',
                                                    color: '#7c3aed',
                                                    scale: 1.01
                                                }}
                                                whileTap={{
                                                    scale: 0.99
                                                }}
                                                transition={{
                                                    duration: 0.15,
                                                    ease: "easeOut"
                                                }}
                                                className={`w-full flex items-center justify-between px-6 py-3 text-left ${isSelected
                                                    ? 'text-purple-700'
                                                    : 'text-gray-700'
                                                    }`}
                                            >
                                                <div className="flex items-center space-x-3">
                                                    <div className={`p-2 rounded-lg ${isSelected
                                                        ? 'bg-purple-100'
                                                        : hoveredButtonId === category.id
                                                            ? 'bg-purple-100'
                                                            : 'bg-gray-100'
                                                        }`}>
                                                        <IconComponent className={`h-5 w-5 ${isSelected
                                                            ? 'text-purple-600'
                                                            : hoveredButtonId === category.id
                                                                ? 'text-purple-600'
                                                                : 'text-gray-600'
                                                            }`} />
                                                    </div>
                                                    <span className="font-medium">{category.name}</span>
                                                </div>
                                                <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isMobile
                                                    ? (expandedCategoryId === category.id ? 'rotate-180' : '')
                                                    : (hoveredCategoryId === category.id ? 'rotate-180' : '')
                                                    }`} />
                                            </motion.button>

                                            {/* Alt Kategoriler */}
                                            <AnimatePresence>
                                                {((isMobile && expandedCategoryId === category.id) ||
                                                    (!isMobile && hoveredCategoryId === category.id)) &&
                                                    (isMobile ? mobileSubCategories.length > 0 : hoveredSubCategories.length > 0) && (
                                                        <motion.div
                                                            initial={{ opacity: 0, height: 0 }}
                                                            animate={{ opacity: 1, height: 'auto' }}
                                                            exit={{ opacity: 0, height: 0 }}
                                                            transition={{ duration: 0.2 }}
                                                            onMouseEnter={!isMobile ? handleSubCategoryAreaEnter : undefined}
                                                            onMouseLeave={!isMobile ? handleCategoryLeave : undefined}
                                                            className="bg-gray-50 border-t border-gray-100"
                                                        >
                                                            <div className="py-2">
                                                                {(isMobile ? mobileSubCategories : hoveredSubCategories).map((subCategory) => (
                                                                    <motion.button
                                                                        key={subCategory.id}
                                                                        onClick={() => handleSubCategoryClick(subCategory.id)}
                                                                        initial={{
                                                                            backgroundColor: selectedSubCategoryId === subCategory.id ? '#ffffff' : 'transparent'
                                                                        }}
                                                                        animate={{
                                                                            backgroundColor: selectedSubCategoryId === subCategory.id ? '#ffffff' : 'transparent'
                                                                        }}
                                                                        whileHover={{
                                                                            x: 4,
                                                                            backgroundColor: '#ffffff',
                                                                            color: '#7c3aed',
                                                                            scale: 1.01
                                                                        }}
                                                                        whileTap={{
                                                                            scale: 0.99
                                                                        }}
                                                                        transition={{
                                                                            duration: 0.15,
                                                                            ease: "easeOut"
                                                                        }}
                                                                        onMouseEnter={!isMobile ? handleSubCategoryAreaEnter : undefined}
                                                                        className={`w-full flex items-center px-12 py-2 text-left ${selectedSubCategoryId === subCategory.id
                                                                            ? 'text-purple-700 font-medium shadow-sm'
                                                                            : 'text-gray-600'
                                                                            }`}
                                                                    >
                                                                        <span className="text-sm">{subCategory.name}</span>
                                                                    </motion.button>
                                                                ))}
                                                            </div>
                                                        </motion.div>
                                                    )}
                                            </AnimatePresence>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Seçili Kategori/Alt Kategori Göstergesi */}
            {(selectedCategoryId || selectedSubCategoryId) && (
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 flex items-center gap-2 flex-wrap"
                >
                    <div className="flex items-center gap-3">
                        <span className="text-sm text-gray-600">Aktif filtre:</span>

                        {/* Filtreleri Temizle Butonu */}
                        <motion.button
                            whileHover={{
                                scale: 1.02,
                                transition: { duration: 0.15, ease: "easeOut" }
                            }}
                            whileTap={{
                                scale: 0.98,
                                transition: { duration: 0.1, ease: "easeInOut" }
                            }}
                            transition={{
                                duration: 0.15,
                                ease: "easeInOut"
                            }}
                            onClick={resetFilters}
                            className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-xs font-medium"
                            title="Tüm filtreleri temizle"
                        >
                            <RotateCcw className="w-3 h-3" />
                            <span>Temizle</span>
                        </motion.button>
                    </div>

                    {selectedCategoryId && (
                        <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                            {categories.find(cat => cat.id === selectedCategoryId)?.name}
                            {!selectedSubCategoryId && (
                                <button
                                    onClick={() => setSelectedCategory(null)}
                                    className="text-purple-500 hover:text-purple-700 transition-colors"
                                >
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            )}
                        </span>
                    )}

                    {selectedSubCategoryId && (
                        <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                            {selectedSubCategories.find(sub => sub.id === selectedSubCategoryId)?.name}
                            <button
                                onClick={() => setSelectedSubCategory(null)}
                                className="text-blue-500 hover:text-blue-700 transition-colors"
                            >
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </span>
                    )}
                </motion.div>
            )}
        </div>
    );
}
