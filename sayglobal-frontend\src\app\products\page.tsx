'use client';

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useSearchParams } from "next/navigation";
import { X } from "lucide-react";
import { useProducts, useReferenceData } from '@/hooks/useProductsPage';
import { useProductsPageStore } from '@/stores/productsPageStore';
import CategoryNavigation from '@/components/CategoryNavigation';
import ProductCard from '@/components/ProductCard';
import CustomCheckbox from '@/components/CustomCheckbox';
import { ProductSortOption } from '@/types';
import { useDiscountRate } from '@/hooks/useDiscountRate';
import { useAuth } from '@/components/auth/AuthContext';
import { useCustomerPriceStore } from '@/stores/customerPriceStore';

export default function ProductsPage() {
    const searchParams = useSearchParams();
    const categorySlug = searchParams.get('category');

    // Local state
    // Auth and discount rate hooks
    const { user, isAuthenticated } = useAuth();
    const { data: discountRateData, isLoading: discountRateLoading } = useDiscountRate();

    // Global customer price state
    const { isCustomerPrice, setIsCustomerPrice, resetCustomerPrice, _hasHydrated } = useCustomerPriceStore();

    // Store
    const {
        priceRange,
        sortBy,
        isFilterOpen,
        selectedBrandIds,
        selectedFeatureValueIds,
        brands,
        featureDefinitions,
        featureValues,
        getCurrentFilterRequest
    } = useProductsPageStore();

    const {
        setFilterOpen,
        setPriceRange,
        setSortBy,
        setSelectedBrandIds,
        setSelectedFeatureValueIds,
        setBrands,
        setFeatureDefinitions,
        setFeatureValues
    } = useProductsPageStore();

    // API calls
    const filterRequest = getCurrentFilterRequest();
    const { data: productsResponse, isLoading: productsLoading, error: productsError } = useProducts(filterRequest);
    const { data: referenceDataResponse, isLoading: referenceDataLoading, error: referenceDataError } = useReferenceData();

    // Get products from API response
    const products = productsResponse?.data || [];

    // URL'deki kategori parametresini kontrol et (gelecekte kategori slug'ları için)
    useEffect(() => {
        if (categorySlug) {
            // TODO: Kategori slug'ını ID'ye çevir ve setSelectedCategory çağır
            console.log('Category slug from URL:', categorySlug);
        }
    }, [categorySlug]);

    // Reference data'yı store'a kaydet
    useEffect(() => {
        if (referenceDataResponse?.data) {
            setBrands(referenceDataResponse.data.brands);
            setFeatureDefinitions(referenceDataResponse.data.featureDefinitions);
            setFeatureValues(referenceDataResponse.data.featureValues);
        }
    }, [referenceDataResponse, setBrands, setFeatureDefinitions, setFeatureValues]);

    // Animasyon varyantları
    const container = {
        hidden: { opacity: 0 },
        show: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const item = {
        hidden: { opacity: 0, y: 20 },
        show: { opacity: 1, y: 0 }
    };



    return (
        <div className="container mx-auto px-4 py-8">
            {/* Başlık */}
            <motion.div
                className="mb-6 text-center"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                <h1 className="text-4xl font-bold">Ürünlerimiz</h1>
            </motion.div>

            {/* Kategori Navigation */}
            <CategoryNavigation />

            <div className="flex flex-col lg:flex-row gap-8">
                {/* Mobil filtre açma butonu */}
                <div className="lg:hidden mb-4">
                    <motion.button
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setFilterOpen(!isFilterOpen)}
                        className="w-full bg-white rounded-lg border border-gray-200 px-4 py-3 flex items-center justify-between shadow-sm"
                    >
                        <span className="font-medium text-gray-700">Filtreler</span>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className={`h-5 w-5 transition-transform  text-gray-700 ${isFilterOpen ? 'rotate-180' : ''}`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                    </motion.button>
                </div>

                {/* Sol Sidebar - Sadece Fiyat Filtresi */}
                <motion.aside
                    className={`lg:block lg:w-1/4 ${isFilterOpen ? 'block' : 'hidden'}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                >
                    <div className="bg-white rounded-xl shadow-md p-6 sticky top-24">
                        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
                            <h2 className="text-xl font-bold text-black">Filtreler</h2>
                            {(selectedBrandIds.length > 0 || selectedFeatureValueIds.length > 0) && (
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={() => {
                                        setSelectedBrandIds([]);
                                        setSelectedFeatureValueIds([]);
                                    }}
                                    className="flex items-center space-x-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                    title="Filtreleri Temizle"
                                >
                                    <X className="w-3 h-3" />
                                    <span>Temizle</span>
                                </motion.button>
                            )}
                        </div>

                        {/* Fiyat Aralığı Filtresi */}
                        <div className="mb-8">
                            <h3 className="font-semibold mb-4 text-gray-800">Fiyat Aralığı</h3>
                            <div className="space-y-4">
                                <div className="flex justify-between text-sm text-gray-700">
                                    <span>{priceRange[0]} ₺</span>
                                    <span>{priceRange[1]} ₺</span>
                                </div>
                                <input
                                    type="range"
                                    min="0"
                                    max="10000"
                                    step="100"
                                    value={priceRange[1]}
                                    onChange={(e) =>
                                        setPriceRange([priceRange[0], parseInt(e.target.value)])
                                    }
                                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-purple-600"
                                />
                                <div className="flex justify-between gap-4">
                                    <motion.div
                                        className="relative flex-1"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        <input
                                            type="number"
                                            min="0"
                                            max={priceRange[1] || 10000}
                                            value={priceRange[0]}
                                            onChange={(e) => {
                                                setPriceRange([
                                                    parseInt(e.target.value) || 0,
                                                    priceRange[1],
                                                ]);
                                            }}
                                            className="w-full p-2 pr-4 border border-gray-200 rounded-md text-sm text-gray-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                                        />
                                        <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-700 text-sm">₺</span>
                                    </motion.div>
                                    <motion.div
                                        className="relative flex-1"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        <input
                                            type="number"
                                            min={priceRange[0] || 0}
                                            max="10000"
                                            value={priceRange[1]}
                                            onChange={(e) => {
                                                setPriceRange([
                                                    priceRange[0],
                                                    parseInt(e.target.value) || 10000,
                                                ]);
                                            }}
                                            className="w-full p-2 pr-4 border border-gray-200 rounded-md text-sm text-gray-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                                        />
                                        <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-700 text-sm">₺</span>
                                    </motion.div>
                                </div>
                            </div>
                        </div>

                        {/* Marka Filtresi */}
                        <div className="mb-8">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="font-semibold text-gray-800">Marka</h3>
                                {selectedBrandIds.length > 0 && (
                                    <motion.button
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                        onClick={() => setSelectedBrandIds([])}
                                        className="flex items-center space-x-1 px-1.5 py-0.5 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                        title="Marka Filtrelerini Temizle"
                                    >
                                        <X className="w-3 h-3" />
                                    </motion.button>
                                )}
                            </div>
                            <div className="space-y-3 max-h-48 overflow-y-auto overflow-x-hidden">
                                {brands.map((brand) => (
                                    <CustomCheckbox
                                        key={brand.id}
                                        checked={selectedBrandIds.includes(brand.id)}
                                        onChange={(checked) => {
                                            if (checked) {
                                                setSelectedBrandIds([...selectedBrandIds, brand.id]);
                                            } else {
                                                setSelectedBrandIds(selectedBrandIds.filter(id => id !== brand.id));
                                            }
                                        }}
                                        label={brand.name}
                                        size="sm"
                                        className="hover:bg-gray-50 p-2 rounded-lg transition-colors"
                                    />
                                ))}
                            </div>
                        </div>

                        {/* Özellik Filtresi */}
                        <div className="mb-8">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="font-semibold text-gray-800">Özellikler</h3>
                                {selectedFeatureValueIds.length > 0 && (
                                    <motion.button
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                        onClick={() => setSelectedFeatureValueIds([])}
                                        className="flex items-center space-x-1 px-1.5 py-0.5 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                        title="Özellik Filtrelerini Temizle"
                                    >
                                        <X className="w-3 h-3" />
                                    </motion.button>
                                )}
                            </div>
                            <div className="space-y-4">
                                {featureDefinitions.map((featureDef) => {
                                    const relatedValues = featureValues.filter(fv => fv.featureDefinitionId === featureDef.id);
                                    if (relatedValues.length === 0) return null;

                                    return (
                                        <div key={featureDef.id} className="border-b border-gray-100 pb-4">
                                            <h4 className="font-medium text-gray-700 mb-3 truncate">{featureDef.name}</h4>
                                            <div className="space-y-2 max-h-32 overflow-y-auto overflow-x-hidden">
                                                {relatedValues.map((featureValue) => (
                                                    <CustomCheckbox
                                                        key={featureValue.id}
                                                        checked={selectedFeatureValueIds.includes(featureValue.id)}
                                                        onChange={(checked) => {
                                                            if (checked) {
                                                                setSelectedFeatureValueIds([...selectedFeatureValueIds, featureValue.id]);
                                                            } else {
                                                                setSelectedFeatureValueIds(selectedFeatureValueIds.filter(id => id !== featureValue.id));
                                                            }
                                                        }}
                                                        label={featureValue.name}
                                                        size="sm"
                                                        className="hover:bg-gray-50 p-1.5 rounded-lg transition-colors"
                                                    />
                                                ))}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Sıralama Filtresi */}
                        <div>
                            <h3 className="font-semibold mb-3 text-gray-800">Sıralama</h3>
                            <motion.select
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                value={sortBy}
                                onChange={(e) => setSortBy(parseInt(e.target.value) as ProductSortOption)}
                                className="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 text-gray-700"
                            >
                                <option value={ProductSortOption.Default}>Öne Çıkanlar</option>
                                <option value={ProductSortOption.PriceAsc}>Fiyat: Düşükten Yükseğe</option>
                                <option value={ProductSortOption.PriceDesc}>Fiyat: Yüksekten Düşüğe</option>
                                <option value={ProductSortOption.RatingDesc}>Puana Göre</option>
                            </motion.select>
                        </div>
                    </div>
                </motion.aside>

                {/* Ürünler */}
                <motion.div
                    className="lg:w-3/4"
                    variants={container}
                    initial="hidden"
                    animate="show"
                >
                    {/* Customer Price Checkbox - Sadece discount rate varsa göster */}
                    {_hasHydrated && !discountRateLoading && discountRateData?.discountRate && discountRateData.discountRate > 0 && (
                        <motion.div
                            className="mb-6 bg-white rounded-xl shadow-md p-4"
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                        >
                            <CustomCheckbox
                                checked={isCustomerPrice}
                                onChange={setIsCustomerPrice}
                                label="Müşteri Fiyatlarını Göster (Üye indirimi uygulanmaz)"
                                size="md"
                                className="flex items-center gap-3"
                            />
                            <p className="text-sm text-gray-600 mt-2 ml-8">
                                Bu seçenek aktif olduğunda üye indiriminiz (%{discountRateData.discountRate}) uygulanmaz ve ürünler müşteri fiyatları ile görüntülenir.
                            </p>
                        </motion.div>
                    )}

                    {(productsLoading || referenceDataLoading) ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            {[...Array(6)].map((_, i) => (
                                <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
                                    <div className="h-64 bg-gray-200"></div>
                                    <div className="p-5">
                                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                        <div className="h-3 bg-gray-200 rounded mb-4 w-2/3"></div>
                                        <div className="h-3 bg-gray-200 rounded mb-4"></div>
                                        <div className="h-8 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (productsError || referenceDataError) ? (
                        <motion.div
                            className="bg-white rounded-xl p-8 text-center shadow-md"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.3 }}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-16 w-16 mx-auto text-red-400 mb-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={1.5}
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <h3 className="text-xl font-bold mb-2">Hata Oluştu</h3>
                            <p className="text-gray-700 mb-4">
                                Ürünler yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.
                            </p>
                            <p className="text-sm text-red-600">{productsError?.message || referenceDataError?.message}</p>
                        </motion.div>
                    ) : products.length === 0 ? (
                        <motion.div
                            className="bg-white rounded-xl p-8 text-center shadow-md"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.3 }}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-16 w-16 mx-auto text-gray-400 mb-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={1.5}
                                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <h3 className="text-xl font-bold mb-2">Ürün Bulunamadı</h3>
                            <p className="text-gray-700 mb-4">
                                Seçtiğiniz filtrelere uygun ürün bulunmamaktadır. Lütfen
                                filtrelerinizi değiştirerek tekrar deneyin.
                            </p>
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                    // Store'dan resetFilters fonksiyonunu kullan
                                    const { resetFilters } = useProductsPageStore.getState();
                                    resetFilters();
                                }}
                                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                            >
                                Filtreleri Temizle
                            </motion.button>
                        </motion.div>
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            {products.map((product) => (
                                <motion.div
                                    key={product.id}
                                    variants={item}
                                    className="group"
                                >
                                    <ProductCard
                                        product={product}
                                    />
                                </motion.div>
                            ))}
                        </div>
                    )}
                </motion.div>
            </div>
        </div>
    );
} 