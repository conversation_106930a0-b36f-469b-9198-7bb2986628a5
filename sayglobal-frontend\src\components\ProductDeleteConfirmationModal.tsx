'use client';

import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 🚀 Modal store imports
import {
    useProductDeleteConfirmationModal,
    useProductDeleteConfirmationData,
    useModalActions
} from '@/stores/modalStore';

interface ProductDeleteConfirmationModalProps {
    // 🔄 BACKWARD COMPATIBILITY: Eski props hala destekleniyor
    isOpen?: boolean;
    onClose?: () => void;
    onConfirm?: () => void;
    productId?: number;
    productName?: string;
    brandName?: string;
    imageUrl?: string;
}

export default function ProductDeleteConfirmationModal({
    isOpen,
    onClose,
    onConfirm,
    productId: propProductId,
    productName: propProductName,
    brandName: propBrandName,
    imageUrl: propImageUrl
}: ProductDeleteConfirmationModalProps) {
    // 🚀 Modal store hooks
    const isModalOpen = useProductDeleteConfirmationModal();
    const modalData = useProductDeleteConfirmationData();
    const { closeProductDeleteConfirmation } = useModalActions();

    // 🎯 HYBRID APPROACH: Store'u önceliklendire, props'u fallback olarak kullan
    const modalOpen = isModalOpen || isOpen || false;
    const productId = modalData?.productId || propProductId;
    const productName = modalData?.productName || propProductName;
    const brandName = modalData?.brandName || propBrandName;
    const imageUrl = modalData?.imageUrl || propImageUrl;

    const handleClose = () => {
        // Store'dan açıldıysa store'u kapat
        if (isModalOpen) {
            closeProductDeleteConfirmation();
        }

        // Prop'dan açıldıysa callback'i çağır
        if (onClose) {
            onClose();
        }
    };

    const handleConfirm = () => {
        // Store'dan gelen callback'i çağır
        if (modalData?.onConfirm) {
            modalData.onConfirm();
        }

        // Prop'dan gelen callback'i çağır
        if (onConfirm) {
            onConfirm();
        }

        // Modal'ı kapat
        handleClose();
    };

    // 🔑 ESC tuşu ile kapanma
    useEffect(() => {
        if (modalOpen) {
            const handleKeyDown = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    handleClose();
                }
            };

            document.addEventListener('keydown', handleKeyDown);
            return () => {
                document.removeEventListener('keydown', handleKeyDown);
            };
        }
    }, [modalOpen]);

    return (
        <AnimatePresence>
            {modalOpen && productName && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={handleClose}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center">
                                <motion.div
                                    className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
                                >
                                    <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </motion.div>
                                <motion.h2
                                    className="text-2xl font-bold text-gray-800"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    Ürünü Sil
                                </motion.h2>
                            </div>
                            <motion.button
                                onClick={handleClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.3 }}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </motion.button>
                        </div>

                        {/* Content */}
                        <motion.div
                            className="mb-8"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                        >
                            <p className="text-gray-700 leading-relaxed mb-4">
                                <span className="font-semibold text-red-600">"{productName}"</span> adlı ürünü silmek istediğinizden emin misiniz?
                            </p>

                            {/* Warning Box */}
                            <motion.div
                                className="bg-red-50 border border-red-200 rounded-lg p-4"
                                initial={{ opacity: 0, scale: 0.95 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 0.4 }}
                            >
                                <div className="flex items-start space-x-3">
                                    <svg className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                    <div>
                                        <p className="text-red-800 font-medium text-sm mb-1">
                                            Dikkat!
                                        </p>
                                        <p className="text-red-700 text-sm">
                                            Bu işlem geri alınamaz. Ürün kalıcı olarak silinecek ve tüm ilgili veriler kaybolacaktır.
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Product Info */}
                            {productId && (
                                <motion.div
                                    className="mt-4 bg-gray-50 rounded-lg p-4"
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                >
                                    <div className="flex items-center space-x-3">
                                        <div className="w-12 h-12 flex-shrink-0">
                                            {imageUrl ? (
                                                <img
                                                    src={imageUrl}
                                                    alt={productName}
                                                    className="w-12 h-12 rounded-lg object-cover"
                                                />
                                            ) : (
                                                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                                            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                                    </svg>
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h4 className="font-semibold text-gray-900 truncate">{productName}</h4>
                                            <p className="text-sm text-gray-600">
                                                {brandName}
                                            </p>
                                        </div>
                                    </div>
                                </motion.div>
                            )}
                        </motion.div>

                        {/* Action Buttons */}
                        <motion.div
                            className="flex flex-col space-y-3"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                        >
                            <motion.button
                                onClick={handleConfirm}
                                className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                <span>Evet, Ürünü Sil</span>
                            </motion.button>

                            <motion.button
                                onClick={handleClose}
                                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                İptal Et
                            </motion.button>
                        </motion.div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 