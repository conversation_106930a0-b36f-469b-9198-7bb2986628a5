'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { adminStats } from '@/data/mocks/admin';
import {
    BarChart3,
    TrendingUp,
    Users,
    ShoppingCart,
    Package,
    Calendar,
    Download,
    Filter,
    ArrowLeft,
    Shield,
    DollarSign,
    Target,
    Activity
} from 'lucide-react';
import Link from 'next/link';

const AdminReportsPage = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const [stats] = useState(adminStats);
    const [selectedPeriod, setSelectedPeriod] = useState('6months');

    useEffect(() => {
        if (!isLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, isLoading, router]);

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    };

    const monthlyGrowth = {
        users: ((stats.newUsersThisMonth / (stats.totalUsers - stats.newUsersThisMonth)) * 100).toFixed(1),
        orders: ((stats.ordersThisMonth / (stats.totalOrders - stats.ordersThisMonth)) * 100).toFixed(1),
        revenue: ((stats.revenueThisMonth / (stats.totalRevenue - stats.revenueThisMonth)) * 100).toFixed(1)
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/admin"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Admin Paneli
                            </Link>
                            <span className="text-gray-300">/</span>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Raporlar ve Analitik
                            </h1>
                        </div>
                        <div className="flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg">
                            <Shield className="h-5 w-5 text-red-600" />
                            <span className="text-red-800 font-medium">Admin Erişimi</span>
                        </div>
                    </div>
                </div>

                {/* Control Panel */}
                <motion.div
                    className="bg-white rounded-xl shadow-lg p-6 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                >
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                                <Calendar className="h-5 w-5 text-gray-400" />
                                <select
                                    value={selectedPeriod}
                                    onChange={(e) => setSelectedPeriod(e.target.value)}
                                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600"
                                >
                                    <option value="1month">Son 1 Ay</option>
                                    <option value="3months">Son 3 Ay</option>
                                    <option value="6months">Son 6 Ay</option>
                                    <option value="1year">Son 1 Yıl</option>
                                </select>
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <Download className="h-4 w-4 mr-2" />
                                Raporu İndir
                            </button>
                        </div>
                    </div>
                </motion.div>

                {/* Performance Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Kullanıcı</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
                                <p className="text-sm text-blue-600">+%{monthlyGrowth.users} bu ay</p>
                            </div>
                            <div className="bg-blue-100 p-3 rounded-full">
                                <Users className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Sipariş</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalOrders.toLocaleString()}</p>
                                <p className="text-sm text-green-600">+%{monthlyGrowth.orders} bu ay</p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-full">
                                <ShoppingCart className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Gelir</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                                <p className="text-sm text-purple-600">+%{monthlyGrowth.revenue} bu ay</p>
                            </div>
                            <div className="bg-purple-100 p-3 rounded-full">
                                <DollarSign className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Ürün</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
                                <p className="text-sm text-orange-600">Aktif katalog</p>
                            </div>
                            <div className="bg-orange-100 p-3 rounded-full">
                                <Package className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Charts Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

                    {/* Revenue Trend */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 }}
                    >
                        <div className="flex items-center justify-between mb-6">
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900">Gelir Trendi</h3>
                                <p className="text-sm text-gray-600">Aylık gelir dağılımı</p>
                            </div>
                            <TrendingUp className="h-6 w-6 text-green-600" />
                        </div>

                        <div className="space-y-4">
                            {stats.monthlyStats.map((month, index) => (
                                <div key={index} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <span className="text-sm font-medium text-gray-700 w-16">
                                            {month.month}
                                        </span>
                                        <div className="flex-1 bg-gray-200 rounded-full h-3 w-40">
                                            <div
                                                className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500"
                                                style={{ width: `${(month.revenue / 20000) * 100}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-sm font-semibold text-gray-900">
                                            {formatCurrency(month.revenue)}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                            {month.orders} sipariş
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </motion.div>

                    {/* User Growth */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7 }}
                    >
                        <div className="flex items-center justify-between mb-6">
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900">Kullanıcı Büyümesi</h3>
                                <p className="text-sm text-gray-600">Aylık yeni üye sayısı</p>
                            </div>
                            <Users className="h-6 w-6 text-blue-600" />
                        </div>

                        <div className="space-y-4">
                            {stats.monthlyStats.map((month, index) => (
                                <div key={index} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <span className="text-sm font-medium text-gray-700 w-16">
                                            {month.month}
                                        </span>
                                        <div className="flex-1 bg-gray-200 rounded-full h-3 w-40">
                                            <div
                                                className="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full transition-all duration-500"
                                                style={{ width: `${(month.users / 100) * 100}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-sm font-semibold text-gray-900">
                                            {month.users} kişi
                                        </div>
                                        <div className="text-xs text-gray-500">
                                            Yeni üye
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </motion.div>
                </div>

                {/* Key Metrics */}
                <motion.div
                    className="bg-white rounded-xl shadow-lg p-6 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                >
                    <div className="flex items-center mb-6">
                        <Target className="h-6 w-6 text-gray-600 mr-3" />
                        <h3 className="text-xl font-semibold text-gray-900">Önemli Metrikler</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                            <Activity className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                            <h4 className="text-lg font-semibold text-gray-900 mb-2">Ortalama Sipariş Değeri</h4>
                            <p className="text-2xl font-bold text-blue-600">
                                {formatCurrency(stats.totalRevenue / stats.totalOrders)}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">Son 6 ay ortalaması</p>
                        </div>

                        <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                            <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
                            <h4 className="text-lg font-semibold text-gray-900 mb-2">Kullanıcı Başına Sipariş</h4>
                            <p className="text-2xl font-bold text-green-600">
                                {(stats.totalOrders / stats.totalUsers).toFixed(1)}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">Ortalama sipariş sayısı</p>
                        </div>

                        <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                            <TrendingUp className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                            <h4 className="text-lg font-semibold text-gray-900 mb-2">Büyüme Oranı</h4>
                            <p className="text-2xl font-bold text-purple-600">
                                +{((stats.revenueThisMonth / (stats.totalRevenue / 6)) * 100).toFixed(1)}%
                            </p>
                            <p className="text-sm text-gray-600 mt-1">Aylık gelir büyümesi</p>
                        </div>
                    </div>
                </motion.div>

                {/* Quick Actions */}
                <motion.div
                    className="bg-white rounded-xl shadow-lg p-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9 }}
                >
                    <h3 className="text-xl font-semibold text-gray-900 mb-6">Hızlı Rapor İşlemleri</h3>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                            <Download className="h-8 w-8 text-blue-600 mb-2" />
                            <span className="text-sm font-medium text-gray-700">Kullanıcı Raporu</span>
                        </button>

                        <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors">
                            <Download className="h-8 w-8 text-green-600 mb-2" />
                            <span className="text-sm font-medium text-gray-700">Satış Raporu</span>
                        </button>

                        <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors">
                            <Download className="h-8 w-8 text-purple-600 mb-2" />
                            <span className="text-sm font-medium text-gray-700">Gelir Raporu</span>
                        </button>

                        <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors">
                            <Download className="h-8 w-8 text-orange-600 mb-2" />
                            <span className="text-sm font-medium text-gray-700">Ürün Raporu</span>
                        </button>
                    </div>
                </motion.div>
            </div>
        </div>
    );
};

export default AdminReportsPage; 