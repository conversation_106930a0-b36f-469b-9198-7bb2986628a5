'use client';

import { motion } from "framer-motion";
import { useState } from "react";
import AnnouncementModal from "@/components/AnnouncementModal";

interface Announcement {
    id: number;
    title: string;
    date: string;
    content: string;
    excerpt: string;
    category: string;
    readTime: string;
}

const announcements: Announcement[] = [
    {
        id: 1,
        title: "Yeni Ürün Serimiz Piyasada!",
        date: "1 Ocak 2024",
        excerpt: "Doğal içerikli yeni vitamin serisi ile sağlığınıza katkıda bulunmaya devam ediyoruz. Bu seride yer alan ürünler tamamen organik...",
        category: "Ürün Lansmanı",
        readTime: "3 dk",
        content: `Say Global olarak, müşterilerimizin sağlıklı yaşam tarzlarını desteklemek amacıyla geliştirdiğimiz yeni vitamin serimizi sizlerle buluşturmanın gururunu yaşıyoruz.

Bu yeni serimizde yer alan <PERSON>:

• Vitamin C Complex - Bağışıklık sisteminizi güçlendirmek için
• B12 Vitamin Kompleksi - Enerji metabolizmanız için
• Omega-3 Balık Yağı - Kalp ve beyin sağlığınız için
• Multivitamin - Günlük vitamin ihtiyaçlarınız için

Tüm ürünlerimiz:
- %100 doğal içeriklerle üretilmektedir
- Katkı madde içermez
- Laboratuvar testlerinden geçmiştir
- GMP sertifikalı tesislerde üretilmektedir

Yeni ürünlerimiz hakkında detaylı bilgi almak ve sipariş vermek için web sitemizi ziyaret edebilir veya müşteri hizmetlerimizle iletişime geçebilirsiniz.

Sağlıklı yaşam yolculuğunuzda yanınızda olmaktan mutluluk duyuyoruz.`
    },
    {
        id: 2,
        title: "Ücretsiz Kargo Kampanyası",
        date: "2 Ocak 2024",
        excerpt: "250 TL ve üzeri tüm siparişlerinizde ücretsiz kargo fırsatı başladı. Bu fırsat sınırlı süre için geçerli...",
        category: "Kampanya",
        readTime: "2 dk",
        content: `Sevgili müşterilerimiz,

Say Global olarak sizlere daha iyi hizmet verebilmek adına yeni bir kampanya başlatıyoruz!

KAMPANYA DETAYLARI:
• 250 TL ve üzeri tüm siparişlerde ücretsiz kargo
• Kampanya süresi: 31 Ocak 2024'e kadar
• Tüm ürün kategorilerinde geçerli
• Türkiye genelinde geçerli

NASIL YARARLANABILIRSINIZ?
1. Sepetinize 250 TL değerinde ürün ekleyin
2. Kargo seçeneklerinde "Ücretsiz Kargo" seçeneğini seçin
3. Siparişinizi tamamlayın

Bu fırsatı kaçırmayın! Sağlıklı yaşam ürünlerinizi uygun fiyatlarla kapınıza kadar getiriyoruz.

Sorularınız için müşteri hizmetlerimizle 7/24 iletişime geçebilirsiniz.`
    },
    {
        id: 3,
        title: "Yaz Detoks Programı",
        date: "3 Ocak 2024",
        excerpt: "Uzman diyetisyenlerimiz tarafından hazırlanan 21 günlük detoks programımız başlıyor. Program tamamen ücretsiz...",
        category: "Sağlık",
        readTime: "5 dk",
        content: `Kış aylarının ardından vücudunuzu arındırmak ve yaza hazırlamak için özel olarak tasarlanmış 21 günlük detoks programımız başlıyor!

PROGRAM ÖZELLİKLERİ:
• Uzman diyetisyen kontrolünde
• 21 günlük kişiselleştirilmiş beslenme planı
• Günlük egzersiz önerileri
• Detoks destekleyici ürün önerileri
• Haftalık online danışmanlık seansları

PROGRAMA KATILIM:
✓ Tamamen ücretsiz
✓ Online başvuru
✓ Yaş sınırı: 18-65 arası
✓ Sağlık durumu uygun olan herkes

PROGRAMIN FAYDALARI:
• Metabolizma hızlanması
• Enerji seviyesi artışı
• Cilt kalitesinde iyileşme
• Sindirim sisteminde düzelme
• Genel sağlık durumunda iyileşme

BAŞVURU TARİHLERİ:
Başvuru: 15 Ocak - 31 Ocak 2024
Program Başlangıcı: 1 Şubat 2024

Detoks yolculuğunuzda size rehberlik etmek için sabırsızlanıyoruz. Başvuru için web sitemizin "Detoks Programı" bölümünü ziyaret edin.

Sorularınız için: <EMAIL>`
    },
    {
        id: 4,
        title: "Müşteri Memnuniyet Anketi",
        date: "4 Ocak 2024",
        excerpt: "Hizmet kalitemizi artırmak için görüşlerinizi almak istiyoruz. Ankete katılan tüm müşterilerimize özel indirim kodu...",
        category: "Anket",
        readTime: "1 dk",
        content: `Değerli müşterilerimiz,

Say Global olarak sizlere en iyi hizmeti verebilmek için sürekli kendimizi geliştirmeye odaklanıyoruz. Bu doğrultuda görüş ve önerilerinizi almak için bir anket hazırladık.

ANKET HAKKINDA:
• Süre: Yaklaşık 3-5 dakika
• Konu: Hizmet kalitesi ve memnuniyet
• Gizlilik: Tüm yanıtlar anonim olarak değerlendirilir

KATILIM ÖDÜLÜ:
Anketi tamamlayan tüm müşterilerimize bir sonraki siparişlerinde kullanabilecekleri %15 indirim kodu hediye ediyoruz!

ANKET LİNKİ:
www.sayglobal.com/anket2024

ANKET SORULARI:
• Ürün kalitesi değerlendirmesi
• Kargo ve teslimat deneyimi
• Müşteri hizmetleri memnuniyeti
• Web sitesi kullanım kolaylığı
• Genel memnuniyet durumu
• Öneriler ve yorumlar

Görüşleriniz bizim için çok değerli. Daha iyi bir Say Global için fikirlerinizi bizimle paylaşın.

Ankete katılım için son tarih: 31 Ocak 2024

Teşekkür ederiz.`
    }
];

export default function AnnouncementsPage() {
    const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    const handleReadMore = (announcement: Announcement) => {
        setSelectedAnnouncement(announcement);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
        setSelectedAnnouncement(null);
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'Ürün Lansmanı':
                return 'bg-green-100 text-green-800';
            case 'Kampanya':
                return 'bg-red-100 text-red-800';
            case 'Sağlık':
                return 'bg-blue-100 text-blue-800';
            case 'Anket':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'Ürün Lansmanı':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                );
            case 'Kampanya':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                );
            case 'Sağlık':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                );
            case 'Anket':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                );
            default:
                return null;
        }
    };

    return (
        <div className="container mx-auto px-4 py-16">
            <motion.div
                className="mb-12 text-center"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                <h1 className="text-4xl font-bold mb-4">Duyurular</h1>
                <p className="text-gray-600 max-w-2xl mx-auto">
                    Say Global'den en güncel haberler ve duyurular
                </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {announcements.map((announcement, index) => (
                    <motion.div
                        key={announcement.id}
                        className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 group"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                        whileHover={{ y: -5 }}
                    >
                        <div className="p-6">
                            {/* Header */}
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-2">
                                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(announcement.category)}`}>
                                        {getCategoryIcon(announcement.category)}
                                        {announcement.category}
                                    </span>
                                    <span className="text-xs text-gray-500">
                                        {announcement.readTime} okuma
                                    </span>
                                </div>
                                <div className="text-sm text-gray-500">
                                    {announcement.date}
                                </div>
                            </div>

                            {/* Title */}
                            <h2 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-purple-600 transition-colors">
                                {announcement.title}
                            </h2>

                            {/* Excerpt */}
                            <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                                {announcement.excerpt}
                            </p>

                            {/* Footer */}
                            <div className="flex justify-between items-center">
                                <div className="flex items-center text-sm text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {announcement.date}
                                </div>
                                <motion.button
                                    className="text-purple-600 font-medium inline-flex items-center group bg-purple-50 px-4 py-2 rounded-lg hover:bg-purple-100 transition-colors"
                                    whileHover={{ x: 5 }}
                                    whileTap={{ scale: 0.95 }}
                                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                                    onClick={() => handleReadMore(announcement)}
                                >
                                    Devamını Oku
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-4 w-4 ml-1 transform transition-transform group-hover:translate-x-1"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </motion.button>
                            </div>
                        </div>
                    </motion.div>
                ))}
            </div>

            {/* Modal */}
            <AnnouncementModal
                isOpen={isModalOpen}
                onClose={closeModal}
                announcement={selectedAnnouncement}
            />
        </div>
    );
} 