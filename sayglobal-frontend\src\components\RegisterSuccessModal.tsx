'use client';

import { useRegisterSuccessModal, useModalActions } from '@/stores/modalStore';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function RegisterSuccessModal() {
    const isOpen = useRegisterSuccessModal();
    const { closeRegisterSuccessModal } = useModalActions();
    const router = useRouter();
    const [progress, setProgress] = useState(0);

    // Auto close modal after 5 seconds with progress bar
    useEffect(() => {
        if (isOpen) {
            setProgress(0);

            // Progress bar güncelle<PERSON>i (50ms her adımda)
            const progressInterval = setInterval(() => {
                setProgress((prev) => {
                    if (prev >= 99) {
                        clearInterval(progressInterval);
                        return 100;
                    }
                    return prev + 1; // Her 50ms'de %1 artır (5 saniye = 5000ms / 50ms = 100 adım)
                });
            }, 50);

            return () => {
                clearInterval(progressInterval);
            };
        }
    }, [isOpen]);

    // Progress %100 olduğunda modal'ı kapat
    useEffect(() => {
        if (progress >= 100 && isOpen) {
            const timeoutId = setTimeout(() => {
                closeRegisterSuccessModal();
                router.push('/login');
            }, 100); // 100ms gecikme ile state çakışmasını önle

            return () => clearTimeout(timeoutId);
        }
    }, [progress, isOpen, closeRegisterSuccessModal, router]);

    const handleLoginRedirect = () => {
        closeRegisterSuccessModal();
        router.push('/login');
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={handleLoginRedirect}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Close button */}
                        <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleLoginRedirect}
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </motion.button>

                        {/* Success Icon */}
                        <div className="flex justify-center mb-6">
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.1, duration: 0.3, type: "spring", stiffness: 300 }}
                                className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center"
                            >
                                <motion.svg
                                    initial={{ pathLength: 0 }}
                                    animate={{ pathLength: 1 }}
                                    transition={{ delay: 0.2, duration: 0.5, ease: "easeInOut" }}
                                    className="w-12 h-12 text-green-500"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <motion.path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </motion.svg>
                            </motion.div>
                        </div>

                        {/* Content */}
                        <div className="text-center">
                            <motion.h2
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.15, duration: 0.3 }}
                                className="text-2xl font-bold text-gray-800 mb-4"
                            >
                                Hesabınız Başarıyla Oluşturuldu!
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2, duration: 0.3 }}
                                className="text-gray-600 mb-6 leading-relaxed"
                            >
                                Kayıt işleminiz tamamlandı. Şimdi giriş yaparak SayGlobal platformunu kullanmaya başlayabilirsiniz.
                            </motion.p>

                            {/* Action Buttons */}
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.25, duration: 0.3 }}
                                className="flex flex-col gap-3"
                            >
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={handleLoginRedirect}
                                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"
                                >
                                    Giriş Yap
                                </motion.button>
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={handleLoginRedirect}
                                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-all duration-300"
                                >
                                    Kapat
                                </motion.button>
                            </motion.div>
                        </div>

                        {/* Progress Bar */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.35, duration: 0.3 }}
                            className="mt-6"
                        >
                            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                <motion.div
                                    className="h-full bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full"
                                    initial={{ width: "0%" }}
                                    animate={{ width: `${progress}%` }}
                                    transition={{ duration: 0.1, ease: "linear" }}
                                />
                            </div>
                        </motion.div>

                        {/* Auto redirect info */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.4, duration: 0.3 }}
                            className="mt-3 text-center"
                        >
                            <p className="text-sm text-gray-500">
                                {Math.ceil((100 - progress) / 20)} saniye sonra otomatik olarak giriş sayfasına yönlendirileceksiniz.
                            </p>
                        </motion.div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 