'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
    Package,
    ArrowLeft,
    Check,
    X,
    Eye,
    Edit,
    Clock,
    User,
    Calendar,
    MessageCircle,
    Filter,
    Search,
    ChevronRight,
    ChevronLeft,
    Loader2,
    RefreshCw
} from 'lucide-react';
import { AdminProduct, ProductApprovalStatus, ProductStatus } from '@/types';
import { useAdminProducts, useAdminProductStatistics, useUpdateProductStatus, useProductCacheRefresh } from '@/hooks/useProducts';
import { useProductDetailStore } from '@/stores/productDetailStore';
import ProductApprovalModal from '@/components/ProductApprovalModal';
import AdminProductDetailModalNew from '@/components/AdminProductDetailModalNew';

const AdminPendingProductsPage = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const queryClient = useQueryClient();
    const [page, setPage] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState<ProductApprovalStatus | 'all'>('all');
    const [selectedProduct, setSelectedProduct] = useState<AdminProduct | null>(null);
    const [showApprovalModal, setShowApprovalModal] = useState(false);
    const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve');

    const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
    const [showNewDetailModal, setShowNewDetailModal] = useState(false);

    // Debounced search effect
    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setPage(1); // Reset to first page when searching
        }, 300);

        return () => {
            clearTimeout(handler);
        };
    }, [searchTerm]);

    useEffect(() => {
        if (!isLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, isLoading, router]);

    // Sayfa yüklendiğinde istatistikleri ve ürün listesini yenile
    useEffect(() => {
        if (user && user.role === 'admin') {
            queryClient.invalidateQueries({ queryKey: ['adminProductStatistics'] });
            queryClient.invalidateQueries({ queryKey: ['adminProducts'] });
            console.log('📊 Ürün onay yönetimi sayfası yüklendi, istatistikler ve ürün listesi yenileniyor...');
        }
    }, [user, queryClient]);

    // API'den ürünleri çek
    const { data: statisticsData, isLoading: statsLoading } = useAdminProductStatistics();
    const { data: products, isLoading: productsLoading, isFetching } = useAdminProducts(page, debouncedSearchTerm);

    // Ürün durumu güncelleme mutation'ı
    const updateProductStatusMutation = useUpdateProductStatus();

    // Zustand cache temizleme
    const clearProductCache = useProductDetailStore((state) => state.clearProductCache);

    // Cache yenileme fonksiyonları
    const { refreshProductLists } = useProductCacheRefresh();

    // Filtrelenmiş ürünler
    const filteredProducts = products?.filter(product => {
        if (filterStatus === 'all') return true;

        // Status'a göre filtreleme
        if (filterStatus === 'pending') return product.status === ProductStatus.Pending;
        if (filterStatus === 'approved') return product.status === ProductStatus.Accepted;
        if (filterStatus === 'rejected') return product.status === ProductStatus.Rejected;

        return true;
    }) || [];

    const handleApproval = (product: AdminProduct, action: 'approve' | 'reject') => {
        setSelectedProduct(product);
        setApprovalAction(action);
        setShowApprovalModal(true);
    };

    const confirmApproval = (adminNotes: string) => {
        if (!selectedProduct) return;

        const isApproved = approvalAction === 'approve';

        updateProductStatusMutation.mutate({
            productId: selectedProduct.id,
            isApproved: isApproved,
            message: adminNotes || (isApproved ? 'Ürün onaylandı' : 'Ürün reddedildi')
        }, {
            onSuccess: () => {
                console.log('✅ Ürün durumu başarıyla güncellendi');

                // Zustand cache'ini de temizle
                clearProductCache(selectedProduct.id);
                console.log(`🧹 Zustand cache temizlendi - ProductID: ${selectedProduct.id}`);

                setShowApprovalModal(false);
                setSelectedProduct(null);
            },
            onError: (error: any) => {
                console.error('❌ Ürün durumu güncellenirken hata:', error);
                // Burada kullanıcıya hata mesajı gösterilebilir
            }
        });
    };



    const handleShowNewDetail = (productId: number) => {
        setSelectedProductId(productId);
        setShowNewDetailModal(true);
    };

    const handleCloseNewDetailModal = () => {
        setShowNewDetailModal(false);
        setSelectedProductId(null);

        // Modal kapandığında istatistikleri yenile (ürün durumu değişmiş olabilir)
        queryClient.invalidateQueries({ queryKey: ['adminProductStatistics'] });
        console.log('🔄 Yeni modal kapandı, istatistik cache temizlendi');
    };

    const getStatusColor = (status: ProductStatus) => {
        switch (status) {
            case ProductStatus.Pending:
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case ProductStatus.Accepted:
                return 'bg-green-100 text-green-800 border-green-200';
            case ProductStatus.Rejected:
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getStatusText = (status: ProductStatus) => {
        switch (status) {
            case ProductStatus.Pending:
                return 'Onay Bekliyor';
            case ProductStatus.Accepted:
                return 'Onaylandı';
            case ProductStatus.Rejected:
                return 'Reddedildi';
            default:
                return 'Bilinmiyor';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Ürün ismini belirli karakter sınırında kısaltma fonksiyonu
    const truncateProductName = (name: string, maxLength: number = 22) => {
        if (name.length <= maxLength) return name;
        return name.substring(0, maxLength) + '...';
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/admin"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Admin Paneli
                            </Link>
                        </div>
                    </div>

                    <div className="mt-4">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Ürün Onay Yönetimi
                        </h1>
                        <p className="text-gray-600">
                            Distribütörler tarafından eklenen ürünleri inceleyin ve onaylayın
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white rounded-lg shadow-sm p-6 border border-yellow-200">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <Clock className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Onay Bekleyen</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? '...' : (statisticsData?.pendingCount || 0)}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm p-6 border border-green-200">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <Check className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Onaylanan</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? '...' : (statisticsData?.acceptedCount || 0)}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm p-6 border border-red-200">
                        <div className="flex items-center">
                            <div className="p-2 bg-red-100 rounded-lg">
                                <X className="h-6 w-6 text-red-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Reddedilen</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? '...' : (statisticsData?.rejectedCount || 0)}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm p-6 border border-blue-200">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Package className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Toplam</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? '...' : (statisticsData?.totalProductCount || 0)}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                <input
                                    type="text"
                                    placeholder="Ürün veya kullanıcı ara..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-black"
                                />
                                {isFetching && <Loader2 className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin" />}
                            </div>

                            <select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value as ProductApprovalStatus | 'all')}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-gray-600"
                            >
                                <option value="all">Tüm Durumlar</option>
                                <option value="pending">Onay Bekleyen</option>
                                <option value="approved">Onaylanan</option>
                                <option value="rejected">Reddedilen</option>
                            </select>
                        </div>

                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => {
                                    console.log('🔄 Manuel cache yenileme başlatıldı...');
                                    refreshProductLists();
                                }}
                                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                                title="Verileri yenile"
                            >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Yenile
                            </button>
                            <div className="text-sm text-gray-600">
                                {filteredProducts.length} ürün gösteriliyor
                            </div>
                        </div>
                    </div>
                </div>

                {/* Products Table */}
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Ürün
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Oluşturan Kullanıcı
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Kategori
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Fiyat
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Durum
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Tarih
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredProducts.map((product) => (
                                    <tr key={product.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <img
                                                    className="h-12 w-12 rounded-lg object-cover"
                                                    src={product.imageUrl || 'https://picsum.photos/id/50/200/200'}
                                                    alt={product.name}
                                                />
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900" title={product.name}>
                                                        {truncateProductName(product.name)}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {product.brandName}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <User className="h-4 w-4 text-gray-400 mr-2" />
                                                <div>
                                                    <div className="text-sm text-gray-900">{product.createdByUserName}</div>
                                                    <div className="text-xs text-gray-500">{product.createdByUserRole}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm text-gray-900">{product.categoryName}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm font-medium text-gray-900">
                                                ₺{product.price.toFixed(2)}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(product.status)}`}>
                                                {getStatusText(product.status)}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                {formatDate(product.updatedAt)}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex items-center justify-end space-x-2">
                                                <button
                                                    onClick={() => handleShowNewDetail(product.id)}
                                                    className="text-gray-600 hover:text-gray-900"
                                                    title="Detayları Görüntüle"
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </button>
                                                <Link
                                                    href={`/admin/products/edit/${product.id}?from=pending-products`}
                                                    className="text-blue-600 hover:text-blue-800"
                                                    title="Ürünü Düzenle"
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Link>
                                                {product.status === ProductStatus.Pending && (
                                                    <>
                                                        <button
                                                            onClick={() => handleApproval(product, 'approve')}
                                                            className="text-green-600 hover:text-green-800"
                                                            title="Onayla"
                                                        >
                                                            <Check className="h-4 w-4" />
                                                        </button>
                                                        <button
                                                            onClick={() => handleApproval(product, 'reject')}
                                                            className="text-red-600 hover:text-red-800"
                                                            title="Reddet"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </button>
                                                    </>
                                                )}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination Controls */}
                    <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <button
                            onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                            disabled={page === 1}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <ChevronLeft className="h-4 w-4 mr-2" />
                            Önceki
                        </button>
                        <span className="text-sm text-gray-700">
                            Sayfa <span className="font-bold">{page}</span>
                        </span>
                        <button
                            onClick={() => setPage(prev => (products && products.length === 10) ? prev + 1 : prev)}
                            disabled={!products || products.length < 10}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Sonraki
                            <ChevronRight className="h-4 w-4 ml-2" />
                        </button>
                    </div>

                    {/* No Products Found */}
                    {filteredProducts.length === 0 && !isFetching && !productsLoading && (
                        <div className="px-6 py-12 text-center">
                            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Ürün bulunamadı</h3>
                            <p className="text-gray-500">Arama veya filtreleme kriterlerinizi değiştirerek tekrar deneyin.</p>
                        </div>
                    )}
                </div>

                {/* Loading State */}
                {productsLoading && (
                    <div className="text-center py-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">Ürünler yükleniyor...</p>
                    </div>
                )}

                {/* Empty State */}
                {!productsLoading && filteredProducts.length === 0 && (
                    <div className="text-center py-12">
                        <Package className="h-24 w-24 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            {searchTerm || filterStatus !== 'all' ? 'Arama kriterlerine uygun ürün bulunamadı' : 'Henüz ürün yok'}
                        </h3>
                        <p className="text-gray-600">
                            {searchTerm || filterStatus !== 'all'
                                ? 'Farklı kriterler deneyebilirsiniz.'
                                : 'Ürünler eklendiğinde burada görünecektir.'
                            }
                        </p>
                    </div>
                )}

                {/* Approval Modal */}
                <ProductApprovalModal
                    product={selectedProduct}
                    isOpen={showApprovalModal}
                    onClose={() => setShowApprovalModal(false)}
                    onConfirm={confirmApproval}
                    action={approvalAction}
                    isLoading={updateProductStatusMutation.isPending}
                />

                {/* New Product Detail Modal */}
                <AdminProductDetailModalNew
                    productId={selectedProductId}
                    isOpen={showNewDetailModal}
                    onClose={handleCloseNewDetailModal}
                    showApprovalStatus={true}
                />
            </div>
        </div>
    );
};

export default AdminPendingProductsPage; 