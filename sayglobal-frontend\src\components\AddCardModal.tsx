'use client';

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useAddCardModal, useAddCardData, useModalActions } from '@/stores/modalStore';

// 💳 Add Card Modal - Modal Store entegrasyonu ile
export default function AddCardModal() {
    // 🏪 Modal Store hooks
    const isOpen = useAddCardModal();
    const cardData = useAddCardData();
    const { closeAddCardModal } = useModalActions();
    const [formData, setFormData] = useState({
        cardHolderName: '',
        cardType: '',
        cardNumber: '',
        expirationDate: '',
        cvv: '',
        saveCard: true,
        setAsDefault: false
    });

    // 🔄 Modal açıldığında card data'sını form'a populate et
    useEffect(() => {
        if (cardData) {
            setFormData(cardData);
        } else if (isOpen) {
            // Modal açıldığında form'u temizle (yeni kart ekleme için)
            setFormData({
                cardHolderName: '',
                cardType: '',
                cardNumber: '',
                expirationDate: '',
                cvv: '',
                saveCard: true,
                setAsDefault: false
            });
        }
    }, [cardData, isOpen]);

    // Modal açıkken body scroll'unu engelle ve titreme önle
    useEffect(() => {
        if (isOpen) {
            // Scrollbar genişliğini hesapla
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

            // Body'yi kilitle ve scrollbar genişliği kadar padding ekle
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            // Normal duruma döndür
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        }

        // Cleanup function - component unmount olduğunda
        return () => {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        };
    }, [isOpen]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        if (type === 'checkbox') {
            const checked = (e.target as HTMLInputElement).checked;
            setFormData(prev => ({
                ...prev,
                [name]: checked
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const formatCardNumber = (value: string) => {
        const cleaned = value.replace(/\D/g, '');
        return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
    };

    const formatExpirationDate = (value: string) => {
        const cleaned = value.replace(/\D/g, '');
        if (cleaned.length >= 2) {
            return cleaned.slice(0, 2) + '/' + cleaned.slice(2, 4);
        }
        return cleaned;
    };

    const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const formatted = formatCardNumber(e.target.value);
        if (formatted.replace(/\s/g, '').length <= 16) {
            setFormData(prev => ({
                ...prev,
                cardNumber: formatted
            }));
        }
    };

    const handleExpirationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const formatted = formatExpirationDate(e.target.value);
        setFormData(prev => ({
            ...prev,
            expirationDate: formatted
        }));
    };

    const handleCvvChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '');
        if (value.length <= 4) {
            setFormData(prev => ({
                ...prev,
                cvv: value
            }));
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        console.log('Yeni Kart:', formData);

        setFormData({
            cardHolderName: '',
            cardType: '',
            cardNumber: '',
            expirationDate: '',
            cvv: '',
            saveCard: true,
            setAsDefault: false
        });

        closeAddCardModal();
    };

    const getCardTypeFromNumber = (number: string) => {
        const cleaned = number.replace(/\s/g, '');
        if (cleaned.startsWith('4')) return 'Visa';
        if (cleaned.startsWith('5') || cleaned.startsWith('2')) return 'MasterCard';
        if (cleaned.startsWith('3')) return 'American Express';
        if (cleaned.startsWith('9')) return 'Troy';
        return '';
    };

    const cardType = getCardTypeFromNumber(formData.cardNumber);

    return (
        <AnimatePresence>
            {isOpen && (
                <>
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={closeAddCardModal}
                        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
                    />

                    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.95, y: 20 }}
                            className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
                        >
                            <div className="flex items-center justify-between p-6 border-b border-gray-200">
                                <h2 className="text-xl font-semibold text-gray-800">
                                    Yeni Kart Ekle
                                </h2>
                                <motion.button
                                    onClick={closeAddCardModal}
                                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </motion.button>
                            </div>

                            <form onSubmit={handleSubmit} className="p-6 space-y-6">
                                <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-6 text-white">
                                    <div className="flex justify-between items-start mb-4">
                                        <div className="text-sm opacity-90">
                                            {cardType || 'Kart Türü'}
                                        </div>
                                        <div className="w-8 h-8 bg-white/20 rounded-full"></div>
                                    </div>
                                    <div className="text-lg font-mono mb-4 tracking-wider">
                                        {formData.cardNumber || '**** **** **** ****'}
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span className="opacity-90">
                                            {formData.cardHolderName.toUpperCase() || 'AD SOYAD'}
                                        </span>
                                        <span className="opacity-90">
                                            {formData.expirationDate || 'MM/YY'}
                                        </span>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="cardHolderName" className="block text-sm font-medium text-gray-700 mb-2">
                                            Kart Sahibi Adı <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            id="cardHolderName"
                                            name="cardHolderName"
                                            value={formData.cardHolderName}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black uppercase placeholder:normal-case"
                                            placeholder="Kartın üzerindeki ismi girin"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="cardType" className="block text-sm font-medium text-gray-700 mb-2">
                                            Kart Türü
                                        </label>
                                        <select
                                            id="cardType"
                                            name="cardType"
                                            value={formData.cardType}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        >
                                            <option value="">Otomatik Algıla</option>
                                            <option value="visa">Visa</option>
                                            <option value="mastercard">MasterCard</option>
                                            <option value="amex">American Express</option>
                                            <option value="troy">Troy</option>
                                        </select>
                                        {cardType && (
                                            <p className="text-xs text-green-600 mt-1">Algılanan: {cardType}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="cardNumber" className="block text-sm font-medium text-gray-700 mb-2">
                                        Kart Numarası <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="cardNumber"
                                        name="cardNumber"
                                        value={formData.cardNumber}
                                        onChange={handleCardNumberChange}
                                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono text-lg tracking-wider"
                                        placeholder="0000 0000 0000 0000"
                                        required
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-2">
                                            Son Kullanma <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            id="expirationDate"
                                            name="expirationDate"
                                            value={formData.expirationDate}
                                            onChange={handleExpirationChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono text-center"
                                            placeholder="MM/YY"
                                            maxLength={5}
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="cvv" className="block text-sm font-medium text-gray-700 mb-2">
                                            CVV <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            id="cvv"
                                            name="cvv"
                                            value={formData.cvv}
                                            onChange={handleCvvChange}
                                            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black font-mono text-center"
                                            placeholder="123"
                                            required
                                        />
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-start">
                                        <div className="flex items-center h-5">
                                            <input
                                                id="saveCard"
                                                name="saveCard"
                                                type="checkbox"
                                                checked={formData.saveCard}
                                                onChange={handleInputChange}
                                                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                            />
                                        </div>
                                        <div className="ml-3 text-sm">
                                            <label htmlFor="saveCard" className="font-medium text-gray-700">
                                                Kartımı güvenli şekilde kaydet
                                            </label>
                                            <p className="text-gray-500">Gelecekteki ödemeleriniz için kart bilgileriniz güvenli şekilde saklanır</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start">
                                        <div className="flex items-center h-5">
                                            <input
                                                id="setAsDefault"
                                                name="setAsDefault"
                                                type="checkbox"
                                                checked={formData.setAsDefault}
                                                onChange={handleInputChange}
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            />
                                        </div>
                                        <div className="ml-3 text-sm">
                                            <label htmlFor="setAsDefault" className="font-medium text-gray-700 flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                                Bu kartı varsayılan kart yap
                                            </label>
                                            <p className="text-gray-500">Bu kart tüm ödemelerinizde öncelikli olarak kullanılacak</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-end space-x-3 pt-4">
                                    <motion.button
                                        type="button"
                                        onClick={closeAddCardModal}
                                        className="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        İptal
                                    </motion.button>
                                    <motion.button
                                        type="submit"
                                        className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:shadow-lg transition duration-300"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        Kartı Ekle
                                    </motion.button>
                                </div>
                            </form>
                        </motion.div>
                    </div>
                </>
            )}
        </AnimatePresence>
    );
} 