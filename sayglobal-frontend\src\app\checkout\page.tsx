'use client';

import { useCart } from '@/contexts/CartContext';
import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import SuccessModal from '@/components/SuccessModal';

export default function CheckoutPage() {
    const { items, getTotalPrice, getTotalPoints, clearCart } = useCart();
    const router = useRouter();

    const [step, setStep] = useState(1);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [formData, setFormData] = useState({
        // Teslimat Bilgileri
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        district: '',
        postalCode: '',

        // Ödeme Bilgileri
        paymentMethod: 'creditCard',
        cardNumber: '',
        cardName: '',
        expiryDate: '',
        cvv: '',

        // Fatura Bilgileri
        billingAddress: '',
        billingCity: '',
        billingDistrict: '',
        billingPostalCode: '',
        sameAsDelivery: true
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    // Sepet boşsa yönlendir
    if (items.length === 0) {
        return (
            <div className="container mx-auto px-4 py-16">
                <div className="text-center">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                    >
                        <h1 className="text-3xl font-bold text-white mb-4">Sepetiniz Boş</h1>
                        <p className="text-gray-300 mb-8">
                            Ödeme yapabilmek için sepetinizde ürün bulunmalıdır.
                        </p>
                        <Link
                            href="/products"
                            className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300"
                        >
                            Alışverişe Başla
                        </Link>
                    </motion.div>
                </div>
            </div>
        );
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;

        if (type === 'checkbox') {
            const checked = (e.target as HTMLInputElement).checked;
            setFormData(prev => ({
                ...prev,
                [name]: checked
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }

        // Hata temizle
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateStep = (stepNumber: number) => {
        const newErrors: Record<string, string> = {};

        if (stepNumber === 1) {
            // Teslimat bilgileri validasyonu
            if (!formData.firstName.trim()) newErrors.firstName = 'Ad gereklidir';
            if (!formData.lastName.trim()) newErrors.lastName = 'Soyad gereklidir';
            if (!formData.email.trim()) newErrors.email = 'E-posta gereklidir';
            if (!formData.phone.trim()) newErrors.phone = 'Telefon gereklidir';
            if (!formData.address.trim()) newErrors.address = 'Adres gereklidir';
            if (!formData.city.trim()) newErrors.city = 'Şehir gereklidir';
            if (!formData.district.trim()) newErrors.district = 'İlçe gereklidir';
            if (!formData.postalCode.trim()) newErrors.postalCode = 'Posta kodu gereklidir';
        }

        if (stepNumber === 2) {
            // Ödeme bilgileri validasyonu
            if (formData.paymentMethod === 'creditCard') {
                if (!formData.cardNumber.trim()) newErrors.cardNumber = 'Kart numarası gereklidir';
                if (!formData.cardName.trim()) newErrors.cardName = 'Kart sahibi adı gereklidir';
                if (!formData.expiryDate.trim()) newErrors.expiryDate = 'Son kullanma tarihi gereklidir';
                if (!formData.cvv.trim()) newErrors.cvv = 'CVV gereklidir';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleNextStep = () => {
        if (validateStep(step)) {
            setStep(prev => prev + 1);
        }
    };

    const handlePrevStep = () => {
        setStep(prev => prev - 1);
    };

    const handleSubmit = async () => {
        if (validateStep(2)) {
            // Burada ödeme işlemi yapılacak
            setShowSuccessModal(true);
        }
    };

    const handleCloseSuccessModal = () => {
        setShowSuccessModal(false);
        clearCart(); // Her durumda sepeti temizle
        // router.push navigasyonunu modal component'inde Link'ler halledecek
    };

    const formatCardNumber = (value: string) => {
        return value.replace(/\D/g, '').replace(/(\d{4})(?=\d)/g, '$1 ');
    };

    const formatExpiryDate = (value: string) => {
        return value.replace(/\D/g, '').replace(/(\d{2})(\d{2})/, '$1/$2');
    };

    return (
        <>
            <div className="container mx-auto px-4 py-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                >
                    {/* Header */}
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-white mb-4">Ödeme</h1>

                        {/* Progress Bar */}
                        <div className="flex items-center space-x-4 mb-6">
                            <div className="flex items-center space-x-2">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-300 text-gray-600'
                                    }`}>
                                    1
                                </div>
                                <span className={`text-sm font-medium ${step >= 1 ? 'text-white' : 'text-gray-400'
                                    }`}>
                                    Teslimat
                                </span>
                            </div>

                            <div className={`h-0.5 w-16 ${step >= 2 ? 'bg-purple-600' : 'bg-gray-300'}`}></div>

                            <div className="flex items-center space-x-2">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-300 text-gray-600'
                                    }`}>
                                    2
                                </div>
                                <span className={`text-sm font-medium ${step >= 2 ? 'text-white' : 'text-gray-400'
                                    }`}>
                                    Ödeme
                                </span>
                            </div>

                            <div className={`h-0.5 w-16 ${step >= 3 ? 'bg-purple-600' : 'bg-gray-300'}`}></div>

                            <div className="flex items-center space-x-2">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-purple-600 text-white' : 'bg-gray-300 text-gray-600'
                                    }`}>
                                    3
                                </div>
                                <span className={`text-sm font-medium ${step >= 3 ? 'text-white' : 'text-gray-400'
                                    }`}>
                                    Onay
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Sol Taraf - Form */}
                        <div className="lg:col-span-2">
                            {step === 1 && (
                                <motion.div
                                    className="bg-white rounded-lg p-6 shadow-md"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5 }}
                                >
                                    <h2 className="text-xl font-bold text-gray-800 mb-6">Teslimat Bilgileri</h2>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Ad *</label>
                                            <input
                                                type="text"
                                                name="firstName"
                                                value={formData.firstName}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.firstName ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Soyad *</label>
                                            <input
                                                type="text"
                                                name="lastName"
                                                value={formData.lastName}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.lastName ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">E-posta *</label>
                                            <input
                                                type="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.email ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Telefon *</label>
                                            <input
                                                type="tel"
                                                name="phone"
                                                value={formData.phone}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.phone ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                                        </div>
                                    </div>

                                    <div className="mt-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Adres *</label>
                                        <input
                                            type="text"
                                            name="address"
                                            value={formData.address}
                                            onChange={handleInputChange}
                                            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.address ? 'border-red-500' : 'border-gray-300'
                                                }`}
                                            placeholder="Mahalle, Sokak, No"
                                        />
                                        {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Şehir *</label>
                                            <input
                                                type="text"
                                                name="city"
                                                value={formData.city}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.city ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">İlçe *</label>
                                            <input
                                                type="text"
                                                name="district"
                                                value={formData.district}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.district ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.district && <p className="text-red-500 text-sm mt-1">{errors.district}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Posta Kodu *</label>
                                            <input
                                                type="text"
                                                name="postalCode"
                                                value={formData.postalCode}
                                                onChange={handleInputChange}
                                                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.postalCode ? 'border-red-500' : 'border-gray-300'
                                                    }`}
                                            />
                                            {errors.postalCode && <p className="text-red-500 text-sm mt-1">{errors.postalCode}</p>}
                                        </div>
                                    </div>
                                </motion.div>
                            )}

                            {step === 2 && (
                                <motion.div
                                    className="bg-white rounded-lg p-6 shadow-md"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5 }}
                                >
                                    <h2 className="text-xl font-bold text-gray-800 mb-6">Ödeme Bilgileri</h2>

                                    {/* Ödeme Yöntemi Seçimi */}
                                    <div className="mb-6">
                                        <label className="block text-sm font-medium text-gray-700 mb-3">Ödeme Yöntemi</label>
                                        <div className="space-y-3">
                                            <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                <input
                                                    type="radio"
                                                    name="paymentMethod"
                                                    value="creditCard"
                                                    checked={formData.paymentMethod === 'creditCard'}
                                                    onChange={handleInputChange}
                                                    className="mr-3"
                                                />
                                                <div className="flex items-center space-x-3">
                                                    <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" />
                                                    </svg>
                                                    <span className="font-medium text-gray-700">Kredi/Banka Kartı</span>
                                                </div>
                                            </label>

                                            <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                <input
                                                    type="radio"
                                                    name="paymentMethod"
                                                    value="bankTransfer"
                                                    checked={formData.paymentMethod === 'bankTransfer'}
                                                    onChange={handleInputChange}
                                                    className="mr-3"
                                                />
                                                <div className="flex items-center space-x-3">
                                                    <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M11.5,1L2,6V8H21V6M16,10V17H19V10M2,22H21V19H2M10,10V17H13V10" />
                                                    </svg>
                                                    <span className="font-medium text-gray-700">Banka Havalesi/EFT</span>
                                                </div>
                                            </label>

                                            <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                <input
                                                    type="radio"
                                                    name="paymentMethod"
                                                    value="cashOnDelivery"
                                                    checked={formData.paymentMethod === 'cashOnDelivery'}
                                                    onChange={handleInputChange}
                                                    className="mr-3"
                                                />
                                                <div className="flex items-center space-x-3">
                                                    <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z" />
                                                    </svg>
                                                    <span className="font-medium text-gray-700">Kapıda Ödeme</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    {/* Kredi Kartı Bilgileri */}
                                    {formData.paymentMethod === 'creditCard' && (
                                        <div className="space-y-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">Kart Numarası *</label>
                                                <input
                                                    type="text"
                                                    name="cardNumber"
                                                    value={formatCardNumber(formData.cardNumber)}
                                                    onChange={(e) => {
                                                        const value = e.target.value.replace(/\s/g, '');
                                                        if (value.length <= 16) {
                                                            handleInputChange({
                                                                ...e,
                                                                target: { ...e.target, value }
                                                            });
                                                        }
                                                    }}
                                                    placeholder="1234 5678 9012 3456"
                                                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.cardNumber ? 'border-red-500' : 'border-gray-300'
                                                        }`}
                                                />
                                                {errors.cardNumber && <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">Kart Sahibi Adı *</label>
                                                <input
                                                    type="text"
                                                    name="cardName"
                                                    value={formData.cardName}
                                                    onChange={handleInputChange}
                                                    placeholder="John Doe"
                                                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.cardName ? 'border-red-500' : 'border-gray-300'
                                                        }`}
                                                />
                                                {errors.cardName && <p className="text-red-500 text-sm mt-1">{errors.cardName}</p>}
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">Son Kullanma Tarihi *</label>
                                                    <input
                                                        type="text"
                                                        name="expiryDate"
                                                        value={formatExpiryDate(formData.expiryDate)}
                                                        onChange={(e) => {
                                                            const value = e.target.value.replace(/\D/g, '');
                                                            if (value.length <= 4) {
                                                                handleInputChange({
                                                                    ...e,
                                                                    target: { ...e.target, value }
                                                                });
                                                            }
                                                        }}
                                                        placeholder="MM/YY"
                                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.expiryDate ? 'border-red-500' : 'border-gray-300'
                                                            }`}
                                                    />
                                                    {errors.expiryDate && <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>}
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">CVV *</label>
                                                    <input
                                                        type="text"
                                                        name="cvv"
                                                        value={formData.cvv}
                                                        onChange={(e) => {
                                                            const value = e.target.value.replace(/\D/g, '');
                                                            if (value.length <= 3) {
                                                                handleInputChange({
                                                                    ...e,
                                                                    target: { ...e.target, value }
                                                                });
                                                            }
                                                        }}
                                                        placeholder="123"
                                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.cvv ? 'border-red-500' : 'border-gray-300'
                                                            }`}
                                                    />
                                                    {errors.cvv && <p className="text-red-500 text-sm mt-1">{errors.cvv}</p>}
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Banka Havalesi Bilgisi */}
                                    {formData.paymentMethod === 'bankTransfer' && (
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <h3 className="font-medium text-blue-800 mb-2">Banka Hesap Bilgileri</h3>
                                            <div className="text-sm text-blue-700 space-y-1">
                                                <p><strong>Banka:</strong> Türkiye İş Bankası</p>
                                                <p><strong>Hesap Sahibi:</strong> Say Global Ltd. Şti.</p>
                                                <p><strong>IBAN:</strong> TR12 0006 4000 0011 2345 6789 01</p>
                                                <p><strong>Açıklama:</strong> Sipariş numaranızı belirtiniz</p>
                                            </div>
                                        </div>
                                    )}

                                    {/* Kapıda Ödeme Bilgisi */}
                                    {formData.paymentMethod === 'cashOnDelivery' && (
                                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                            <h3 className="font-medium text-orange-800 mb-2">Kapıda Ödeme</h3>
                                            <p className="text-sm text-orange-700">
                                                Siparişiniz adresinize teslim edilirken nakit olarak ödeme yapabilirsiniz.
                                                Kapıda ödeme için ek ücret alınmamaktadır.
                                            </p>
                                        </div>
                                    )}
                                </motion.div>
                            )}

                            {step === 3 && (
                                <motion.div
                                    className="bg-white rounded-lg p-6 shadow-md"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5 }}
                                >
                                    <h2 className="text-xl font-bold text-gray-800 mb-6">Sipariş Onayı</h2>

                                    {/* Teslimat Özeti */}
                                    <div className="mb-6">
                                        <h3 className="font-medium text-gray-800 mb-3">Teslimat Adresi</h3>
                                        <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-700">
                                            <p className="font-medium">{formData.firstName} {formData.lastName}</p>
                                            <p>{formData.address}</p>
                                            <p>{formData.district}, {formData.city} {formData.postalCode}</p>
                                            <p>{formData.phone}</p>
                                            <p>{formData.email}</p>
                                        </div>
                                    </div>

                                    {/* Ödeme Özeti */}
                                    <div className="mb-6">
                                        <h3 className="font-medium text-gray-800 mb-3">Ödeme Yöntemi</h3>
                                        <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-700">
                                            {formData.paymentMethod === 'creditCard' && (
                                                <p>Kredi/Banka Kartı (**** **** **** {formData.cardNumber.slice(-4)})</p>
                                            )}
                                            {formData.paymentMethod === 'bankTransfer' && <p>Banka Havalesi/EFT</p>}
                                            {formData.paymentMethod === 'cashOnDelivery' && <p>Kapıda Ödeme</p>}
                                        </div>
                                    </div>

                                    {/* Ürün Listesi */}
                                    <div>
                                        <h3 className="font-medium text-gray-800 mb-3">Sipariş Detayları</h3>
                                        <div className="space-y-3">
                                            {items.map((item) => (
                                                <div key={item.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="relative w-12 h-12 flex-shrink-0">
                                                            <Image
                                                                src={item.thumbnail}
                                                                alt={item.title}
                                                                fill
                                                                className="object-cover rounded"
                                                            />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-gray-800 text-sm">{item.title}</p>
                                                            <p className="text-gray-600 text-xs">Adet: {item.quantity}</p>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-medium text-purple-700">
                                                            {(item.price * item.quantity).toFixed(2)} ₺
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </motion.div>
                            )}

                            {/* Navigation Buttons */}
                            <div className="flex justify-between mt-6">
                                <motion.button
                                    onClick={handlePrevStep}
                                    className={`px-6 py-2 rounded-lg font-medium ${step === 1
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : 'bg-gray-600 text-white hover:bg-gray-700'
                                        }`}
                                    disabled={step === 1}
                                    whileHover={step > 1 ? { scale: 1.02 } : {}}
                                    whileTap={step > 1 ? { scale: 0.98 } : {}}
                                >
                                    Geri
                                </motion.button>

                                {step < 3 ? (
                                    <motion.button
                                        onClick={handleNextStep}
                                        className="px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg font-medium hover:shadow-lg"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        İleri
                                    </motion.button>
                                ) : (
                                    <motion.button
                                        onClick={handleSubmit}
                                        className="px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:shadow-lg"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        Siparişi Tamamla
                                    </motion.button>
                                )}
                            </div>
                        </div>

                        {/* Sağ Taraf - Sipariş Özeti */}
                        <div className="lg:col-span-1">
                            <motion.div
                                className="bg-white rounded-lg p-6 shadow-md sticky top-8"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.3, duration: 0.6 }}
                            >
                                <h2 className="text-xl font-bold text-gray-800 mb-6">Sipariş Özeti</h2>

                                {/* Ürünler */}
                                <div className="space-y-3 mb-6">
                                    {items.map((item) => (
                                        <div key={item.id} className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3">
                                                <div className="relative w-12 h-12 flex-shrink-0">
                                                    <Image
                                                        src={item.thumbnail}
                                                        alt={item.title}
                                                        fill
                                                        className="object-cover rounded"
                                                    />
                                                </div>
                                                <div className="flex-1">
                                                    <p className="text-sm font-medium text-gray-800 truncate">{item.title}</p>
                                                    <p className="text-xs text-gray-600">x{item.quantity}</p>
                                                </div>
                                            </div>
                                            <div className="text-sm font-medium text-gray-800">
                                                {(item.price * item.quantity).toFixed(2)} ₺
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* Toplam */}
                                <div className="space-y-3 mb-6">
                                    <div className="flex justify-between text-gray-600">
                                        <span>Ürün Toplamı:</span>
                                        <span>{getTotalPrice().toFixed(2)} ₺</span>
                                    </div>
                                    <div className="flex justify-between text-gray-600">
                                        <span>Kargo:</span>
                                        <span className="text-green-600">Ücretsiz</span>
                                    </div>
                                    <div className="flex justify-between items-center text-purple-600 bg-purple-50 p-3 rounded-lg">
                                        <div className="flex items-center space-x-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                            </svg>
                                            <span className="font-medium">Toplam Puan:</span>
                                        </div>
                                        <span className="font-bold text-lg">{getTotalPoints()} puan</span>
                                    </div>
                                    <div className="border-t pt-3">
                                        <div className="flex justify-between text-lg font-bold text-gray-800">
                                            <span>Toplam:</span>
                                            <span className="text-purple-700">{getTotalPrice().toFixed(2)} ₺</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Güvenlik Bilgisi */}
                                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                    <div className="flex items-center space-x-2 text-green-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                        <span className="text-sm font-medium">256-bit SSL ile Güvenli Ödeme</span>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </motion.div>
            </div>

            {showSuccessModal && (
                <SuccessModal isOpen={showSuccessModal} onClose={handleCloseSuccessModal} />
            )}
        </>
    );
} 