'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { userService } from '@/services/api';
import { authService } from '@/services/authService';
import { useQueryClient } from '@tanstack/react-query';
import { authKeys, useProfileInfo } from '@/hooks/useAuth';
import { useAuthStore } from '@/stores/authStore';
import { useAuth } from '@/components/auth/AuthContext';

// 🚀 NEW: Modal store imports
import {
    useEditPersonalInfoModal,
    useEditPersonalInfoData,
    useModalActions
} from '@/stores/modalStore';

interface EditPersonalInfoModalProps {
    // 🔄 BACKWARD COMPATIBILITY: Eski props hala destekleniyor
    isOpen?: boolean;
    onClose?: () => void;
}

export default function EditPersonalInfoModal({ isOpen, onClose }: EditPersonalInfoModalProps) {
    // 🚀 NEW: Modal store hooks
    const isModalOpen = useEditPersonalInfoModal();
    const modalUserData = useEditPersonalInfoData();
    const { closeEditPersonalInfoModal } = useModalActions();

    // Auth ve profile bilgilerini al
    const { user } = useAuth();
    const { data: profileInfo } = useProfileInfo();

    // 🔄 Cache management for instant UI updates
    const queryClient = useQueryClient();

    // 🎯 HYBRID APPROACH: Store'u önceliklendire, props'u fallback olarak kullan
    const modalOpen = isModalOpen || isOpen || false;
    const handleClose = () => {
        // Store'dan açıldıysa store'u kapat
        if (isModalOpen) {
            closeEditPersonalInfoModal();
        }
        // Prop'dan açıldıysa callback'i çağır
        if (onClose) {
            onClose();
        }
    };

    // 🎯 Form state - mevcut profil bilgileriyle doldurulacak
    const [formData, setFormData] = useState(() => ({
        firstName: '',
        lastName: '',
        phoneNumber: '',
    }));

    // Loading state for API calls
    const [isLoading, setIsLoading] = useState(false);

    // 🔄 Modal açıldığında mevcut profil verilerini form'a yükle
    useEffect(() => {
        if (modalOpen) {
            // ProfileInfo'dan güncel verileri al, yoksa user'dan al, yoksa modalUserData'dan al
            const currentData = {
                firstName: profileInfo?.firstName || user?.firstName || modalUserData?.firstName || '',
                lastName: profileInfo?.lastName || user?.lastName || modalUserData?.lastName || '',
                phoneNumber: user?.phoneNumber || modalUserData?.phoneNumber || '',
            };

            setFormData(currentData);
        }
    }, [modalOpen, profileInfo, user, modalUserData]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            // Mevcut profil bilgilerini korumak için tüm bilgileri gönder
            const updateData: any = {};

            // Modal'daki güncellenmiş bilgiler
            if (formData.firstName && formData.firstName.trim()) {
                updateData.firstName = formData.firstName.trim();
            }

            if (formData.lastName && formData.lastName.trim()) {
                updateData.lastName = formData.lastName.trim();
            }

            if (formData.phoneNumber && formData.phoneNumber.trim()) {
                updateData.phoneNumber = formData.phoneNumber.trim();
            }

            // Mevcut profil bilgilerini korumak için profileInfo'dan diğer alanları da ekle
            if (profileInfo?.dateOfBirth) {
                updateData.dateOfBirth = profileInfo.dateOfBirth;
            }

            if (profileInfo?.gender !== undefined && profileInfo?.gender !== null) {
                updateData.gender = profileInfo.gender;
            }

            if (profileInfo?.location && profileInfo.location.trim()) {
                updateData.location = profileInfo.location.trim();
            }

            // Sadece dolu alanlar varsa API çağrısı yap
            if (Object.keys(updateData).length === 0) {
                handleClose();
                return;
            }

            // AuthService kullanarak profil güncelle
            await authService.updateProfile(updateData);

            // 🚀 Instant UI updates için cache'leri invalidate et
            queryClient.invalidateQueries({ queryKey: authKeys.user() });
            queryClient.invalidateQueries({ queryKey: authKeys.profileInfo() });

            // 🔄 Fresh data'yı fetch et ve cache'e al
            await Promise.all([
                queryClient.refetchQueries({ queryKey: authKeys.user() }),
                queryClient.refetchQueries({ queryKey: authKeys.profileInfo() })
            ]);

            // Modal'ı kapat
            handleClose();
        } catch (error: any) {
            console.error('❌ Profil güncellenirken hata:', error);
            alert('Profil güncellenirken bir hata oluştu: ' + (error.message || 'Bilinmeyen hata'));
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <AnimatePresence>
            {modalOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={handleClose}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between mb-6">
                            <h2 className="text-2xl font-bold text-gray-800">
                                Kişisel Bilgilerimi Güncelle
                            </h2>
                            <motion.button
                                onClick={handleClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </motion.button>
                        </div>

                        {/* Form */}
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Ad
                                    </label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        value={formData.firstName || ''}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        placeholder="Adınız"
                                        required
                                    />
                                </div>
                                <div>
                                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Soyad
                                    </label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        value={formData.lastName || ''}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        placeholder="Soyadınız"
                                        required
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                                    Telefon
                                </label>
                                <input
                                    type="tel"
                                    id="phoneNumber"
                                    name="phoneNumber"
                                    value={formData.phoneNumber || ''}
                                    onChange={handleInputChange}
                                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                    placeholder="Telefon numaranız"
                                />
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col space-y-3 pt-4">
                                <motion.button
                                    type="submit"
                                    disabled={isLoading}
                                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                    whileHover={{ scale: isLoading ? 1 : 1.02 }}
                                    whileTap={{ scale: isLoading ? 1 : 0.98 }}
                                >
                                    {isLoading ? 'Güncelleniyor...' : 'Bilgileri Güncelle'}
                                </motion.button>

                                <motion.button
                                    type="button"
                                    onClick={handleClose}
                                    className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    İptal
                                </motion.button>
                            </div>
                        </form>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 