import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Address } from '@/types';

// 🎯 Separate state and actions for better organization
interface AddressState {
    // UI State
    isAddAddressModalOpen: boolean;
    selectedAddressId: number | null;

    // Form state
    addressFormData: Partial<Address>;

    // Filter & Search
    searchQuery: string;
    selectedCity: string | null;

    // UI preferences
    viewMode: 'list' | 'grid';
    sortBy: 'newest' | 'oldest' | 'name';
}

interface AddressActions {
    // Modal actions
    openAddAddressModal: () => void;
    closeAddAddressModal: () => void;

    // Selection actions
    setSelectedAddress: (addressId: number | null) => void;

    // Form actions
    setAddressFormData: (data: Partial<Address>) => void;
    clearAddressFormData: () => void;
    updateFormField: <K extends keyof Address>(field: K, value: Address[K]) => void;

    // Search & Filter actions
    setSearchQuery: (query: string) => void;
    setSelectedCity: (city: string | null) => void;
    clearFilters: () => void;

    // UI preference actions
    setViewMode: (mode: 'list' | 'grid') => void;
    setSortBy: (sort: 'newest' | 'oldest' | 'name') => void;

    // Reset actions
    resetAddressState: () => void;
}

type AddressStore = AddressState & AddressActions;

// 🎨 Initial state
const initialState: AddressState = {
    isAddAddressModalOpen: false,
    selectedAddressId: null,
    addressFormData: {},
    searchQuery: '',
    selectedCity: null,
    viewMode: 'list',
    sortBy: 'newest',
};

// 🐻 Create store with middleware
export const useAddressStore = create<AddressStore>()(
    devtools(
        persist(
            (set, get) => ({
                // Initial state
                ...initialState,

                // Modal actions
                openAddAddressModal: () => {
                    console.log('🏪 Zustand: Opening add address modal');
                    set({ isAddAddressModalOpen: true }, false, 'address/openAddModal');
                },

                closeAddAddressModal: () => {
                    console.log('🏪 Zustand: Closing add address modal');
                    set({
                        isAddAddressModalOpen: false,
                        addressFormData: {} // Clear form when closing
                    }, false, 'address/closeAddModal');
                },

                // Selection actions
                setSelectedAddress: (addressId) => {
                    console.log('🏪 Zustand: Selected address ID:', addressId);
                    set({ selectedAddressId: addressId }, false, 'address/setSelected');
                },

                // Form actions
                setAddressFormData: (data) => {
                    console.log('🏪 Zustand: Setting form data:', data);
                    set((state) => ({
                        addressFormData: { ...state.addressFormData, ...data }
                    }), false, 'address/setFormData');
                },

                clearAddressFormData: () => {
                    console.log('🏪 Zustand: Clearing form data');
                    set({ addressFormData: {} }, false, 'address/clearFormData');
                },

                updateFormField: (field, value) => {
                    console.log(`🏪 Zustand: Updating form field ${field}:`, value);
                    set((state) => ({
                        addressFormData: {
                            ...state.addressFormData,
                            [field]: value
                        }
                    }), false, `address/updateField/${field}`);
                },

                // Search & Filter actions
                setSearchQuery: (query) => {
                    console.log('🏪 Zustand: Setting search query:', query);
                    set({ searchQuery: query }, false, 'address/setSearchQuery');
                },

                setSelectedCity: (city) => {
                    console.log('🏪 Zustand: Setting selected city:', city);
                    set({ selectedCity: city }, false, 'address/setSelectedCity');
                },

                clearFilters: () => {
                    console.log('🏪 Zustand: Clearing all filters');
                    set({
                        searchQuery: '',
                        selectedCity: null
                    }, false, 'address/clearFilters');
                },

                // UI preference actions
                setViewMode: (mode) => {
                    console.log('🏪 Zustand: Setting view mode:', mode);
                    set({ viewMode: mode }, false, 'address/setViewMode');
                },

                setSortBy: (sort) => {
                    console.log('🏪 Zustand: Setting sort by:', sort);
                    set({ sortBy: sort }, false, 'address/setSortBy');
                },

                // Reset actions
                resetAddressState: () => {
                    console.log('🏪 Zustand: Resetting address state');
                    set(initialState, false, 'address/reset');
                },
            }),
            {
                name: 'sayglobal-address-store', // localStorage key
                // Sadece UI preferences'ı persist et, form data'yı değil
                partialize: (state) => ({
                    viewMode: state.viewMode,
                    sortBy: state.sortBy,
                    selectedCity: state.selectedCity, // Keep selected city for convenience
                }),
                version: 1, // Store versioning
                migrate: (persistedState: any, version: number) => {
                    // Migration logic for future versions
                    if (version === 0) {
                        // Migrate from version 0 to 1
                        return {
                            ...persistedState,
                            viewMode: persistedState.viewMode || 'list',
                            sortBy: persistedState.sortBy || 'newest',
                        };
                    }
                    return persistedState;
                },
            }
        ),
        {
            name: 'address-store', // DevTools name
            enabled: process.env.NODE_ENV === 'development', // Only in development
        }
    )
);

// 🔧 Simple Selectors ONLY (No composite selectors to prevent getSnapshot issues)
export const useAddressModalOpen = () => useAddressStore((state) => state.isAddAddressModalOpen);
export const useSelectedAddressId = () => useAddressStore((state) => state.selectedAddressId);
export const useAddressFormData = () => useAddressStore((state) => state.addressFormData); 