"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addressService: () => (/* binding */ addressService),\n/* harmony export */   cartService: () => (/* binding */ cartService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   productService: () => (/* binding */ productService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var axios_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios-retry */ \"(app-pages-browser)/./node_modules/axios-retry/dist/esm/index.js\");\n/* harmony import */ var _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants/apiEndpoints */ \"(app-pages-browser)/./src/constants/apiEndpoints.ts\");\n/* harmony import */ var _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/networkStore */ \"(app-pages-browser)/./src/stores/networkStore.ts\");\n\n\n\n\n// API Base URL - Development'te proxy kullan, production'da direkt API\nconst API_BASE_URL =  true ? '' // Development'te Next.js proxy kullanır\n : 0;\n// Cookie'den belirli bir değeri okuma utility fonksiyonu\nconst getCookieValue = (name)=>{\n    if (typeof document === 'undefined') return null; // SSR kontrolü\n    const value = \"; \".concat(document.cookie);\n    const parts = value.split(\"; \".concat(name, \"=\"));\n    if (parts.length === 2) {\n        var _parts_pop;\n        const cookieValue = (_parts_pop = parts.pop()) === null || _parts_pop === void 0 ? void 0 : _parts_pop.split(';').shift();\n        return cookieValue || null;\n    }\n    return null;\n};\n// Axios instance oluştur - HTTP-only cookies için withCredentials: true\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    },\n    timeout: 30000,\n    withCredentials: true\n});\n// Yeniden deneme mekanizmasını yapılandır\n(0,axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(apiClient, {\n    retries: 3,\n    retryCondition: (error)=>{\n        var _error_response;\n        // Sadece ağ hatalarında veya sunucu tarafı geçici hatalarında yeniden dene.\n        // 401/403 gibi client hatalarında yeniden deneme, çünkü bu auth akışını geciktirir.\n        if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) && error.response.status >= 400 && error.response.status < 500) {\n            return false;\n        }\n        return axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isNetworkError(error) || axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isIdempotentRequestError(error);\n    },\n    retryDelay: (retryCount, error)=>{\n        console.warn(\"[axios-retry] Request failed: \".concat(error.message, \". Retry attempt #\").concat(retryCount, \"...\"));\n        // Her denemede bekleme süresini artır (1s, 2s, 4s)\n        return Math.pow(2, retryCount - 1) * 1000;\n    }\n});\n// Refresh işlemi devam ediyor mu kontrolü\nlet isRefreshing = false;\nlet failedQueue = [];\nconst processQueue = function(error) {\n    let token = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    failedQueue.forEach((param)=>{\n        let { resolve, reject } = param;\n        if (error) {\n            reject(error);\n        } else {\n            resolve(token);\n        }\n    });\n    failedQueue = [];\n};\n// Herkese açık ve engellenmemesi gereken endpoint'ler\nconst publicEndpoints = [\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REGISTER,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGOUT\n];\n// Request interceptor - Cookie'leri otomatik gönder (HttpOnly cookie'ler için)\napiClient.interceptors.request.use((config)=>{\n    var _config_method;\n    console.log('📡 API Request başlıyor:', (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(), config.url);\n    console.log('🍪 withCredentials:', config.withCredentials);\n    // HttpOnly cookie'ler JavaScript ile okunamaz, bu normal bir durum\n    // withCredentials: true olduğu için browser cookie'leri otomatik gönderir\n    // Backend HttpOnly cookie'yi kontrol edecek\n    // Eğer cookie JavaScript ile okunabiliyorsa Authorization header'ına da ekle\n    const accessToken = getCookieValue('AccessToken');\n    if (accessToken) {\n        config.headers.Authorization = \"Bearer \".concat(accessToken);\n        console.log('🔑 Authorization header eklendi (JS readable cookie)');\n    } else {\n        console.log('🔑 Cookie HttpOnly olabilir - browser otomatik gönderecek');\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor - Token yenileme ve hata yönetimi\napiClient.interceptors.response.use((response)=>{\n    // Başarılı response'larda network status'u online'a çek\n    const currentStatus = _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().status;\n    if (currentStatus !== 'online') {\n        console.log('✅ API başarılı - Network status online\\'a çekiliyor');\n        _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('online');\n    }\n    return response;\n}, async (error)=>{\n    var _originalRequest_url, _originalRequest_url1, _error_response, _error_response1;\n    const originalRequest = error.config;\n    // Login endpoint'inden gelen 401 hatasını token refresh döngüsünden çıkar\n    // User/Me endpoint'ini bu kontrolden kaldırıyoruz ki token süresi dolduğunda yenileyebilsin.\n    // REFRESH endpoint'ini de döngüden çıkarıyoruz ki sonsuz döngüye girmesin.\n    if ((((_originalRequest_url = originalRequest.url) === null || _originalRequest_url === void 0 ? void 0 : _originalRequest_url.includes(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN)) || ((_originalRequest_url1 = originalRequest.url) === null || _originalRequest_url1 === void 0 ? void 0 : _originalRequest_url1.includes(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN))) && ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        return Promise.reject(error);\n    }\n    // HİBRİT ÇÖZÜM - HttpOnly Cookie'ler için Güncellenmiş Yaklaşım:\n    // HttpOnly cookie'ler JavaScript ile okunamaz, bu yüzden cookie varlığını kontrol edemeyiz.\n    // Bunun yerine, token yenileme isteğini gönderip sonucuna göre karar vereceğiz.\n    // Eğer refresh token yoksa, backend 401 döndürecek ve biz bunu yakalayacağız.\n    // 1. ÖNCELİK: Token yenileme mantığı\n    // 401 durumunda token yenileme - retry flag kontrolü ile döngüyü engelle\n    if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true; // Bu request'in retry edildiğini işaretle\n        if (isRefreshing) {\n            // Eğer refresh işlemi devam ediyorsa, kuyruğa ekle\n            return new Promise((resolve, reject)=>{\n                failedQueue.push({\n                    resolve,\n                    reject\n                });\n            }).then(()=>{\n                // Refresh tamamlandıktan sonra orijinal isteği tekrar gönder\n                return apiClient(originalRequest);\n            }).catch((err)=>{\n                return Promise.reject(err);\n            });\n        }\n        isRefreshing = true;\n        // Refresh token için özel retry mekanizması\n        const attemptRefresh = async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n            try {\n                const refreshResponse = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN);\n                return refreshResponse;\n            } catch (refreshError) {\n                var _refreshError_response;\n                // Retry koşulları:\n                // 1. Ağ hatası (timeout, connection error vb.)\n                // 2. 401 hatası ama henüz max retry'a ulaşmadıysak (timeout nedeniyle 401 olabilir)\n                const isNetworkError = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && !refreshError.response;\n                const is401Error = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && ((_refreshError_response = refreshError.response) === null || _refreshError_response === void 0 ? void 0 : _refreshError_response.status) === 401;\n                const shouldRetry = (isNetworkError || is401Error) && retryCount < 2;\n                if (shouldRetry) {\n                    console.log(\"\\uD83D\\uDD04 Refresh token denemesi \".concat(retryCount + 1, \"/3 başarısız (\").concat(isNetworkError ? 'Ağ hatası' : '401 - Timeout olabilir', \"). \").concat(retryCount < 1 ? 'Tekrar deneniyor...' : 'Son deneme yapılıyor...'));\n                    // Exponential backoff: 1s, 2s, 4s\n                    const delay = Math.pow(2, retryCount) * 1000;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return attemptRefresh(retryCount + 1);\n                }\n                // Max retry'a ulaştık veya kesin bir hata aldık\n                throw refreshError;\n            }\n        };\n        try {\n            // Refresh token çağrısı - retry mekanizması ile\n            const refreshResponse = await attemptRefresh();\n            // Tüm bekleyen istekleri başarılı olarak işaretle\n            processQueue(null);\n            isRefreshing = false;\n            // Başarılı refresh sonrası orijinal isteği tekrar gönder\n            return apiClient(originalRequest);\n        } catch (refreshError) {\n            var _refreshError_response;\n            // ÖNEMLİ: isRefreshing bayrağını mutlaka sıfırla\n            isRefreshing = false;\n            // 3 deneme sonrasında da 401 alıyorsak, bu gerçekten kullanıcının giriş yapmadığı anlamına gelir\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && ((_refreshError_response = refreshError.response) === null || _refreshError_response === void 0 ? void 0 : _refreshError_response.status) === 401) {\n                console.log('🚪 3 deneme sonrasında da 401 hatası. Kullanıcı gerçekten giriş yapmamış.');\n                // Tüm bekleyen istekleri orijinal hata ile reddet\n                processQueue(error, null);\n                // Orijinal hatayı döndür ki uygulama \"giriş yapmamış\" durumuna geçsin\n                return Promise.reject(error);\n            }\n            // Diğer hatalar için normal işlem\n            processQueue(refreshError, null);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && !refreshError.response) {\n                // 3 deneme sonrasında hala ağ hatası alıyorsak, bu ciddi bir bağlantı sorunu\n                console.log('🔌 3 deneme sonrasında refresh token yenilenemedi. Ağ bağlantısı sorunlu.');\n                _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('reconnecting');\n            } else {\n                console.log('💥 Beklenmedik hata sonrası auth:force-logout event gönderiliyor');\n                window.dispatchEvent(new CustomEvent('auth:force-logout'));\n            }\n            return Promise.reject(refreshError);\n        }\n    }\n    // 2. ÖNCELİK: Genel ağ hatalarını yakalama\n    // Eğer hata 401 değilse ve bir ağ hatasıysa (sunucudan yanıt yoksa),\n    // bu genel bir internet kesintisidir.\n    if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error) && !error.response) {\n        console.log('🔌 Genel ağ hatası algılandı. Yeniden bağlanma moduna geçiliyor.');\n        _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('reconnecting');\n        // Burada hatayı yutup banner'ın çalışmasına izin veriyoruz.\n        // Component'in tekrar denemesi için hatayı reject etmiyoruz ki sürekli error state'i göstermesin.\n        // Bunun yerine, bir daha asla çözülmeyecek bir promise döndürerek isteği askıda bırakıyoruz.\n        return new Promise(()=>{});\n    }\n    // Diğer tüm hatalar (500, 404, 403 vb.) normal şekilde componente geri dönsün.\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n// ===========================================\n// ADDRESS SERVICES\n// ===========================================\nconst addressService = {\n    // Kullanıcının adreslerini listele\n    async getAddresses () {\n        try {\n            console.log('📍 Adresler alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.USER_ADDRESSES);\n            console.log('📍 API Response:', response);\n            console.log('📍 Response data:', response.data);\n            console.log('📍 Response status:', response.status);\n            console.log('📍 Response data type:', typeof response.data);\n            console.log('📍 Response data is array:', Array.isArray(response.data));\n            // Eğer response.data bir object ise, içindeki property'leri kontrol et\n            if (typeof response.data === 'object' && response.data !== null) {\n                console.log('📍 Response data keys:', Object.keys(response.data));\n                console.log('📍 Response data values:', Object.values(response.data));\n                // Muhtemel nested yapıları kontrol et\n                if (response.data.data) {\n                    console.log('📍 Nested data found:', response.data.data);\n                    console.log('📍 Nested data is array:', Array.isArray(response.data.data));\n                }\n                if (response.data.addresses) {\n                    console.log('📍 Addresses property found:', response.data.addresses);\n                    console.log('📍 Addresses is array:', Array.isArray(response.data.addresses));\n                }\n                if (response.data.result) {\n                    console.log('📍 Result property found:', response.data.result);\n                    console.log('📍 Result is array:', Array.isArray(response.data.result));\n                }\n            }\n            // Farklı response yapılarını dene\n            let addressData = response.data;\n            // Eğer response.data.data varsa ve array ise onu kullan\n            if (response.data.data && Array.isArray(response.data.data)) {\n                addressData = response.data.data;\n                console.log('📍 Using nested data array');\n            } else if (response.data.addresses && Array.isArray(response.data.addresses)) {\n                addressData = response.data.addresses;\n                console.log('📍 Using addresses property');\n            } else if (response.data.result && Array.isArray(response.data.result)) {\n                addressData = response.data.result;\n                console.log('📍 Using result property');\n            } else if (Array.isArray(response.data)) {\n                addressData = response.data;\n                console.log('📍 Using direct response data');\n            } else {\n                console.warn('📍 No valid array found in response, using empty array');\n                addressData = [];\n            }\n            console.log('📍 Final address data:', addressData);\n            console.log('📍 Final address data type:', typeof addressData);\n            console.log('📍 Final address data is array:', Array.isArray(addressData));\n            console.log('📍 Final address count:', addressData.length);\n            return {\n                success: true,\n                data: addressData\n            };\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error('❌ Adresler alınırken hata:', error);\n            console.error('❌ Error response:', error.response);\n            console.error('❌ Error data:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            return {\n                success: false,\n                error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Adresler alınırken bir hata oluştu',\n                data: [] // Hata durumunda boş array döndür\n            };\n        }\n    },\n    // Yeni adres ekle\n    async createAddress (addressData) {\n        try {\n            console.log('➕ Yeni adres oluşturuluyor:', addressData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_ADDRESS, addressData);\n            console.log('✅ Adres oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Adres eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Adres eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Adres sil\n    async deleteAddress (addressId, userId) {\n        try {\n            console.log('🗑️ Adres siliniyor:', {\n                addressId,\n                userId\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_ADDRESS, {\n                addressId,\n                userId\n            });\n            console.log('✅ Adres silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Adres silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Adres silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Varsayılan adres ayarla\n    async setDefaultAddress (addressId) {\n        try {\n            console.log('⭐ Varsayılan adres ayarlanıyor:', {\n                addressId\n            });\n            const response = await apiClient.post(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.SET_DEFAULT_ADDRESS, \"?addressId=\").concat(addressId));\n            console.log('✅ Varsayılan adres ayarlama başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Varsayılan adres ayarlanırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Varsayılan adres ayarlanırken bir hata oluştu'\n            };\n        }\n    }\n};\n// ===========================================\n// USER SERVICES\n// ===========================================\nconst userService = {\n    // Kullanıcı profilini güncelle\n    async updateProfile (profileData) {\n        try {\n            console.log('👤 Profil güncelleniyor:', profileData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PROFILE, profileData);\n            console.log('✅ Profil güncelleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Profil güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Profil güncellenirken bir hata oluştu'\n            };\n        }\n    },\n    // Admin kullanıcıları getir (filtreleme ve sayfalama ile)\n    async getUsers (params) {\n        try {\n            // Request body oluştur\n            const requestBody = {\n                page: params.page || 1,\n                pageSize: params.pageSize || 10,\n                search: params.search || \"\"\n            };\n            // Sadece roleId 0'dan farklıysa ekle\n            if (params.roleId && params.roleId > 0) {\n                requestBody.roleId = params.roleId;\n            }\n            // Sadece isActive parametresi gönderildiyse ekle\n            if (params.isActive !== undefined) {\n                requestBody.isActive = params.isActive;\n            }\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_USERS, requestBody);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcılar alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcılar alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kullanıcı rol sayılarını getir\n    async getUserRoleCounts () {\n        try {\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_USER_ROLE_COUNTS);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı rol sayıları alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcı rol sayıları alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Kullanıcı indirim oranını getir\n    async getDiscountRate () {\n        try {\n            console.log('💰 Kullanıcı indirim oranı alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_DISCOUNT_RATE);\n            console.log('✅ Kullanıcı indirim oranı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı indirim oranı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcı indirim oranı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Sepet tipini güncelle (customer price toggle)\n    async updateCartType () {\n        try {\n            console.log('🛒 Sepet tipi güncelleniyor...');\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_CART_TYPE);\n            console.log('✅ Sepet tipi başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet tipi güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Sepet tipi güncellenirken bir hata oluştu',\n                data: null\n            };\n        }\n    }\n};\n// ===========================================\n// PRODUCT SERVICES\n// ===========================================\nconst productService = {\n    // Markaları getir\n    async getBrands () {\n        try {\n            console.log('🏷️ Markalar alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_BRANDS);\n            console.log('✅ Markalar başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Markalar alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Markalar alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Markaya göre kategorileri getir\n    async getCategoriesByBrand (brandId) {\n        try {\n            console.log('📂 Kategoriler alınıyor, brandId:', brandId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATEGORIES_BY_BRAND, \"/\").concat(brandId));\n            console.log('✅ Kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kategoriye göre alt kategorileri getir\n    async getSubCategories (categoryId) {\n        try {\n            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORIES, \"/\").concat(categoryId, \"/subcategories\"));\n            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Alt kategori özelliklerini getir\n    async getSubCategoryFeatures (subCategoryId) {\n        try {\n            console.log('🔧 Alt kategori özellikleri alınıyor, subCategoryId:', subCategoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORY_FEATURES, \"/\").concat(subCategoryId));\n            console.log('✅ Alt kategori özellikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategori özellikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategori özellikleri alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Özellik değerlerini getir\n    async getFeatureValues (definitionId) {\n        try {\n            console.log('🏷️ Özellik değerleri alınıyor, definitionId:', definitionId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_FEATURE_VALUES, \"/\").concat(definitionId));\n            console.log('✅ Özellik değerleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Özellik değerleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Özellik değerleri alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Tüm özellik tanımlarını getir\n    async getAllFeatureDefinitions () {\n        try {\n            console.log('🔍 Tüm özellik tanımları alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ALL_FEATURE_DEFINITIONS);\n            console.log('✅ Tüm özellik tanımları başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Tüm özellik tanımları alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Tüm özellik tanımları alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Yeni ürün ekle (tam ürün - varyantlarla birlikte)\n    async createFullProduct (productData) {\n        try {\n            console.log('➕ Tam ürün oluşturuluyor:', productData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_FULL_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Tam ürün oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Tam ürün oluşturulurken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün oluşturulurken bir hata oluştu'\n            };\n        }\n    },\n    // Satıcı ürünü ekle (dealership product - PV/CV/SP olmadan)\n    async createDealershipProduct (productData) {\n        try {\n            console.log('➕ Satıcı ürünü oluşturuluyor:', productData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_DEALERSHIP_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Satıcı ürünü oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Satıcı ürünü oluşturulurken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün oluşturulurken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görseli ekle\n    async addProductImage (imageData) {\n        try {\n            console.log('🖼️ Ürün görseli ekleniyor:', imageData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_PRODUCT_IMAGE, imageData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Ürün görseli ekleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görselini sil\n    async deleteProductImage (imageId) {\n        try {\n            console.log('🗑️ Ürün görseli siliniyor, imageId:', imageId);\n            const response = await apiClient.delete(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PRODUCT_IMAGE, \"/\").concat(imageId));\n            console.log('✅ Ürün görseli silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görselini değiştir\n    async replaceProductImage (imageId, newImageFile) {\n        try {\n            console.log('🔄 Ürün görseli değiştiriliyor, imageId:', imageId);\n            const formData = new FormData();\n            formData.append('file', newImageFile);\n            const response = await apiClient.put(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REPLACE_PRODUCT_IMAGE, \"/\").concat(imageId), formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Ürün görseli değiştirme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli değiştirilirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli değiştirilirken bir hata oluştu'\n            };\n        }\n    },\n    // Admin ürünlerini getir\n    async getAdminProducts (params) {\n        try {\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ADMIN_PRODUCTS, {\n                params\n            });\n            return Array.isArray(response.data) ? response.data : [];\n        } catch (error) {\n            console.error('❌ Admin ürünleri alınırken hata:', error);\n            return [];\n        }\n    },\n    // Kullanıcıya ait ürünleri getir\n    async getMyProducts (params) {\n        try {\n            console.log('📦 Kullanıcıya ait ürünler alınıyor:', params);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_MY_PRODUCTS, params);\n            console.log('✅ Kullanıcıya ait ürünler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcıya ait ürünler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürünler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Dealership ürün detayını getir\n    async getDealershipProductDetail (productId) {\n        try {\n            console.log('📦 Dealership ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_DEALERSHIP_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Dealership ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Dealership ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Ürünü sil\n    async deleteProduct (productId) {\n        try {\n            console.log('🗑️ Ürün siliniyor, productId:', productId);\n            // API GET metodu ve query parametresi beklediği için ona uygun istek atıyoruz.\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PRODUCT, {\n                params: {\n                    productId\n                }\n            });\n            console.log('✅ Ürün silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürünü güncelle (tam ürün - varyantlarla birlikte)\n    async updateFullProduct (productData) {\n        try {\n            console.log('🔄 Tam ürün güncelleniyor:', productData);\n            // FormData içeriğini detaylı logla\n            console.log('📋 FormData içeriği:');\n            for (let [key, value] of productData.entries()){\n                console.log(\"\".concat(key, \":\"), value);\n            }\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_FULL_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Tam ürün güncelleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2, _error_response_data1, _error_response3;\n            console.error('❌ Tam ürün güncellenirken hata:', error);\n            console.error('❌ Hata detayları:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error('❌ HTTP Status:', (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            // Hata mesajını daha detaylı döndür\n            const errorMessage = ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data1 = _error_response3.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.title) || error.message || 'Ürün güncellenirken bir hata oluştu';\n            throw new Error(errorMessage);\n        }\n    },\n    // Admin ürün istatistiklerini getir\n    async getAdminProductStatistics () {\n        try {\n            console.log('📊 Admin ürün istatistikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ADMIN_PRODUCT_STATISTICS);\n            console.log('✅ Admin ürün istatistikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Admin ürün istatistikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'İstatistikler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Kullanıcı ürün istatistiklerini getir\n    async getMyProductStatistics () {\n        try {\n            console.log('📊 Kullanıcı ürün istatistikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_MY_PRODUCT_STATISTICS);\n            console.log('✅ Kullanıcı ürün istatistikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data.data // API response'u data wrapper'ı içinde geliyor\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı ürün istatistikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'İstatistikler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Basit ürün güncelleme (dealership için)\n    async updateSimpleProduct (productFormData) {\n        console.log('🔄 API Service: Basit ürün güncelleniyor...');\n        console.log('🔗 Endpoint:', _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT);\n        // FormData içeriğini logla\n        console.log('📋 API Service: FormData contents:');\n        for (let [key, value] of productFormData.entries()){\n            if (value instanceof File) {\n                console.log(\"  \".concat(key, \": File(\").concat(value.name, \", \").concat(value.size, \" bytes, \").concat(value.type, \")\"));\n            } else {\n                console.log(\"  \".concat(key, \": \").concat(value));\n            }\n        }\n        const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT, productFormData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        console.log('✅ API Service: Basit ürün başarıyla güncellendi');\n        console.log('📄 Response status:', response.status);\n        console.log('📄 Response data:', response.data);\n        return response.data;\n    },\n    // Ürün detayını getir\n    async getProductDetail (productId) {\n        try {\n            console.log('📦 Ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Catalog ürün detayını getir (public)\n    async getCatalogProductDetail (productId) {\n        try {\n            console.log('📦 Catalog ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATALOG_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Catalog ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Catalog ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Ürün durumunu güncelle (onay/red)\n    async updateProductStatus (productId, isApproved, message) {\n        try {\n            console.log('🔄 Ürün durumu güncelleniyor:', {\n                productId,\n                isApproved,\n                message\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PRODUCT_STATUS, {\n                productId,\n                isApproved,\n                message\n            });\n            console.log('✅ Ürün durumu başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün durumu güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün durumu güncellenirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün admin notunu getir\n    async getProductMessage (productId) {\n        try {\n            console.log('📝 Ürün admin notu alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_PRODUCT_MESSAGE, \"/\").concat(productId));\n            console.log('✅ Ürün admin notu başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error('❌ Ürün admin notu alınırken hata:', error);\n            // 404 hatası ise kayıt yok demektir\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                return {\n                    success: true,\n                    data: null\n                };\n            }\n            // Diğer hatalar için false döndür\n            return {\n                success: false,\n                error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün admin notu alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Tüm kategorileri getir (public)\n    async getCategories () {\n        try {\n            console.log('📂 Kategoriler alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATEGORIES);\n            console.log('✅ Kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kategoriye göre alt kategorileri getir (public)\n    async getSubCategoriesByCategory (categoryId) {\n        try {\n            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORIES_BY_CATEGORY.replace('{categoryId}', categoryId.toString())));\n            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Ürünleri filtrele (public)\n    async filterProducts (filterRequest) {\n        try {\n            console.log('🔍 Ürünler filtreleniyor:', filterRequest);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.FILTER_PRODUCTS, filterRequest);\n            console.log('✅ Ürünler başarıyla filtrelendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürünler filtrelenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürünler filtrelenirken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Reference data getir (public)\n    async getReferenceData () {\n        try {\n            console.log('📋 Reference data alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_REFERENCE_DATA);\n            console.log('✅ Reference data başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Reference data alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Reference data alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    }\n};\nconst cartService = {\n    // Sepete ürün ekle\n    async addToCart (productVariantId, quantity, isCustomerPrice) {\n        try {\n            console.log('🛒 Sepete ürün ekleniyor:', {\n                productVariantId,\n                quantity,\n                isCustomerPrice\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_TO_CART, {\n                productVariantId,\n                quantity,\n                isCustomerPrice\n            });\n            console.log('✅ Ürün sepete başarıyla eklendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepete ürün eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün sepete eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Sepet içeriklerini getir\n    async getCartItems () {\n        try {\n            console.log('🛒 Sepet içerikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CART_ITEMS);\n            console.log('✅ Sepet içerikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet içerikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Sepet içerikleri alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Sepetteki ürün sayısını getir\n    async getCartCount () {\n        try {\n            console.log('🛒 Sepet ürün sayısı alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CART_COUNT);\n            console.log('✅ Sepet ürün sayısı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet ürün sayısı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Sepet ürün sayısı alınırken bir hata oluştu',\n                data: 0\n            };\n        }\n    },\n    // Sepetten ürün çıkar\n    async removeFromCart (productVariantId) {\n        try {\n            console.log('🗑️ Sepetten ürün çıkarılıyor:', {\n                productVariantId\n            });\n            const url = \"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REMOVE_FROM_CART, \"/\").concat(productVariantId);\n            console.log('🔍 API URL:', url);\n            console.log('🔍 API_ENDPOINTS.REMOVE_FROM_CART:', _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REMOVE_FROM_CART);\n            const response = await apiClient.delete(url);\n            console.log('✅ Ürün sepetten başarıyla çıkarıldı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2;\n            console.error('❌ Sepetten ürün çıkarılırken hata:', error);\n            console.error('❌ Error response:', error.response);\n            console.error('❌ Error status:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status);\n            console.error('❌ Error data:', (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data);\n            return {\n                success: false,\n                error: ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün sepetten çıkarılırken bir hata oluştu'\n            };\n        }\n    },\n    // Sepet ürün miktarını güncelle\n    async updateCartQuantity (productVariantId, quantity) {\n        try {\n            console.log('🔄 Sepet ürün miktarı güncelleniyor:', {\n                productVariantId,\n                quantity\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_CART_QUANTITY, {\n                productVariantId,\n                quantity\n            });\n            console.log('✅ Sepet ürün miktarı başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet ürün miktarı güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün miktarı güncellenirken bir hata oluştu'\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});