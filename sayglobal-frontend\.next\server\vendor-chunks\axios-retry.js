"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/axios-retry";
exports.ids = ["vendor-chunks/axios-retry"];
exports.modules = {

/***/ "(ssr)/./node_modules/axios-retry/dist/esm/index.js":
/*!****************************************************!*\
  !*** ./node_modules/axios-retry/dist/esm/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_OPTIONS: () => (/* binding */ DEFAULT_OPTIONS),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   exponentialDelay: () => (/* binding */ exponentialDelay),\n/* harmony export */   isIdempotentRequestError: () => (/* binding */ isIdempotentRequestError),\n/* harmony export */   isNetworkError: () => (/* binding */ isNetworkError),\n/* harmony export */   isNetworkOrIdempotentRequestError: () => (/* binding */ isNetworkOrIdempotentRequestError),\n/* harmony export */   isRetryableError: () => (/* binding */ isRetryableError),\n/* harmony export */   isSafeRequestError: () => (/* binding */ isSafeRequestError),\n/* harmony export */   linearDelay: () => (/* binding */ linearDelay),\n/* harmony export */   namespace: () => (/* binding */ namespace),\n/* harmony export */   retryAfter: () => (/* binding */ retryAfter)\n/* harmony export */ });\n/* harmony import */ var is_retry_allowed__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-retry-allowed */ \"(ssr)/./node_modules/is-retry-allowed/index.js\");\n\nconst namespace = 'axios-retry';\nfunction isNetworkError(error) {\n    const CODE_EXCLUDE_LIST = ['ERR_CANCELED', 'ECONNABORTED'];\n    if (error.response) {\n        return false;\n    }\n    if (!error.code) {\n        return false;\n    }\n    // Prevents retrying timed out & cancelled requests\n    if (CODE_EXCLUDE_LIST.includes(error.code)) {\n        return false;\n    }\n    // Prevents retrying unsafe errors\n    return is_retry_allowed__WEBPACK_IMPORTED_MODULE_0__(error);\n}\nconst SAFE_HTTP_METHODS = ['get', 'head', 'options'];\nconst IDEMPOTENT_HTTP_METHODS = SAFE_HTTP_METHODS.concat(['put', 'delete']);\nfunction isRetryableError(error) {\n    return (error.code !== 'ECONNABORTED' &&\n        (!error.response ||\n            error.response.status === 429 ||\n            (error.response.status >= 500 && error.response.status <= 599)));\n}\nfunction isSafeRequestError(error) {\n    if (!error.config?.method) {\n        // Cannot determine if the request can be retried\n        return false;\n    }\n    return isRetryableError(error) && SAFE_HTTP_METHODS.indexOf(error.config.method) !== -1;\n}\nfunction isIdempotentRequestError(error) {\n    if (!error.config?.method) {\n        // Cannot determine if the request can be retried\n        return false;\n    }\n    return isRetryableError(error) && IDEMPOTENT_HTTP_METHODS.indexOf(error.config.method) !== -1;\n}\nfunction isNetworkOrIdempotentRequestError(error) {\n    return isNetworkError(error) || isIdempotentRequestError(error);\n}\nfunction retryAfter(error = undefined) {\n    const retryAfterHeader = error?.response?.headers['retry-after'];\n    if (!retryAfterHeader) {\n        return 0;\n    }\n    // if the retry after header is a number, convert it to milliseconds\n    let retryAfterMs = (Number(retryAfterHeader) || 0) * 1000;\n    // If the retry after header is a date, get the number of milliseconds until that date\n    if (retryAfterMs === 0) {\n        retryAfterMs = (new Date(retryAfterHeader).valueOf() || 0) - Date.now();\n    }\n    return Math.max(0, retryAfterMs);\n}\nfunction noDelay(_retryNumber = 0, error = undefined) {\n    return Math.max(0, retryAfter(error));\n}\nfunction exponentialDelay(retryNumber = 0, error = undefined, delayFactor = 100) {\n    const calculatedDelay = 2 ** retryNumber * delayFactor;\n    const delay = Math.max(calculatedDelay, retryAfter(error));\n    const randomSum = delay * 0.2 * Math.random(); // 0-20% of the delay\n    return delay + randomSum;\n}\n/**\n * Linear delay\n * @param {number | undefined} delayFactor - delay factor in milliseconds (default: 100)\n * @returns {function} (retryNumber: number, error: AxiosError | undefined) => number\n */\nfunction linearDelay(delayFactor = 100) {\n    return (retryNumber = 0, error = undefined) => {\n        const delay = retryNumber * delayFactor;\n        return Math.max(delay, retryAfter(error));\n    };\n}\nconst DEFAULT_OPTIONS = {\n    retries: 3,\n    retryCondition: isNetworkOrIdempotentRequestError,\n    retryDelay: noDelay,\n    shouldResetTimeout: false,\n    onRetry: () => { },\n    onMaxRetryTimesExceeded: () => { },\n    validateResponse: null\n};\nfunction getRequestOptions(config, defaultOptions) {\n    return { ...DEFAULT_OPTIONS, ...defaultOptions, ...config[namespace] };\n}\nfunction setCurrentState(config, defaultOptions, resetLastRequestTime = false) {\n    const currentState = getRequestOptions(config, defaultOptions || {});\n    currentState.retryCount = currentState.retryCount || 0;\n    if (!currentState.lastRequestTime || resetLastRequestTime) {\n        currentState.lastRequestTime = Date.now();\n    }\n    config[namespace] = currentState;\n    return currentState;\n}\nfunction fixConfig(axiosInstance, config) {\n    // @ts-ignore\n    if (axiosInstance.defaults.agent === config.agent) {\n        // @ts-ignore\n        delete config.agent;\n    }\n    if (axiosInstance.defaults.httpAgent === config.httpAgent) {\n        delete config.httpAgent;\n    }\n    if (axiosInstance.defaults.httpsAgent === config.httpsAgent) {\n        delete config.httpsAgent;\n    }\n}\nasync function shouldRetry(currentState, error) {\n    const { retries, retryCondition } = currentState;\n    const shouldRetryOrPromise = (currentState.retryCount || 0) < retries && retryCondition(error);\n    // This could be a promise\n    if (typeof shouldRetryOrPromise === 'object') {\n        try {\n            const shouldRetryPromiseResult = await shouldRetryOrPromise;\n            // keep return true unless shouldRetryPromiseResult return false for compatibility\n            return shouldRetryPromiseResult !== false;\n        }\n        catch (_err) {\n            return false;\n        }\n    }\n    return shouldRetryOrPromise;\n}\nasync function handleRetry(axiosInstance, currentState, error, config) {\n    currentState.retryCount += 1;\n    const { retryDelay, shouldResetTimeout, onRetry } = currentState;\n    const delay = retryDelay(currentState.retryCount, error);\n    // Axios fails merging this configuration to the default configuration because it has an issue\n    // with circular structures: https://github.com/mzabriskie/axios/issues/370\n    fixConfig(axiosInstance, config);\n    if (!shouldResetTimeout && config.timeout && currentState.lastRequestTime) {\n        const lastRequestDuration = Date.now() - currentState.lastRequestTime;\n        const timeout = config.timeout - lastRequestDuration - delay;\n        if (timeout <= 0) {\n            return Promise.reject(error);\n        }\n        config.timeout = timeout;\n    }\n    config.transformRequest = [(data) => data];\n    await onRetry(currentState.retryCount, error, config);\n    if (config.signal?.aborted) {\n        return Promise.resolve(axiosInstance(config));\n    }\n    return new Promise((resolve) => {\n        const abortListener = () => {\n            clearTimeout(timeout);\n            resolve(axiosInstance(config));\n        };\n        const timeout = setTimeout(() => {\n            resolve(axiosInstance(config));\n            if (config.signal?.removeEventListener) {\n                config.signal.removeEventListener('abort', abortListener);\n            }\n        }, delay);\n        if (config.signal?.addEventListener) {\n            config.signal.addEventListener('abort', abortListener, { once: true });\n        }\n    });\n}\nasync function handleMaxRetryTimesExceeded(currentState, error) {\n    if (currentState.retryCount >= currentState.retries)\n        await currentState.onMaxRetryTimesExceeded(error, currentState.retryCount);\n}\nconst axiosRetry = (axiosInstance, defaultOptions) => {\n    const requestInterceptorId = axiosInstance.interceptors.request.use((config) => {\n        setCurrentState(config, defaultOptions, true);\n        if (config[namespace]?.validateResponse) {\n            // by setting this, all HTTP responses will be go through the error interceptor first\n            config.validateStatus = () => false;\n        }\n        return config;\n    });\n    const responseInterceptorId = axiosInstance.interceptors.response.use(null, async (error) => {\n        const { config } = error;\n        // If we have no information to retry the request\n        if (!config) {\n            return Promise.reject(error);\n        }\n        const currentState = setCurrentState(config, defaultOptions);\n        if (error.response && currentState.validateResponse?.(error.response)) {\n            // no issue with response\n            return error.response;\n        }\n        if (await shouldRetry(currentState, error)) {\n            return handleRetry(axiosInstance, currentState, error, config);\n        }\n        await handleMaxRetryTimesExceeded(currentState, error);\n        return Promise.reject(error);\n    });\n    return { requestInterceptorId, responseInterceptorId };\n};\n// Compatibility with CommonJS\naxiosRetry.isNetworkError = isNetworkError;\naxiosRetry.isSafeRequestError = isSafeRequestError;\naxiosRetry.isIdempotentRequestError = isIdempotentRequestError;\naxiosRetry.isNetworkOrIdempotentRequestError = isNetworkOrIdempotentRequestError;\naxiosRetry.exponentialDelay = exponentialDelay;\naxiosRetry.linearDelay = linearDelay;\naxiosRetry.isRetryableError = isRetryableError;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosRetry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/axios-retry/dist/esm/index.js\n");

/***/ })

};
;