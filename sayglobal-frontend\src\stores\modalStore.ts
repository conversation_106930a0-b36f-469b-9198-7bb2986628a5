import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { SubCategoryFeature, VariantFormData } from '@/types';

// 🎯 Modal tipleri
interface ModalState {
    // Modal visibility states
    editPersonalInfo: boolean;
    referenceRegistration: boolean;
    addAddress: boolean;
    setDefaultConfirmation: boolean; // 🏠 Varsayılan adres onay modal'ı
    banking: boolean; // 🏦 Banking modal'ı
    addCard: boolean; // 💳 Add Card modal'ı
    setDefaultCard: boolean; // ⭐ Set Default Card modal'ı
    registerSuccess: boolean; // 🎉 Register Success modal'ı
    successNotification: boolean; // 🎉 Success Notification modal'ı (Category Management için)

    // Product Modals
    productCategorySelector: boolean;
    productVariantSetup: boolean;
    productVariant: boolean;
    productDeleteConfirmation: boolean;
    // TODO: Diğer modal'lar buraya e<PERSON>k
    // addToCart: boolean;
    // favorite: boolean;
}

// 🎯 Modal data tipleri
interface ModalData {
    // EditPersonalInfo modal için user data
    editPersonalInfoUser: {
        firstName: string;
        lastName: string;
        phoneNumber: string;
    } | null;

    // ReferenceRegistration modal için form data
    referenceRegistrationData: {
        firstName: string;
        lastName: string;
        email: string;
        phoneNumber: string;
        notes: string;
    } | null;

    // AddAddress modal için form data
    addAddressData: {
        title: string;
        fullAddress: string;
        city: string;
        district: string;
        postalCode: string;
        isDefault: boolean;
    } | null;

    // SetDefaultConfirmation modal için data
    setDefaultConfirmationData: {
        targetAddress: {
            id: number;
            title: string;
            fullAddress: string;
            city: string;
            district: string;
        };
        deletedDefaultAddress?: {
            id: number;
            title: string;
        };
    } | null;

    // Banking modal için data
    bankingData: {
        accountHolderName: string;
        bankName: string;
        iban: string;
        branchName: string;
        branchCode: string;
    } | null;

    // AddCard modal için data
    addCardData: {
        cardHolderName: string;
        cardType: string;
        cardNumber: string;
        expirationDate: string;
        cvv: string;
        saveCard: boolean;
        setAsDefault: boolean;
    } | null;

    // SetDefaultCard modal için data
    setDefaultCardData: {
        card: {
            id: string;
            cardType: 'visa' | 'mastercard' | 'troy';
            cardNumber: string;
            cardHolderName: string;
            expirationDate: string;
            cvv: string;
            color: string;
            isDefault: boolean;
        };
        onConfirm: () => void;
    } | null;

    // Success Notification Modal Data
    successNotificationData: {
        title: string;
        message: string;
        icon?: 'success' | 'info' | 'warning' | 'error';
        autoClose?: boolean;
        duration?: number;
    } | null;

    // Product Modal Data
    productCategorySelectorData: {
        initialData: {
            brandId: number;
            categoryId: number;
            subCategoryId: number;
            selectedFeatures: { [key: number]: number[] };
        };
    } | null;

    productVariantSetupData: {
        availableFeatures: SubCategoryFeature[];
    } | null;

    productVariantData: {
        editingVariant?: VariantFormData;
        availableFeatures: SubCategoryFeature[];
        existingVariants: VariantFormData[];
    } | null;

    productDeleteConfirmationData: {
        productId: number;
        productName: string;
        brandName: string;
        imageUrl?: string;
        onConfirm: () => void;
    } | null;

    // TODO: Diğer modal data'ları
}

// 🎯 Actions
interface ModalActions {
    // Generic modal actions
    openModal: (modalName: keyof ModalState, data?: any) => void;
    closeModal: (modalName: keyof ModalState) => void;
    closeAllModals: () => void;

    // Specific actions (daha type-safe için)
    openEditPersonalInfoModal: (userData: ModalData['editPersonalInfoUser']) => void;
    closeEditPersonalInfoModal: () => void;

    openReferenceRegistrationModal: (formData?: ModalData['referenceRegistrationData']) => void;
    closeReferenceRegistrationModal: () => void;

    openAddAddressModal: (formData?: ModalData['addAddressData']) => void;
    closeAddAddressModal: () => void;

    openSetDefaultConfirmationModal: (confirmationData: ModalData['setDefaultConfirmationData']) => void;
    closeSetDefaultConfirmationModal: () => void;

    openBankingModal: (bankingData?: ModalData['bankingData']) => void;
    closeBankingModal: () => void;

    openAddCardModal: (cardData?: ModalData['addCardData']) => void;
    closeAddCardModal: () => void;

    openSetDefaultCardModal: (cardData: ModalData['setDefaultCardData']) => void;
    closeSetDefaultCardModal: () => void;

    openRegisterSuccessModal: () => void;
    closeRegisterSuccessModal: () => void;

    openSuccessNotificationModal: (data: ModalData['successNotificationData']) => void;
    closeSuccessNotificationModal: () => void;

    // Product Modal Actions
    openProductCategorySelector: (data: ModalData['productCategorySelectorData']) => void;
    closeProductCategorySelector: () => void;
    openProductVariantSetup: (data: ModalData['productVariantSetupData']) => void;
    closeProductVariantSetup: () => void;
    openProductVariant: (data: ModalData['productVariantData']) => void;
    closeProductVariant: () => void;
    openProductDeleteConfirmation: (data: ModalData['productDeleteConfirmationData']) => void;
    closeProductDeleteConfirmation: () => void;
}

type ModalStore = ModalState & ModalData & ModalActions;

// 🎨 Initial state
const initialModalState: ModalState = {
    editPersonalInfo: false,
    referenceRegistration: false,
    addAddress: false,
    setDefaultConfirmation: false,
    banking: false,
    addCard: false,
    setDefaultCard: false,
    registerSuccess: false,
    successNotification: false,
    // Product Modals
    productCategorySelector: false,
    productVariantSetup: false,
    productVariant: false,
    productDeleteConfirmation: false,
};

const initialModalData: ModalData = {
    editPersonalInfoUser: null,
    referenceRegistrationData: null,
    addAddressData: null,
    setDefaultConfirmationData: null,
    bankingData: null,
    addCardData: null,
    setDefaultCardData: null,
    successNotificationData: null,
    // Product Modals
    productCategorySelectorData: null,
    productVariantSetupData: null,
    productVariantData: null,
    productDeleteConfirmationData: null,
};

// 🐻 Create store
export const useModalStore = create<ModalStore>()(
    devtools(
        (set, get) => ({
            // Initial states
            ...initialModalState,
            ...initialModalData,

            // Generic actions
            openModal: (modalName, data) => {
                set(
                    (state) => ({
                        ...state,
                        [modalName]: true,
                        // Data'yı da set et (eğer varsa)
                        ...(data && { [`${modalName}User`]: data }),
                    }),
                    false,
                    `modal/open/${modalName}`
                );
            },

            closeModal: (modalName) => {
                set(
                    (state) => ({
                        ...state,
                        [modalName]: false,
                        // Data'yı temizle
                        [`${modalName}User`]: null,
                    }),
                    false,
                    `modal/close/${modalName}`
                );
            },

            closeAllModals: () => {
                set(
                    {
                        ...initialModalState,
                        ...initialModalData,
                    },
                    false,
                    'modal/closeAll'
                );
            },

            // Specific typed actions
            openEditPersonalInfoModal: (userData) => {
                set(
                    {
                        editPersonalInfo: true,
                        editPersonalInfoUser: userData,
                    },
                    false,
                    'modal/openEditPersonalInfo'
                );
            },

            closeEditPersonalInfoModal: () => {
                set(
                    {
                        editPersonalInfo: false,
                        editPersonalInfoUser: null,
                    },
                    false,
                    'modal/closeEditPersonalInfo'
                );
            },

            // ReferenceRegistration modal actions
            openReferenceRegistrationModal: (formData) => {
                set(
                    {
                        referenceRegistration: true,
                        referenceRegistrationData: formData,
                    },
                    false,
                    'modal/openReferenceRegistration'
                );
            },

            closeReferenceRegistrationModal: () => {
                set(
                    {
                        referenceRegistration: false,
                        referenceRegistrationData: null,
                    },
                    false,
                    'modal/closeReferenceRegistration'
                );
            },

            // AddAddress modal actions
            openAddAddressModal: (formData) => {
                set(
                    {
                        addAddress: true,
                        addAddressData: formData,
                    },
                    false,
                    'modal/openAddAddress'
                );
            },

            closeAddAddressModal: () => {
                set(
                    {
                        addAddress: false,
                        addAddressData: null,
                    },
                    false,
                    'modal/closeAddAddress'
                );
            },

            // SetDefaultConfirmation modal actions
            openSetDefaultConfirmationModal: (confirmationData) => {
                set(
                    {
                        setDefaultConfirmation: true,
                        setDefaultConfirmationData: confirmationData,
                    },
                    false,
                    'modal/openSetDefaultConfirmation'
                );
            },

            closeSetDefaultConfirmationModal: () => {
                set(
                    {
                        setDefaultConfirmation: false,
                        setDefaultConfirmationData: null,
                    },
                    false,
                    'modal/closeSetDefaultConfirmation'
                );
            },

            // Banking modal actions
            openBankingModal: (bankingData) => {
                set(
                    {
                        banking: true,
                        bankingData: bankingData,
                    },
                    false,
                    'modal/openBanking'
                );
            },

            closeBankingModal: () => {
                set(
                    {
                        banking: false,
                        bankingData: null,
                    },
                    false,
                    'modal/closeBanking'
                );
            },

            // AddCard modal actions
            openAddCardModal: (cardData) => {
                set(
                    {
                        addCard: true,
                        addCardData: cardData,
                    },
                    false,
                    'modal/openAddCard'
                );
            },

            closeAddCardModal: () => {
                set(
                    {
                        addCard: false,
                        addCardData: null,
                    },
                    false,
                    'modal/closeAddCard'
                );
            },

            // SetDefaultCard modal actions
            openSetDefaultCardModal: (cardData) => {
                set(
                    {
                        setDefaultCard: true,
                        setDefaultCardData: cardData,
                    },
                    false,
                    'modal/openSetDefaultCard'
                );
            },

            closeSetDefaultCardModal: () => {
                set(
                    {
                        setDefaultCard: false,
                        setDefaultCardData: null,
                    },
                    false,
                    'modal/closeSetDefaultCard'
                );
            },

            // RegisterSuccess modal actions
            openRegisterSuccessModal: () => {
                set(
                    {
                        registerSuccess: true,
                    },
                    false,
                    'modal/openRegisterSuccess'
                );
            },

            closeRegisterSuccessModal: () => {
                set(
                    {
                        registerSuccess: false,
                    },
                    false,
                    'modal/closeRegisterSuccess'
                );
            },

            // Success Notification modal actions
            openSuccessNotificationModal: (data) => {
                set(
                    {
                        successNotification: true,
                        successNotificationData: data,
                    },
                    false,
                    'modal/openSuccessNotification'
                );
            },

            closeSuccessNotificationModal: () => {
                set(
                    {
                        successNotification: false,
                        successNotificationData: null,
                    },
                    false,
                    'modal/closeSuccessNotification'
                );
            },

            // Product Modals
            openProductCategorySelector: (data) => {
                set({
                    productCategorySelector: true,
                    productCategorySelectorData: data,
                }, false, 'modal/openProductCategorySelector');
            },
            closeProductCategorySelector: () => {
                set({
                    productCategorySelector: false,
                    productCategorySelectorData: null,
                }, false, 'modal/closeProductCategorySelector');
            },
            openProductVariantSetup: (data) => {
                set({
                    productVariantSetup: true,
                    productVariantSetupData: data,
                }, false, 'modal/openProductVariantSetup');
            },
            closeProductVariantSetup: () => {
                set({
                    productVariantSetup: false,
                    productVariantSetupData: null,
                }, false, 'modal/closeProductVariantSetup');
            },
            openProductVariant: (data) => {
                set({
                    productVariant: true,
                    productVariantData: data,
                }, false, 'modal/openProductVariant');
            },
            closeProductVariant: () => {
                set({
                    productVariant: false,
                    productVariantData: null,
                }, false, 'modal/closeProductVariant');
            },
            openProductDeleteConfirmation: (data) => {
                set({
                    productDeleteConfirmation: true,
                    productDeleteConfirmationData: data,
                }, false, 'modal/openProductDeleteConfirmation');
            },
            closeProductDeleteConfirmation: () => {
                set({
                    productDeleteConfirmation: false,
                    productDeleteConfirmationData: null,
                }, false, 'modal/closeProductDeleteConfirmation');
            },
        }),
        {
            name: 'modal-store', // DevTools name
            enabled: process.env.NODE_ENV === 'development',
        }
    )
);

// 🔧 Selector hooks - Performance optimization
export const useEditPersonalInfoModal = () =>
    useModalStore((state) => state.editPersonalInfo);

export const useEditPersonalInfoData = () =>
    useModalStore((state) => state.editPersonalInfoUser);

export const useReferenceRegistrationModal = () =>
    useModalStore((state) => state.referenceRegistration);

export const useReferenceRegistrationData = () =>
    useModalStore((state) => state.referenceRegistrationData);

export const useAddAddressModal = () =>
    useModalStore((state) => state.addAddress);

export const useAddAddressData = () =>
    useModalStore((state) => state.addAddressData);

export const useSetDefaultConfirmationModal = () =>
    useModalStore((state) => state.setDefaultConfirmation);

export const useSetDefaultConfirmationData = () =>
    useModalStore((state) => state.setDefaultConfirmationData);

export const useBankingModal = () =>
    useModalStore((state) => state.banking);

export const useBankingData = () =>
    useModalStore((state) => state.bankingData);

export const useAddCardModal = () =>
    useModalStore((state) => state.addCard);

export const useAddCardData = () =>
    useModalStore((state) => state.addCardData);

export const useSetDefaultCardModal = () =>
    useModalStore((state) => state.setDefaultCard);

export const useSetDefaultCardData = () =>
    useModalStore((state) => state.setDefaultCardData);

export const useRegisterSuccessModal = () =>
    useModalStore((state) => state.registerSuccess);

export const useSuccessNotificationModal = () =>
    useModalStore((state) => state.successNotification);

export const useSuccessNotificationData = () =>
    useModalStore((state) => state.successNotificationData);

// Product Modal Selectors
export const useProductCategorySelectorModal = () => useModalStore(state => state.productCategorySelector);
export const useProductCategorySelectorData = () => useModalStore(state => state.productCategorySelectorData);
export const useProductVariantSetupModal = () => useModalStore(state => state.productVariantSetup);
export const useProductVariantSetupData = () => useModalStore(state => state.productVariantSetupData);
export const useProductVariantModal = () => useModalStore(state => state.productVariant);
export const useProductVariantData = () => useModalStore(state => state.productVariantData);
export const useProductDeleteConfirmationModal = () => useModalStore(state => state.productDeleteConfirmation);
export const useProductDeleteConfirmationData = () => useModalStore(state => state.productDeleteConfirmationData);

// 🔧 Action hooks - Easier usage (Fixed: Stable reference)
export const useModalActions = () => {
    const openModal = useModalStore((state) => state.openModal);
    const closeModal = useModalStore((state) => state.closeModal);
    const closeAllModals = useModalStore((state) => state.closeAllModals);
    const openEditPersonalInfoModal = useModalStore((state) => state.openEditPersonalInfoModal);
    const closeEditPersonalInfoModal = useModalStore((state) => state.closeEditPersonalInfoModal);
    const openReferenceRegistrationModal = useModalStore((state) => state.openReferenceRegistrationModal);
    const closeReferenceRegistrationModal = useModalStore((state) => state.closeReferenceRegistrationModal);
    const openAddAddressModal = useModalStore((state) => state.openAddAddressModal);
    const closeAddAddressModal = useModalStore((state) => state.closeAddAddressModal);
    const openSetDefaultConfirmationModal = useModalStore((state) => state.openSetDefaultConfirmationModal);
    const closeSetDefaultConfirmationModal = useModalStore((state) => state.closeSetDefaultConfirmationModal);
    const openBankingModal = useModalStore((state) => state.openBankingModal);
    const closeBankingModal = useModalStore((state) => state.closeBankingModal);
    const openAddCardModal = useModalStore((state) => state.openAddCardModal);
    const closeAddCardModal = useModalStore((state) => state.closeAddCardModal);
    const openSetDefaultCardModal = useModalStore((state) => state.openSetDefaultCardModal);
    const closeSetDefaultCardModal = useModalStore((state) => state.closeSetDefaultCardModal);
    const openRegisterSuccessModal = useModalStore((state) => state.openRegisterSuccessModal);
    const closeRegisterSuccessModal = useModalStore((state) => state.closeRegisterSuccessModal);
    const openSuccessNotificationModal = useModalStore((state) => state.openSuccessNotificationModal);
    const closeSuccessNotificationModal = useModalStore((state) => state.closeSuccessNotificationModal);

    // Product Modal Actions
    const openProductCategorySelector = useModalStore((state) => state.openProductCategorySelector);
    const closeProductCategorySelector = useModalStore((state) => state.closeProductCategorySelector);
    const openProductVariantSetup = useModalStore((state) => state.openProductVariantSetup);
    const closeProductVariantSetup = useModalStore((state) => state.closeProductVariantSetup);
    const openProductVariant = useModalStore((state) => state.openProductVariant);
    const closeProductVariant = useModalStore((state) => state.closeProductVariant);
    const openProductDeleteConfirmation = useModalStore((state) => state.openProductDeleteConfirmation);
    const closeProductDeleteConfirmation = useModalStore((state) => state.closeProductDeleteConfirmation);

    // Return stable reference using useMemo-like approach
    return {
        openModal,
        closeModal,
        closeAllModals,
        openEditPersonalInfoModal,
        closeEditPersonalInfoModal,
        openReferenceRegistrationModal,
        closeReferenceRegistrationModal,
        openAddAddressModal,
        closeAddAddressModal,
        openSetDefaultConfirmationModal,
        closeSetDefaultConfirmationModal,
        openBankingModal,
        closeBankingModal,
        openAddCardModal,
        closeAddCardModal,
        openSetDefaultCardModal,
        closeSetDefaultCardModal,
        openRegisterSuccessModal,
        closeRegisterSuccessModal,
        openSuccessNotificationModal,
        closeSuccessNotificationModal,
        // Product Modals
        openProductCategorySelector,
        closeProductCategorySelector,
        openProductVariantSetup,
        closeProductVariantSetup,
        openProductVariant,
        closeProductVariant,
        openProductDeleteConfirmation,
        closeProductDeleteConfirmation,
    };
}; 