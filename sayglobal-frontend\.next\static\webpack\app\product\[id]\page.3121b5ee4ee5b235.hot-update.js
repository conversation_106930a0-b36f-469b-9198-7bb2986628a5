"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _contexts_FavoritesContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/FavoritesContext */ \"(app-pages-browser)/./src/contexts/FavoritesContext.tsx\");\n/* harmony import */ var _components_AddToCartModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AddToCartModal */ \"(app-pages-browser)/./src/components/AddToCartModal.tsx\");\n/* harmony import */ var _components_FavoriteModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/FavoriteModal */ \"(app-pages-browser)/./src/components/FavoriteModal.tsx\");\n/* harmony import */ var _components_ImageModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ImageModal */ \"(app-pages-browser)/./src/components/ImageModal.tsx\");\n/* harmony import */ var _hooks_useCatalogProducts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useCatalogProducts */ \"(app-pages-browser)/./src/hooks/useCatalogProducts.ts\");\n/* harmony import */ var _hooks_useDiscountRate__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useDiscountRate */ \"(app-pages-browser)/./src/hooks/useDiscountRate.ts\");\n/* harmony import */ var _hooks_useCart__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useCart */ \"(app-pages-browser)/./src/hooks/useCart.ts\");\n/* harmony import */ var _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/stores/catalogProductDetailStore */ \"(app-pages-browser)/./src/stores/catalogProductDetailStore.ts\");\n/* harmony import */ var _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/stores/customerPriceStore */ \"(app-pages-browser)/./src/stores/customerPriceStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDetailPage() {\n    _s();\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [productId, setProductId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddToCartModal, setShowAddToCartModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastAddedProduct, setLastAddedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFavoriteModal, setShowFavoriteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favoriteModalIsAdded, setFavoriteModalIsAdded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Hooks\n    const { addToCart } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_5__.useCart)();\n    const { addToFavorites, removeFromFavorites, isFavorite } = (0,_contexts_FavoritesContext__WEBPACK_IMPORTED_MODULE_6__.useFavorites)();\n    const { data: discountRateData, isLoading: discountRateLoading } = (0,_hooks_useDiscountRate__WEBPACK_IMPORTED_MODULE_11__.useDiscountRate)();\n    const addToCartMutation = (0,_hooks_useCart__WEBPACK_IMPORTED_MODULE_12__.useAddToCart)();\n    // Global customer price state\n    const { isCustomerPrice } = (0,_stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_14__.useCustomerPriceStore)();\n    // Store actions\n    const { setSelectedVariant, setCurrentImage, increaseQuantity, decreaseQuantity, resetState } = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore)();\n    // Store selectors\n    const selectedVariant = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSelectedVariant)();\n    const currentImage = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImage)();\n    const variantImages = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useVariantImages)();\n    const quantity = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductQuantity)();\n    const slideDirection = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSlideDirection)();\n    const currentImageIndex = (0,_stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImageIndex)();\n    // API call\n    const { data: productData, isLoading, error } = (0,_hooks_useCatalogProducts__WEBPACK_IMPORTED_MODULE_10__.useCatalogProductDetail)(productId);\n    // Initialize product ID from params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            if (id) {\n                const numericId = parseInt(id);\n                if (!isNaN(numericId)) {\n                    setProductId(numericId);\n                }\n            }\n        }\n    }[\"ProductDetailPage.useEffect\"], [\n        id\n    ]);\n    // Reset store when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            return ({\n                \"ProductDetailPage.useEffect\": ()=>{\n                    resetState();\n                }\n            })[\"ProductDetailPage.useEffect\"];\n        }\n    }[\"ProductDetailPage.useEffect\"], []); // Empty dependency array to run only on mount/unmount\n    // Loading state\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: \"\\xdcr\\xfcn Y\\xfckleniyor...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"\\xdcr\\xfcn detayları getiriliyor, l\\xfctfen bekleyin.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 13\n        }, this);\n    }\n    // Error state\n    if (error || !productData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-16 w-16 mx-auto text-gray-400 mb-4\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: \"\\xdcr\\xfcn Bulunamadı\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: (error === null || error === void 0 ? void 0 : error.message) || 'Aradığınız ürün bulunamadı veya artık mevcut değil.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/products\",\n                        className: \"inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300\",\n                        children: \"\\xdcr\\xfcnlere D\\xf6n\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 103,\n            columnNumber: 13\n        }, this);\n    }\n    const product = productData.data;\n    // Get stock status text based on API enum\n    const getStockStatusText = (stockStatus)=>{\n        switch(stockStatus){\n            case 0:\n                return {\n                    text: 'Stokta yok',\n                    color: 'text-red-600'\n                }; // OutOfStock\n            case 1:\n                return {\n                    text: 'Az stok',\n                    color: 'text-yellow-600'\n                }; // LowStock\n            case 2:\n                return {\n                    text: 'Stokta var',\n                    color: 'text-green-600'\n                }; // InStock\n            default:\n                return {\n                    text: 'Bilinmiyor',\n                    color: 'text-gray-600'\n                };\n        }\n    };\n    // Calculate points from ratios (pv, cv, sp are percentages) based on original price\n    const calculatePoints = (ratio, originalPrice)=>{\n        return Math.round(ratio / 100 * originalPrice);\n    };\n    // Calculate discounted price with membership discount and extra discount\n    const calculateDiscountedPrice = function(originalPrice, extraDiscount) {\n        let applyMembershipDiscount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n        let finalPrice = originalPrice;\n        const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n        // Müşteri fiyatları gösteriliyorsa membership discount uygulama\n        const shouldApplyMembershipDiscount = applyMembershipDiscount && !isCustomerPrice;\n        // Önce membership discount uygula (eğer varsa ve uygulanacaksa)\n        if (shouldApplyMembershipDiscount && discountRate && discountRate > 0) {\n            finalPrice = finalPrice * (1 - discountRate / 100);\n        }\n        // Sonra extra discount uygula\n        if (extraDiscount > 0) {\n            finalPrice = finalPrice * (1 - extraDiscount / 100);\n        }\n        return finalPrice;\n    };\n    // Check if there's any discount applied\n    const hasAnyDiscount = function(extraDiscount) {\n        let applyMembershipDiscount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n        return applyMembershipDiscount && discountRate && discountRate > 0 || extraDiscount > 0;\n    };\n    // Image navigation functions\n    const goToPreviousImage = (e)=>{\n        var _state_productData;\n        if (e) {\n            e.stopPropagation(); // Modal açılmasını engelle\n        }\n        // Explicitly set direction before calling prevImage\n        const { setCurrentImage } = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const state = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const currentVariant = (_state_productData = state.productData) === null || _state_productData === void 0 ? void 0 : _state_productData.data.variants[state.selectedVariantIndex];\n        const maxIndex = (currentVariant === null || currentVariant === void 0 ? void 0 : currentVariant.images.length) || 0;\n        if (maxIndex > 0) {\n            const prevIndex = state.currentImageIndex === 0 ? maxIndex - 1 : state.currentImageIndex - 1;\n            // Set direction first, then update image\n            _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.setState({\n                slideDirection: -1\n            });\n            setTimeout(()=>{\n                setCurrentImage(prevIndex);\n            }, 0);\n        }\n    };\n    const goToNextImage = (e)=>{\n        var _state_productData;\n        if (e) {\n            e.stopPropagation(); // Modal açılmasını engelle\n        }\n        // Explicitly set direction before calling nextImage\n        const { setCurrentImage } = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const state = _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.getState();\n        const currentVariant = (_state_productData = state.productData) === null || _state_productData === void 0 ? void 0 : _state_productData.data.variants[state.selectedVariantIndex];\n        const maxIndex = (currentVariant === null || currentVariant === void 0 ? void 0 : currentVariant.images.length) || 0;\n        if (maxIndex > 0) {\n            const nextIndex = (state.currentImageIndex + 1) % maxIndex;\n            // Set direction first, then update image\n            _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.setState({\n                slideDirection: 1\n            });\n            setTimeout(()=>{\n                setCurrentImage(nextIndex);\n            }, 0);\n        }\n    };\n    // Modal image navigation - senkron olarak ana sayfadaki fotoğrafı da değiştirir\n    const handleModalImageChange = (index)=>{\n        setCurrentImage(index);\n    };\n    const handleAddToCart = async ()=>{\n        if (product && selectedVariant && currentImage) {\n            try {\n                // API'ye sepete ekleme isteği gönder (mutation otomatik olarak cache'i yenileyecek)\n                await addToCartMutation.mutateAsync({\n                    productVariantId: selectedVariant.id,\n                    quantity: quantity,\n                    isCustomerPrice: isCustomerPrice\n                });\n                // Başarılı olduğunda modal için gerekli bilgileri hazırla\n                const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n                const membershipDiscount = !isCustomerPrice && discountRate && discountRate > 0 ? discountRate : 0;\n                const extraDiscount = selectedVariant.extraDiscount || 0;\n                const cartItem = {\n                    id: selectedVariant.id,\n                    title: product.name,\n                    price: selectedVariant.price,\n                    discountedPrice: calculateDiscountedPrice(selectedVariant.price, selectedVariant.extraDiscount, !isCustomerPrice),\n                    thumbnail: currentImage.url,\n                    brand: product.brandName,\n                    membershipDiscount: membershipDiscount,\n                    extraDiscount: extraDiscount,\n                    pvPoints: calculatePoints(selectedVariant.pv, selectedVariant.price),\n                    cvPoints: calculatePoints(selectedVariant.cv, selectedVariant.price),\n                    spPoints: calculatePoints(selectedVariant.sp, selectedVariant.price),\n                    quantity: quantity\n                };\n                // Local sepete de ekle (UI için)\n                addToCart(cartItem);\n                // Modal için son eklenen ürünü ayarla ve modalı göster\n                setLastAddedProduct(cartItem);\n                setShowAddToCartModal(true);\n            } catch (error) {\n                console.error('Sepete ekleme sırasında hata:', error);\n            // Hata durumunda kullanıcıya bilgi verilebilir\n            }\n        }\n    };\n    // Favorileme fonksiyonu\n    const handleFavoriteClick = ()=>{\n        if (!product || !selectedVariant || !currentImage) return;\n        // Toplam indirim oranını hesapla\n        const discountRate = (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) || null;\n        let totalDiscountPercentage = 0;\n        if (discountRate && discountRate > 0) {\n            totalDiscountPercentage += discountRate;\n        }\n        if (selectedVariant.extraDiscount && selectedVariant.extraDiscount > 0) {\n            if (totalDiscountPercentage > 0) {\n                totalDiscountPercentage = totalDiscountPercentage + selectedVariant.extraDiscount - totalDiscountPercentage * selectedVariant.extraDiscount / 100;\n            } else {\n                totalDiscountPercentage = selectedVariant.extraDiscount;\n            }\n        }\n        const favoriteItem = {\n            id: product.id,\n            title: product.name,\n            price: selectedVariant.price,\n            thumbnail: currentImage.url,\n            brand: product.brandName,\n            discountPercentage: totalDiscountPercentage,\n            points: calculatePoints(selectedVariant.pv, selectedVariant.price)\n        };\n        const isCurrentlyFavorite = isFavorite(product.id);\n        if (isCurrentlyFavorite) {\n            removeFromFavorites(product.id);\n            setFavoriteModalIsAdded(false);\n        } else {\n            addToFavorites(favoriteItem);\n            setFavoriteModalIsAdded(true);\n        }\n        setShowFavoriteModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/products\",\n                    className: \"inline-flex items-center text-gray-600 hover:text-purple-600 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, this),\n                        \"\\xdcr\\xfcnlere D\\xf6n\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        className: \"lg:w-1/2\",\n                        initial: {\n                            opacity: 0,\n                            x: -30\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl overflow-hidden shadow-md mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-96 w-full cursor-pointer\",\n                                    onClick: ()=>setShowImageModal(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.AnimatePresence, {\n                                            mode: \"wait\",\n                                            children: currentImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                                initial: slideDirection !== 0 ? {\n                                                    x: slideDirection > 0 ? 300 : -300,\n                                                    opacity: 0\n                                                } : {\n                                                    opacity: 1\n                                                },\n                                                animate: {\n                                                    x: 0,\n                                                    opacity: 1\n                                                },\n                                                exit: slideDirection !== 0 ? {\n                                                    x: slideDirection > 0 ? -300 : 300,\n                                                    opacity: 0\n                                                } : {\n                                                    opacity: 0\n                                                },\n                                                transition: {\n                                                    type: \"tween\",\n                                                    ease: \"easeOut\",\n                                                    duration: slideDirection !== 0 ? 0.2 : 0,\n                                                    delay: slideDirection !== 0 ? 0.1 : 0\n                                                },\n                                                className: \"absolute inset-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: currentImage.url,\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, currentImageIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 29\n                                        }, this),\n                                        variantImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: (e)=>goToPreviousImage(e),\n                                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: (e)=>goToNextImage(e),\n                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        selectedVariant && selectedVariant.extraDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\",\n                                            children: [\n                                                \"%\",\n                                                selectedVariant.extraDiscount,\n                                                \" İndirim\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 33\n                                        }, this),\n                                        !isCustomerPrice && (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) && discountRateData.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute \".concat(selectedVariant && selectedVariant.extraDiscount > 0 ? 'top-12' : 'top-4', \" right-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\"),\n                                            children: [\n                                                \"%\",\n                                                discountRateData.discountRate,\n                                                \" \\xdcye İndirimi\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 33\n                                        }, this),\n                                        selectedVariant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 flex flex-col space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"PV\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: calculatePoints(selectedVariant.pv, selectedVariant.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"CV\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: calculatePoints(selectedVariant.cv, selectedVariant.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"SP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: calculatePoints(selectedVariant.sp, selectedVariant.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-5 gap-2\",\n                                children: variantImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                        className: \"relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 \".concat((currentImage === null || currentImage === void 0 ? void 0 : currentImage.id) === image.id ? \"border-purple-500\" : \"border-gray-200\"),\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        onClick: ()=>{\n                                            // Determine slide direction based on thumbnail position\n                                            const direction = index > currentImageIndex ? 1 : index < currentImageIndex ? -1 : 0;\n                                            _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore.setState({\n                                                slideDirection: direction\n                                            });\n                                            setTimeout(()=>{\n                                                setCurrentImage(index);\n                                            }, 0);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-16 w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: image.url,\n                                                alt: \"\".concat(product.name, \" - \").concat(index + 1),\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, image.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 29\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        className: \"lg:w-1/2\",\n                        initial: {\n                            opacity: 0,\n                            x: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-md p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-800 mb-2\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-2\",\n                                                    children: product.brandName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 29\n                                        }, this),\n                                        product.averageRating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-yellow-50 px-3 py-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 text-yellow-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1 font-medium text-yellow-700\",\n                                                    children: product.averageRating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-100 pb-6 mb-6\",\n                                    children: [\n                                        product.variants && product.variants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-3 text-gray-700\",\n                                                    children: \"Varyant Se\\xe7imi\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 sm:grid-cols-3 gap-3\",\n                                                    children: product.variants.map((variant, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            onClick: ()=>setSelectedVariant(index),\n                                                            className: \"p-3 border-2 rounded-lg text-sm transition-all duration-200 \".concat((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.id) === variant.id ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 hover:border-purple-300'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-left\",\n                                                                children: [\n                                                                    variant.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-600\",\n                                                                            children: [\n                                                                                feature.featureName,\n                                                                                \": \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: feature.featureValue\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 84\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 57\n                                                                        }, this)),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold mt-1\",\n                                                                        children: hasAnyDiscount(variant.extraDiscount, !isCustomerPrice) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.id) === variant.id ? \"text-purple-700\" : \"text-gray-600\",\n                                                                                    children: [\n                                                                                        calculateDiscountedPrice(variant.price, variant.extraDiscount).toFixed(2),\n                                                                                        \" ₺\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 529,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-500 line-through text-xs ml-1\",\n                                                                                    children: [\n                                                                                        variant.price.toFixed(2),\n                                                                                        \" ₺\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.id) === variant.id ? \"text-purple-700\" : \"text-gray-600\",\n                                                                            children: [\n                                                                                variant.price.toFixed(2),\n                                                                                \" ₺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs mt-1 \".concat(getStockStatusText(variant.stockStatus).color),\n                                                                        children: getStockStatusText(variant.stockStatus).text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-1 mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: [\n                                                                                    \"PV \",\n                                                                                    calculatePoints(variant.pv, variant.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 57\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: [\n                                                                                    \"CV \",\n                                                                                    calculatePoints(variant.cv, variant.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 544,\n                                                                                columnNumber: 57\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: [\n                                                                                    \"SP \",\n                                                                                    calculatePoints(variant.sp, variant.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 57\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        }, variant.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 45\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 33\n                                        }, this),\n                                        selectedVariant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-baseline mb-4\",\n                                                    children: hasAnyDiscount(selectedVariant.extraDiscount, !isCustomerPrice) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-purple-700 mr-2\",\n                                                                children: [\n                                                                    calculateDiscountedPrice(selectedVariant.price, selectedVariant.extraDiscount).toFixed(2),\n                                                                    \" ₺\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg text-gray-500 line-through\",\n                                                                children: [\n                                                                    selectedVariant.price.toFixed(2),\n                                                                    \" ₺\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col gap-1 ml-3\",\n                                                                children: [\n                                                                    !isCustomerPrice && (discountRateData === null || discountRateData === void 0 ? void 0 : discountRateData.discountRate) && discountRateData.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                                        children: [\n                                                                            \"%\",\n                                                                            discountRateData.discountRate,\n                                                                            \" \\xdcye İndirimi\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    selectedVariant.extraDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                                        children: [\n                                                                            \"%\",\n                                                                            selectedVariant.extraDiscount,\n                                                                            \" İndirim\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-purple-700 mr-2\",\n                                                        children: [\n                                                            selectedVariant.price.toFixed(2),\n                                                            \" ₺\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Bu \\xfcr\\xfcn\\xfc satın alarak kazanacağınız puanlar:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-5 w-5\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 595,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"PV\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                calculatePoints(selectedVariant.pv, selectedVariant.price),\n                                                                                \" Puan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-5 w-5\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 603,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"CV\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                calculatePoints(selectedVariant.cv, selectedVariant.price),\n                                                                                \" Puan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-5 w-5\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 610,\n                                                                                columnNumber: 53\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"SP\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                calculatePoints(selectedVariant.sp, selectedVariant.price),\n                                                                                \" Puan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm flex items-center \".concat(getStockStatusText(selectedVariant.stockStatus).color),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5 mr-1\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                getStockStatusText(selectedVariant.stockStatus).text\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm flex items-center text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5 mr-1\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"Kategori: \",\n                                                                product.categoryName\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-center gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center border border-gray-200 rounded-md text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: decreaseQuantity,\n                                                        className: \"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 text-center\",\n                                                        children: quantity\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: increaseQuantity,\n                                                        className: \"px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none\",\n                                                        children: \"+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.02\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                onClick: handleAddToCart,\n                                                className: \"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5 mr-2\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \"Sepete Ekle\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                onClick: handleFavoriteClick,\n                                                className: \"w-12 h-12 flex items-center justify-center border rounded-full transition-colors \".concat(isFavorite(product.id) ? 'border-red-500 text-red-500 bg-red-50' : 'border-gray-200 text-gray-400 hover:text-red-500 hover:border-red-500'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-6 w-6\",\n                                                    fill: isFavorite(product.id) ? 'currentColor' : 'none',\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-3 text-gray-700\",\n                                            children: \"\\xdcr\\xfcn A\\xe7ıklaması\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddToCartModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: showAddToCartModal,\n                onClose: ()=>setShowAddToCartModal(false),\n                product: lastAddedProduct,\n                quantity: quantity\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 741,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FavoriteModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showFavoriteModal,\n                onClose: ()=>setShowFavoriteModal(false),\n                product: selectedVariant && currentImage ? {\n                    id: product.id,\n                    title: product.name,\n                    price: selectedVariant.price,\n                    thumbnail: currentImage.url,\n                    brand: product.brandName,\n                    discountPercentage: selectedVariant.extraDiscount,\n                    points: calculatePoints(selectedVariant.pv, selectedVariant.price)\n                } : null,\n                isAdded: favoriteModalIsAdded\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 749,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showImageModal,\n                onClose: ()=>setShowImageModal(false),\n                images: variantImages,\n                currentImageIndex: variantImages.findIndex((img)=>img.id === (currentImage === null || currentImage === void 0 ? void 0 : currentImage.id)),\n                onImageChange: handleModalImageChange,\n                productName: product.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 765,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Sayglobal\\\\sayglobal-frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 309,\n        columnNumber: 9\n    }, this);\n}\n_s(ProductDetailPage, \"T23K+/DdSK8IK6O8thctjWU3bAg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_5__.useCart,\n        _contexts_FavoritesContext__WEBPACK_IMPORTED_MODULE_6__.useFavorites,\n        _hooks_useDiscountRate__WEBPACK_IMPORTED_MODULE_11__.useDiscountRate,\n        _hooks_useCart__WEBPACK_IMPORTED_MODULE_12__.useAddToCart,\n        _stores_customerPriceStore__WEBPACK_IMPORTED_MODULE_14__.useCustomerPriceStore,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductDetailStore,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSelectedVariant,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImage,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useVariantImages,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCatalogProductQuantity,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useSlideDirection,\n        _stores_catalogProductDetailStore__WEBPACK_IMPORTED_MODULE_13__.useCurrentImageIndex,\n        _hooks_useCatalogProducts__WEBPACK_IMPORTED_MODULE_10__.useCatalogProductDetail\n    ];\n});\n_c = ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/product/[id]/page.tsx\n"));

/***/ })

});