'use client';

import React, { useEffect } from 'react';
import { DealershipApplication, DealershipApplicationStatus } from '@/types';
import {
    Clock,
    CheckCircle,
    XCircle,
    User,
    Building2,
    Package,
    MessageSquare,
    ExternalLink
} from 'lucide-react';

interface DealershipApplicationDetailModalProps {
    application: DealershipApplication;
    isOpen: boolean;
    onClose: () => void;
    onApprove: (applicationId: number, notes: string) => void;
    onReject: (applicationId: number, notes: string) => void;
    adminNotes: string;
    setAdminNotes: (notes: string) => void;
    isProcessing: boolean;
    onShowWarning?: () => void;
}

const DealershipApplicationDetailModal: React.FC<DealershipApplicationDetailModalProps> = ({
    application,
    isOpen,
    onClose,
    onApprove,
    onReject,
    adminNotes,
    setAdminNotes,
    isProcessing,
    onShowWarning
}) => {
    // Modal açıldığında body scroll'unu kapat
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        // Cleanup function - component unmount olduğunda scroll'u geri aç
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    if (!isOpen || !application) {
        return null;
    }

    const getStatusBadge = (status: DealershipApplicationStatus) => {
        switch (status) {
            case 'pending':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <Clock className="w-3 h-3 mr-1" />
                        Beklemede
                    </span>
                );
            case 'approved':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Onaylandı
                    </span>
                );
            case 'rejected':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <XCircle className="w-3 h-3 mr-1" />
                        Reddedildi
                    </span>
                );
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleApproveClick = () => {
        onApprove(application.id, adminNotes);
    };

    const handleRejectClick = () => {
        if (!adminNotes.trim()) {
            if (onShowWarning) {
                onShowWarning();
            }
            return;
        }
        onReject(application.id, adminNotes);
    };

    return (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    {/* Modal Header */}
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            Başvuru Detayları
                        </h3>
                        <div className="flex items-center space-x-2">
                            {getStatusBadge(application.status)}
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {/* Application Details */}
                    <div className="space-y-6 max-h-96 overflow-y-auto">
                        {/* Başvuran Bilgileri */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <User className="h-4 w-4 mr-2" />
                                Başvuran Bilgileri
                            </h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-gray-600">Ad Soyad:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.firstName} {application.applicationData.lastName}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">E-posta:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.email}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Telefon:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.phone}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Alternatif Tel:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.alternativeContactNumber}</span>
                                </div>
                            </div>
                        </div>

                        {/* Şirket Bilgileri */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <Building2 className="h-4 w-4 mr-2" />
                                Şirket Bilgileri
                            </h4>
                            <div className="space-y-2 text-sm">
                                <div>
                                    <span className="text-gray-600">Şirket Adı:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.companyName}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Vergi No:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.taxNumber}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Vergi Dairesi:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.taxOffice}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Adres:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.companyAddress}</span>
                                </div>
                            </div>
                        </div>

                        {/* Ürün Bilgileri */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <Package className="h-4 w-4 mr-2" />
                                Ürün Bilgileri
                            </h4>
                            <div className="space-y-2 text-sm">
                                <div>
                                    <span className="text-gray-600">Ana Kategori:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.mainProductCategory}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Tahmini Ürün Sayısı:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.estimatedProductCount}</span>
                                </div>
                                {application.applicationData.sampleProductListUrl && (
                                    <div>
                                        <span className="text-gray-600">Örnek URL:</span>
                                        <a
                                            href={application.applicationData.sampleProductListUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="ml-2 text-blue-600 hover:text-blue-800 flex items-center"
                                        >
                                            Link <ExternalLink className="h-3 w-3 ml-1" />
                                        </a>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Yetkili Kişi */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-3">
                                Yetkili Kişi
                            </h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-gray-600">Ad Soyad:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.authorizedPersonName}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">T.C. No:</span>
                                    <span className="ml-2 font-medium text-gray-700">{application.applicationData.authorizedPersonTcId}</span>
                                </div>
                            </div>
                        </div>

                        {/* Admin Notları */}
                        {application.adminNotes && (
                            <div className="bg-blue-50 p-4 rounded-lg">
                                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                                    <MessageSquare className="h-4 w-4 mr-2" />
                                    Admin Notları
                                </h4>
                                <p className="text-sm text-gray-700">{application.adminNotes}</p>
                                {application.reviewedAt && (
                                    <p className="text-xs text-gray-500 mt-2">
                                        İncelenme: {formatDate(application.reviewedAt)}
                                    </p>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Actions */}
                    {application.status === 'pending' && (
                        <div className="mt-6 space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Admin Notları
                                </label>
                                <textarea
                                    value={adminNotes}
                                    onChange={(e) => setAdminNotes(e.target.value)}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 text-black"
                                    placeholder="Başvuru hakkında notlarınızı yazın..."
                                />
                            </div>

                            <div className="flex justify-end space-x-3">
                                <button
                                    onClick={handleRejectClick}
                                    disabled={isProcessing}
                                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center"
                                >
                                    {isProcessing ? (
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    ) : (
                                        <XCircle className="h-4 w-4 mr-2" />
                                    )}
                                    Reddet
                                </button>
                                <button
                                    onClick={handleApproveClick}
                                    disabled={isProcessing}
                                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center"
                                >
                                    {isProcessing ? (
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    ) : (
                                        <CheckCircle className="h-4 w-4 mr-2" />
                                    )}
                                    Onayla
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default DealershipApplicationDetailModal; 