import { OrderDetailsMap } from '@/types';

// Sipariş geçmişi için mock data
export const mockOrders = [
    {
        id: "ORD-2023-1001",
        date: "25 Mart 2024",
        status: "Teslim Edildi",
        total: "₺1,245.00",
        items: 3,
        statusClass: "bg-green-100 text-green-800"
    },
    {
        id: "ORD-2023-985",
        date: "18 Mart 2024",
        status: "Teslim Edildi",
        total: "₺780.50",
        items: 2,
        statusClass: "bg-green-100 text-green-800"
    },
    {
        id: "ORD-2023-964",
        date: "5 Mart 2024",
        status: "Teslim Edildi",
        total: "₺1,650.00",
        items: 5,
        statusClass: "bg-green-100 text-green-800"
    },
];

// Sipariş detayları için mock data
export const mockOrderDetails: OrderDetailsMap = {
    "ORD-2023-1001": {
        id: "ORD-2023-1001",
        orderDate: "25 Mart 2024",
        status: "Teslim Edildi",
        statusClass: "bg-green-100 text-green-800",
        deliveryDate: "28 Mart 2024",
        trackingNumber: "TRK-2024-001234",
        items: [
            {
                id: 1,
                name: "Premium Collagen Plus",
                brand: "SayGlobal Nutrition",
                image: "/images/collagen-plus.jpg",
                quantity: 2,
                unitPrice: 299.00,
                totalPrice: 598.00,
                points: 60
            },
            {
                id: 2,
                name: "Omega-3 Fish Oil",
                brand: "SayGlobal Nutrition",
                image: "/images/omega3.jpg",
                quantity: 1,
                unitPrice: 199.00,
                totalPrice: 199.00,
                points: 20
            },
            {
                id: 3,
                name: "Vitamin D3 + K2",
                brand: "SayGlobal Nutrition",
                image: "/images/vitamin-d3-k2.jpg",
                quantity: 1,
                unitPrice: 149.00,
                totalPrice: 149.00,
                points: 15
            }
        ],
        pricing: {
            subtotal: 946.00,
            discount: 50.00,
            shippingCost: 35.00,
            tax: 314.00,
            total: 1245.00
        },
        shippingInfo: {
            method: "Hızlı Kargo",
            address: {
                name: "Ahmet Yılmaz",
                phone: "+90 532 123 45 67",
                address: "Atatürk Caddesi No:123 Daire:5",
                district: "Kadıköy",
                city: "İstanbul",
                postalCode: "34710"
            }
        },
        paymentInfo: {
            method: "Kredi Kartı",
            cardLast4: "1234",
            paymentDate: "25 Mart 2024"
        },
        timeline: [
            {
                status: "Sipariş Alındı",
                date: "25 Mart 2024 - 14:30",
                description: "Siparişiniz başarıyla alınmıştır."
            },
            {
                status: "Hazırlanıyor",
                date: "25 Mart 2024 - 16:45",
                description: "Siparişiniz ambalajlanmaya başlandı."
            },
            {
                status: "Kargoya Verildi",
                date: "26 Mart 2024 - 09:15",
                description: "Siparişiniz kargo firmasına teslim edildi."
            },
            {
                status: "Teslim Edildi",
                date: "28 Mart 2024 - 11:20",
                description: "Siparişiniz adresinize teslim edilmiştir."
            }
        ]
    },
    "ORD-2023-985": {
        id: "ORD-2023-985",
        orderDate: "18 Mart 2024",
        status: "Teslim Edildi",
        statusClass: "bg-green-100 text-green-800",
        deliveryDate: "21 Mart 2024",
        trackingNumber: "TRK-2024-001235",
        items: [
            {
                id: 4,
                name: "Hyaluronic Acid Serum",
                brand: "SayGlobal Beauty",
                image: "/images/hyaluronic-serum.jpg",
                quantity: 1,
                unitPrice: 399.00,
                totalPrice: 399.00,
                points: 40
            },
            {
                id: 5,
                name: "Probiotics Advanced",
                brand: "SayGlobal Nutrition",
                image: "/images/probiotics.jpg",
                quantity: 1,
                unitPrice: 249.00,
                totalPrice: 249.00,
                points: 25
            }
        ],
        pricing: {
            subtotal: 648.00,
            discount: 25.00,
            shippingCost: 35.00,
            tax: 122.50,
            total: 780.50
        },
        shippingInfo: {
            method: "Standart Kargo",
            address: {
                name: "Ahmet Yılmaz",
                phone: "+90 532 123 45 67",
                address: "Atatürk Caddesi No:123 Daire:5",
                district: "Kadıköy",
                city: "İstanbul",
                postalCode: "34710"
            }
        },
        paymentInfo: {
            method: "Kredi Kartı",
            cardLast4: "1234",
            paymentDate: "18 Mart 2024"
        },
        timeline: [
            {
                status: "Sipariş Alındı",
                date: "18 Mart 2024 - 10:15",
                description: "Siparişiniz başarıyla alınmıştır."
            },
            {
                status: "Hazırlanıyor",
                date: "18 Mart 2024 - 15:30",
                description: "Siparişiniz ambalajlanmaya başlandı."
            },
            {
                status: "Kargoya Verildi",
                date: "19 Mart 2024 - 08:45",
                description: "Siparişiniz kargo firmasına teslim edildi."
            },
            {
                status: "Teslim Edildi",
                date: "21 Mart 2024 - 14:10",
                description: "Siparişiniz adresinize teslim edilmiştir."
            }
        ]
    },
    "ORD-2023-964": {
        id: "ORD-2023-964",
        orderDate: "5 Mart 2024",
        status: "Teslim Edildi",
        statusClass: "bg-green-100 text-green-800",
        deliveryDate: "8 Mart 2024",
        trackingNumber: "TRK-2024-001236",
        items: [
            {
                id: 6,
                name: "Magnesium Complex",
                brand: "SayGlobal Nutrition",
                image: "/images/magnesium.jpg",
                quantity: 2,
                unitPrice: 179.00,
                totalPrice: 358.00,
                points: 18
            },
            {
                id: 7,
                name: "Zinc + Selenium",
                brand: "SayGlobal Nutrition",
                image: "/images/zinc-selenium.jpg",
                quantity: 1,
                unitPrice: 129.00,
                totalPrice: 129.00,
                points: 13
            },
            {
                id: 8,
                name: "Vitamin C 1000mg",
                brand: "SayGlobal Nutrition",
                image: "/images/vitamin-c.jpg",
                quantity: 2,
                unitPrice: 99.00,
                totalPrice: 198.00,
                points: 10
            },
            {
                id: 9,
                name: "B-Complex Vitamins",
                brand: "SayGlobal Nutrition",
                image: "/images/b-complex.jpg",
                quantity: 1,
                unitPrice: 159.00,
                totalPrice: 159.00,
                points: 16
            },
            {
                id: 10,
                name: "Iron Supplement",
                brand: "SayGlobal Nutrition",
                image: "/images/iron.jpg",
                quantity: 1,
                unitPrice: 119.00,
                totalPrice: 119.00,
                points: 12
            }
        ],
        pricing: {
            subtotal: 963.00,
            discount: 75.00,
            shippingCost: 35.00,
            tax: 727.00,
            total: 1650.00
        },
        shippingInfo: {
            method: "Express Kargo",
            address: {
                name: "Ahmet Yılmaz",
                phone: "+90 532 123 45 67",
                address: "Atatürk Caddesi No:123 Daire:5",
                district: "Kadıköy",
                city: "İstanbul",
                postalCode: "34710"
            }
        },
        paymentInfo: {
            method: "Havale/EFT",
            paymentDate: "5 Mart 2024"
        },
        timeline: [
            {
                status: "Sipariş Alındı",
                date: "5 Mart 2024 - 12:00",
                description: "Siparişiniz başarıyla alınmıştır."
            },
            {
                status: "Ödeme Onaylandı",
                date: "5 Mart 2024 - 16:30",
                description: "Ödemeniz onaylanmıştır."
            },
            {
                status: "Hazırlanıyor",
                date: "6 Mart 2024 - 09:00",
                description: "Siparişiniz ambalajlanmaya başlandı."
            },
            {
                status: "Kargoya Verildi",
                date: "6 Mart 2024 - 17:15",
                description: "Siparişiniz kargo firmasına teslim edildi."
            },
            {
                status: "Teslim Edildi",
                date: "8 Mart 2024 - 10:45",
                description: "Siparişiniz adresinize teslim edilmiştir."
            }
        ]
    }
};

// Kullanıcı bakiye ve puanları için mock data
export const mockUserBalance = {
    accountBalance: 125.50, // Hesap bakiyesi (TL)
    totalPoints: 2450, // Toplam puanlar
    availablePoints: 1850, // Kullanılabilir puanlar
    usedPoints: 600, // Kullanılan puanlar
    // Haftalık puan kazanımları (son 8 hafta)
    weeklyEarnings: [
        { week: "28 Eki", points: 85 },
        { week: "4 Kas", points: 120 },
        { week: "11 Kas", points: 95 },
        { week: "18 Kas", points: 150 },
        { week: "25 Kas", points: 110 },
        { week: "2 Ara", points: 175 },
        { week: "9 Ara", points: 130 },
        { week: "16 Ara", points: 165 },
    ],
    // Aylık puan kazanımları (son 6 ay)
    monthlyEarnings: [
        { month: "Tem", points: 320 },
        { month: "Ağu", points: 450 },
        { month: "Eyl", points: 380 },
        { month: "Eki", points: 520 },
        { month: "Kas", points: 475 },
        { month: "Ara", points: 470 },
    ],
    pointHistory: [
        {
            id: 1,
            date: "2024-06-15",
            type: "earned" as const, // earned, used
            points: 125,
            description: "Premium Collagen Plus alışverişi",
            orderNo: "ORD-2024-1001"
        },
        {
            id: 2,
            date: "2024-06-10",
            type: "used" as const,
            points: -100,
            description: "Omega-3 Fish Oil indirimi",
            orderNo: "ORD-2024-0998"
        },
        {
            id: 3,
            date: "2024-06-08",
            type: "earned" as const,
            points: 90,
            description: "Vitamin D3 + K2 alışverişi",
            orderNo: "ORD-2024-0995"
        },
        {
            id: 4,
            date: "2024-06-05",
            type: "earned" as const,
            points: 150,
            description: "Hyaluronic Acid Serum alışverişi",
            orderNo: "ORD-2024-0992"
        },
        {
            id: 5,
            date: "2024-05-28",
            type: "used" as const,
            points: -200,
            description: "Probiotics Advanced indirimi",
            orderNo: "ORD-2024-0989"
        }
    ],
    balanceHistory: [
        {
            id: 1,
            date: "2024-06-12",
            type: "refund" as const, // refund, gift_card, withdrawal
            amount: 45.50,
            description: "ORD-2024-0990 iadesi",
            status: "completed"
        },
        {
            id: 2,
            date: "2024-05-20",
            type: "gift_card" as const,
            amount: 100.00,
            description: "Hediye kartı yüklemesi",
            status: "completed"
        },
        {
            id: 3,
            date: "2024-05-15",
            type: "withdrawal" as const,
            amount: -20.00,
            description: "Kargo ücreti ödemesi",
            status: "completed"
        }
    ]
};

// Banka bilgileri için mock data
export const mockBankingInfo = {
    iban: {
        accountHolderName: 'Ahmet Mehmet YILMAZ',
        bankName: 'garanti',
        bankDisplayName: 'Garanti BBVA',
        iban: 'TR64 0006 2000 4440 0006 2986 57',
        branchName: 'Kadıköy Şubesi',
        branchCode: '444'
    },
    cards: [
        {
            id: 1,
            cardHolderName: 'AHMET YILMAZ',
            cardType: 'visa',
            cardNumber: '**** **** **** 1234',
            expirationDate: '12/26',
            isDefault: true,
            addedDate: '2023-03-15',
            color: 'blue' // blue, green, purple, orange
        },
        {
            id: 2,
            cardHolderName: 'AHMET YILMAZ',
            cardType: 'mastercard',
            cardNumber: '**** **** **** 5678',
            expirationDate: '09/25',
            isDefault: false,
            addedDate: '2023-08-22',
            color: 'green'
        },
        {
            id: 3,
            cardHolderName: 'AHMET YILMAZ',
            cardType: 'troy',
            cardNumber: '**** **** **** 9012',
            expirationDate: '03/27',
            isDefault: false,
            addedDate: '2024-01-10',
            color: 'purple'
        }
    ],
    statistics: {
        monthlyEarnings: 2450,
        totalTransactions: 24,
        averageTransaction: 102
    }
};