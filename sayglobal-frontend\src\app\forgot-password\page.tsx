'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useState } from "react";

export default function ForgotPasswordPage() {
    const [email, setEmail] = useState("");
    const [submitted, setSubmitted] = useState(false);
    const [error, setError] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!email) {
            setError("Lütfen e-posta adresinizi girin");
            return;
        }

        if (!/\S+@\S+\.\S+/.test(email)) {
            setError("Geçerli bir e-posta adresi giriniz");
            return;
        }

        setError("");
        setIsLoading(true);

        // Simüle edilmiş API çağrısı
        try {
            // Burada gerçek bir API çağrısı yapılacak
            await new Promise(resolve => setTimeout(resolve, 1500)); // Simüle edilmiş gecikme
            setSubmitted(true);
        } catch (err) {
            setError("Bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="container mx-auto px-4 py-16">
            <div className="max-w-md mx-auto">
                <motion.div
                    className="bg-white rounded-2xl shadow-lg overflow-hidden"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="p-8">
                        {!submitted ? (
                            <>
                                <div className="text-center mb-8">
                                    <h1 className="text-3xl font-bold mb-2 text-gray-800">Şifrenizi mi Unuttunuz?</h1>
                                    <p className="text-gray-700">
                                        Endişelenmeyin, size şifrenizi sıfırlamanız için bir bağlantı göndereceğiz.
                                    </p>
                                </div>

                                <form onSubmit={handleSubmit}>
                                    <div className="space-y-5">
                                        <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                                E-posta Adresi
                                            </label>
                                            <input
                                                id="email"
                                                type="email"
                                                value={email}
                                                onChange={(e) => {
                                                    setEmail(e.target.value);
                                                    if (error) setError("");
                                                }}
                                                className={`w-full px-4 py-3 rounded-lg border ${error ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500`}
                                                placeholder="<EMAIL>"
                                            />
                                            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
                                        </div>

                                        <motion.button
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                            type="submit"
                                            disabled={isLoading}
                                            className={`w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                                        >
                                            {isLoading ? (
                                                <span className="flex items-center justify-center">
                                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    İşleniyor...
                                                </span>
                                            ) : (
                                                "Şifre Sıfırlama Bağlantısı Gönder"
                                            )}
                                        </motion.button>
                                    </div>
                                </form>
                            </>
                        ) : (
                            <motion.div
                                className="text-center py-6"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 0.5 }}
                            >
                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <h2 className="text-2xl font-bold text-gray-800 mb-2">E-posta Gönderildi!</h2>
                                <p className="text-gray-700 mb-6">
                                    <span className="font-medium">{email}</span> adresine şifre sıfırlama bağlantısı gönderdik. Lütfen gelen kutunuzu kontrol edin.
                                </p>
                                <p className="text-sm text-gray-600 mb-4">
                                    E-postayı bulamıyor musunuz? Spam klasörünüzü kontrol edin veya tekrar deneyin.
                                </p>
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={() => setSubmitted(false)}
                                    className="text-purple-600 font-medium hover:text-purple-800 transition"
                                >
                                    Farklı bir e-posta dene
                                </motion.button>
                            </motion.div>
                        )}

                        <div className="mt-8 text-center">
                            <p className="text-gray-700">
                                <Link
                                    href="/login"
                                    className="text-purple-600 font-medium hover:text-purple-800 transition"
                                >
                                    Giriş sayfasına dön
                                </Link>
                            </p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
} 