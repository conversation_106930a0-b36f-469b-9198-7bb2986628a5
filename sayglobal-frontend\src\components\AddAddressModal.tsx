'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { CreateAddressRequest } from '@/types';
import { useAuth } from '@/components/auth/AuthContext';
import { useAuthStore } from '@/stores/authStore';

// 🚀 NEW: TanStack Query hooks
import { useCreateAddress, useAddresses } from '@/hooks/useAddresses';

// 🚀 Modal store imports
import {
    useAddAddressModal,
    useAddAddressData,
    useModalActions
} from '@/stores/modalStore';

interface AddAddressModalProps {
    // 🔄 BACKWARD COMPATIBILITY: Eski props hala destekleniyor
    isOpen?: boolean;
    onClose?: () => void;
    onAddressAdded?: () => void; // Callback for when address is successfully added
}

export default function AddAddressModal({ isOpen, onClose, onAddressAdded }: AddAddressModalProps) {
    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al - modal'da da aynı sorun vardı
    const authStore = useAuthStore();
    const { user: contextUser } = useAuth();



    // 🔄 Fallback: Auth store'dan user yoksa context'den al
    const user = authStore.user || contextUser;

    // 🚀 Modal store hooks
    const isModalOpen = useAddAddressModal();
    const modalData = useAddAddressData();
    const { closeAddAddressModal } = useModalActions();

    // 🏠 Mevcut adresleri al (ilk adres kontrolü için)
    const { data: addresses = [] } = useAddresses();
    const isFirstAddress = addresses.length === 0;

    // 🎯 HYBRID APPROACH: Store'u önceliklendire, props'u fallback olarak kullan
    const modalOpen = isModalOpen || isOpen || false;
    const handleClose = () => {
        // Store'dan açıldıysa store'u kapat
        if (isModalOpen) {
            closeAddAddressModal();
        }
        // Prop'dan açıldıysa callback'i çağır
        if (onClose) {
            onClose();
        }
    };

    // 🎯 Form state
    const [formData, setFormData] = useState<CreateAddressRequest>(() => ({
        title: '',
        fullAddress: '',
        city: '',
        district: '',
        postalCode: '',
        isDefault: false // Bu useEffect'te güncellenir
    }));

    // 🔄 Store data varsa form'u doldur
    useEffect(() => {
        if (modalData) {
            setFormData(modalData);
        } else if (modalOpen) {
            // Modal açıldığında form'u temizle ve ilk adres kontrolü yap
            setFormData({
                title: '',
                fullAddress: '',
                city: '',
                district: '',
                postalCode: '',
                isDefault: isFirstAddress // 🏠 İlk adres ise otomatik varsayılan
            });

            if (isFirstAddress) {
                console.log('🏠 Modal: İlk adres - checkbox otomatik checked');
            }
        }
    }, [modalData, modalOpen, isFirstAddress]);

    // 🚀 NEW: TanStack Query mutation hook
    const createAddressMutation = useCreateAddress();

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        console.log('🏠 Modal: Adres ekleme başlıyor...', {
            hasUser: !!user,
            userId: user?.id,
            isAuthenticated: authStore.isAuthenticated,
            formData
        });

        if (!user) {
            console.error('❌ Modal: User bulunamadı, adres ekleme iptal edildi', {
                authStoreUser: authStore.user,
                contextUser,
                isAuthenticated: authStore.isAuthenticated,
                authUserExists: !!authStore.user,
                contextUserExists: !!contextUser
            });
            return;
        }

        // 🚀 NEW: Use TanStack Query mutation
        createAddressMutation.mutate(formData, {
            onSuccess: (data) => {
                console.log('✅ Modal: Address created successfully', data);

                // Form'u temizle
                setFormData({
                    title: '',
                    fullAddress: '',
                    city: '',
                    district: '',
                    postalCode: '',
                    isDefault: false
                });

                // Callback'i çağır ve modal'ı kapat
                if (onAddressAdded) {
                    onAddressAdded();
                }
                handleClose();
            },
            onError: (error) => {
                console.error('❌ Modal: Address creation failed:', error);
                console.error('❌ Modal: Error name:', error.name);
                console.error('❌ Modal: Error message:', error.message);
            }
        });
    };

    const cities = [
        'İstanbul', 'Ankara', 'İzmir', 'Antalya', 'Bursa', 'Adana', 'Konya', 'Gaziantep', 'Mersin', 'Diyarbakır'
    ];

    // Loading state from mutation
    const isLoading = createAddressMutation.isPending;
    const error = createAddressMutation.error?.message;

    return (
        <AnimatePresence>
            {modalOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={handleClose}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between mb-6">
                            <h2 className="text-2xl font-bold text-gray-800">
                                Yeni Adres Ekle
                            </h2>
                            <motion.button
                                onClick={handleClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                disabled={isLoading}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </motion.button>
                        </div>

                        {/* Error Message */}
                        {error && (
                            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                                {error}
                            </div>
                        )}

                        {/* Form */}
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                                    Adres Başlığı *
                                </label>
                                <input
                                    type="text"
                                    id="title"
                                    name="title"
                                    value={formData.title}
                                    onChange={handleInputChange}
                                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                    placeholder="Ev, İş, vb."
                                    required
                                    disabled={isLoading}
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                                        Şehir *
                                    </label>
                                    <select
                                        id="city"
                                        name="city"
                                        value={formData.city}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        required
                                        disabled={isLoading}
                                    >
                                        <option value="">Şehir seçin</option>
                                        {cities.map(city => (
                                            <option key={city} value={city}>{city}</option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label htmlFor="district" className="block text-sm font-medium text-gray-700 mb-1">
                                        İlçe *
                                    </label>
                                    <input
                                        type="text"
                                        id="district"
                                        name="district"
                                        value={formData.district}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                        placeholder="İlçe"
                                        required
                                        disabled={isLoading}
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="fullAddress" className="block text-sm font-medium text-gray-700 mb-1">
                                    Adres *
                                </label>
                                <textarea
                                    id="fullAddress"
                                    name="fullAddress"
                                    value={formData.fullAddress}
                                    onChange={handleInputChange}
                                    rows={3}
                                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black resize-none"
                                    placeholder="Detaylı adres bilgisi"
                                    required
                                    disabled={isLoading}
                                />
                            </div>

                            <div>
                                <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 mb-1">
                                    Posta Kodu (Opsiyonel)
                                </label>
                                <input
                                    type="text"
                                    id="postalCode"
                                    name="postalCode"
                                    value={formData.postalCode}
                                    onChange={handleInputChange}
                                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                    placeholder="Posta kodu (isteğe bağlı)"
                                    disabled={isLoading}
                                />
                            </div>

                            <div className="flex flex-col">
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="isDefault"
                                        name="isDefault"
                                        checked={formData.isDefault}
                                        onChange={handleInputChange}
                                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                        disabled={isLoading || isFirstAddress} // 🏠 İlk adres ise disabled
                                    />
                                    <label
                                        htmlFor="isDefault"
                                        className={`ml-2 block text-sm ${isFirstAddress ? 'text-gray-500' : 'text-gray-700'}`}
                                    >
                                        Bu adresi varsayılan adres olarak ayarla
                                    </label>
                                </div>
                                {isFirstAddress && (
                                    <p className="text-xs text-purple-600 mt-1 ml-6">
                                        İlk adres otomatik olarak varsayılan adres olur
                                    </p>
                                )}
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col space-y-3 mt-6">
                                <motion.button
                                    type="submit"
                                    className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${isLoading
                                        ? 'bg-gray-400 cursor-not-allowed'
                                        : 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:shadow-lg'
                                        }`}
                                    whileHover={!isLoading ? { scale: 1.02 } : {}}
                                    whileTap={!isLoading ? { scale: 0.98 } : {}}
                                    disabled={isLoading}
                                >
                                    {isLoading ? 'Kaydediliyor...' : 'Adresi Kaydet'}
                                </motion.button>

                                <motion.button
                                    type="button"
                                    onClick={handleClose}
                                    className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    disabled={isLoading}
                                >
                                    İptal
                                </motion.button>
                            </div>
                        </form>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 