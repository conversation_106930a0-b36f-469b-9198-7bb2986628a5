import { DealershipApplication, DealershipApplicationStatus } from '@/types';

// Mock satıcı başvuruları
export let mockDealershipApplications: DealershipApplication[] = [
    {
        id: 1,
        userId: 6,
        userName: 'Mehm<PERSON>',
        userEmail: '<EMAIL>',
        applicationData: {
            firstName: 'Mehmet',
            lastName: '<PERSON>ılma<PERSON>',
            email: '<EMAIL>',
            phone: '+90 ************',
            mainProductCategory: 'Elektronik',
            subProductCategories: ['Telefon', 'Bilgisayar', 'Aksesuar'],
            estimatedProductCount: '50-100 adet',
            sampleProductListUrl: 'https://example.com/products',
            companyName: 'Yılmaz Elektronik Ltd. Şti.',
            taxNumber: '1234567890',
            taxOffice: 'İstanbul Vergi Dairesi',
            companyAddress: 'Atatürk Cad. No:123 Kadıköy/İstanbul',
            authorizedPersonName: '<PERSON><PERSON><PERSON>',
            authorizedPersonTcId: '12345678901',
            alternativeContactNumber: '+90 ************',
            userAgreementAccepted: true,
            dealershipAgreementAccepted: true,
            privacyPolicyAccepted: true
        },
        status: 'pending',
        submittedAt: '2024-01-15T10:30:00Z'
    },
    {
        id: 2,
        userId: 7,
        userName: 'Ayşe Kaya',
        userEmail: '<EMAIL>',
        applicationData: {
            firstName: 'Ayşe',
            lastName: 'Kaya',
            email: '<EMAIL>',
            phone: '+90 ************',
            mainProductCategory: 'Ev & Yaşam',
            subProductCategories: ['Mutfak', 'Dekorasyon'],
            estimatedProductCount: '20-50 adet',
            companyName: 'Kaya Ev Tekstili',
            taxNumber: '0987654321',
            taxOffice: 'Ankara Vergi Dairesi',
            companyAddress: 'Kızılay Cad. No:456 Çankaya/Ankara',
            authorizedPersonName: 'Ayşe Kaya',
            authorizedPersonTcId: '10987654321',
            alternativeContactNumber: '+90 ************',
            userAgreementAccepted: true,
            dealershipAgreementAccepted: true,
            privacyPolicyAccepted: true
        },
        status: 'approved',
        submittedAt: '2024-01-10T14:20:00Z',
        reviewedAt: '2024-01-12T09:15:00Z',
        reviewedBy: 1,
        adminNotes: 'Başvuru uygun bulunmuştur.'
    }
];

// Ürün kategorileri
export const productCategories = [
    'Elektronik',
    'Ev & Yaşam',
    'Giyim & Aksesuar',
    'Spor & Outdoor',
    'Kozmetik & Kişisel Bakım',
    'Kitap & Kırtasiye',
    'Oyuncak & Hobi',
    'Otomotiv',
    'Anne & Bebek',
    'Diğer'
];

// Tahmini ürün sayısı seçenekleri
export const estimatedProductCountOptions = [
    '1-10 adet',
    '11-25 adet',
    '26-50 adet',
    '51-100 adet',
    '101-250 adet',
    '250+ adet'
]; 