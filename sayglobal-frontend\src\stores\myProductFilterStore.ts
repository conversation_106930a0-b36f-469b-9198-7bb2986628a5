import { create } from 'zustand';

interface MyProductFilterState {
    searchTerm: string;
    statusFilter: number; // -1: <PERSON><PERSON><PERSON><PERSON>, 0: <PERSON><PERSON><PERSON><PERSON>, 1: <PERSON>ayland<PERSON>, 2: Reddedildi
    setSearchTerm: (term: string) => void;
    setStatusFilter: (status: number) => void;
    resetFilters: () => void;
}

export const useMyProductFilterStore = create<MyProductFilterState>((set) => ({
    searchTerm: '',
    statusFilter: -1, // Varsayılan olarak tüm durumlar
    setSearchTerm: (term) => set({ searchTerm: term }),
    setStatusFilter: (status) => set({ statusFilter: status }),
    resetFilters: () => set({ searchTerm: '', statusFilter: -1 }),
}));
