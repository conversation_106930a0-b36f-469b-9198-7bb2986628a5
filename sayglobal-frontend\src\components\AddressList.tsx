'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Address } from '@/types';
import { useAuth } from '@/components/auth/AuthContext';

// 🚀 NEW: TanStack Query hooks
import { useDeleteAddress, useSetDefaultAddress } from '@/hooks/useAddresses';

// 🚀 Modal store imports
import { useModalActions, useSetDefaultConfirmationData } from '@/stores/modalStore';

// 🚀 Modal components
import SetDefaultConfirmationModal from './SetDefaultConfirmationModal';

interface AddressListProps {
    addresses: Address[];
    onAddressDeleted?: () => void; // Optional callback
}

export default function AddressList({ addresses, onAddressDeleted }: AddressListProps) {
    const { user } = useAuth();

    // 🚀 Modal store actions and data
    const { openSetDefaultConfirmationModal } = useModalActions();
    const setDefaultModalData = useSetDefaultConfirmationData();

    // 🚀 TanStack Query mutation hooks
    const setDefaultAddressMutation = useSetDefaultAddress();

    // 🚨 Delete mutation with callback for default address handling
    const deleteAddressMutation = useDeleteAddress((nextDefaultAddress, deletedAddress) => {
        // Varsayılan adres silindiğinde confirmation modal'ı aç
        openSetDefaultConfirmationModal({
            targetAddress: {
                id: nextDefaultAddress.id!,
                title: nextDefaultAddress.title,
                fullAddress: nextDefaultAddress.fullAddress,
                city: nextDefaultAddress.city,
                district: nextDefaultAddress.district
            },
            deletedDefaultAddress: {
                id: deletedAddress?.id || 0,
                title: deletedAddress?.title || 'Silinmiş Adres'
            }
        });
    });

    // Ensure addresses is always an array and filter out invalid entries
    const safeAddresses = Array.isArray(addresses) ?
        addresses.filter(address =>
            address &&
            address.id !== undefined &&
            address.title &&
            address.fullAddress &&
            address.city &&
            address.district
        ) : [];



    const handleDeleteAddress = async (addressId: number) => {
        if (!user || !addressId) {
            return;
        }

        // 🔍 Silinen adresin bilgisini al
        const addressToDelete = safeAddresses.find(addr => addr.id === addressId);

        // 🚀 NEW: Use TanStack Query mutation
        deleteAddressMutation.mutate(addressId, {
            onSuccess: () => {
                console.log('✅ Address deleted successfully');

                // Optional callback
                if (onAddressDeleted) {
                    onAddressDeleted();
                }
            },
            onError: (error) => {
                console.error('❌ Address deletion failed:', error);
            }
        });
    };

    // 🏠 Varsayılan adres yapma handler
    const handleSetDefault = (address: Address) => {
        if (!user || !address.id || address.isDefault) {
            return;
        }

        // Confirmation modal'ı aç
        openSetDefaultConfirmationModal({
            targetAddress: {
                id: address.id,
                title: address.title,
                fullAddress: address.fullAddress,
                city: address.city,
                district: address.district
            }
        });
    };

    // 🏠 Modal confirmation handler - SetDefaultConfirmationModal'dan çağrılacak
    const handleConfirmSetDefault = (targetAddress: Address) => {
        if (!user || !targetAddress.id) {
            return;
        }

        // 🚀 Set default address mutation
        setDefaultAddressMutation.mutate(targetAddress.id, {
            onSuccess: () => {
                console.log('✅ Default address set successfully');

                // Optional callback
                if (onAddressDeleted) {
                    onAddressDeleted(); // Refresh için aynı callback'i kullanabiliriz
                }
            },
            onError: (error) => {
                console.error('❌ Set default address failed:', error);
            }
        });
    };

    if (safeAddresses.length === 0) {
        return (
            <div className="text-center py-10 bg-gray-50 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz kayıtlı adresiniz bulunmuyor</h3>
                <p className="text-gray-500 mb-6">Siparişlerinizin teslimatı için adres bilgilerinizi ekleyebilirsiniz.</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Error handling from mutation */}
            {deleteAddressMutation.error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                    {deleteAddressMutation.error.message || 'Adres silinirken bir hata oluştu'}
                </div>
            )}

            <div className="grid gap-4 md:grid-cols-2">
                <AnimatePresence mode="popLayout">
                    {safeAddresses.map((address, index) => (
                        <motion.div
                            key={address.id || `address-${index}`}
                            layoutId={`address-${address.id || index}`}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{
                                type: "spring",
                                stiffness: 500,
                                damping: 30,
                                duration: 0.3
                            }}
                            layout
                            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow duration-300"
                        >
                            <div className="flex justify-between items-start mb-3">
                                <div className="flex items-center space-x-2">
                                    <h3 className="font-semibold text-gray-900">{address.title}</h3>
                                    {address.isDefault && (
                                        <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded">
                                            Varsayılan
                                        </span>
                                    )}
                                </div>

                                <div className="flex items-center space-x-2">
                                    {/* 🏠 Varsayılan Yap Button - Sadece varsayılan olmayan adresler için */}
                                    {!address.isDefault && (
                                        <motion.button
                                            onClick={() => handleSetDefault(address)}
                                            disabled={setDefaultAddressMutation.isPending}
                                            className={`px-3 py-1 text-xs font-medium rounded transition-colors ${setDefaultAddressMutation.isPending
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-purple-50 text-purple-600 hover:bg-purple-100 hover:text-purple-700'
                                                }`}
                                            whileHover={{ scale: setDefaultAddressMutation.isPending ? 1 : 1.05 }}
                                            whileTap={{ scale: setDefaultAddressMutation.isPending ? 1 : 0.95 }}
                                            title="Bu adresi varsayılan yap"
                                        >
                                            {setDefaultAddressMutation.isPending ? (
                                                <div className="flex items-center space-x-1">
                                                    <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                    </svg>
                                                    <span>Ayarlanıyor...</span>
                                                </div>
                                            ) : (
                                                <div className="flex items-center space-x-1">
                                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                    </svg>
                                                    <span>Varsayılan Yap</span>
                                                </div>
                                            )}
                                        </motion.button>
                                    )}

                                    {/* 🗑️ Delete Button */}
                                    <motion.button
                                        onClick={() => handleDeleteAddress(address.id!)}
                                        disabled={deleteAddressMutation.isPending}
                                        className={`p-2 rounded-lg transition-colors ${deleteAddressMutation.isPending
                                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                            : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                                            }`}
                                        whileHover={{ scale: deleteAddressMutation.isPending ? 1 : 1.1 }}
                                        whileTap={{ scale: deleteAddressMutation.isPending ? 1 : 0.95 }}
                                        title="Bu adresi sil"
                                    >
                                        {deleteAddressMutation.isPending ? (
                                            <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                        ) : (
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        )}
                                    </motion.button>
                                </div>
                            </div>

                            <div className="text-sm text-gray-600 space-y-1">
                                <p className="flex items-start">
                                    <svg className="w-4 h-4 mt-0.5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span>{address.fullAddress}</span>
                                </p>
                                <p className="flex items-center">
                                    <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span>{address.district}, {address.city}</span>
                                </p>
                                {address.postalCode && (
                                    <p className="flex items-center">
                                        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 7.89a2 2 0 002.83 0L21 9M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                        <span>Posta Kodu: {address.postalCode}</span>
                                    </p>
                                )}
                            </div>
                        </motion.div>
                    ))}
                </AnimatePresence>
            </div>

            {/* 🏠 Set Default Confirmation Modal */}
            <SetDefaultConfirmationModal
                onConfirm={() => {
                    // Modal store'dan data alıp mutation çağır
                    if (setDefaultModalData?.targetAddress) {
                        handleConfirmSetDefault({
                            id: setDefaultModalData.targetAddress.id,
                            title: setDefaultModalData.targetAddress.title,
                            fullAddress: setDefaultModalData.targetAddress.fullAddress,
                            city: setDefaultModalData.targetAddress.city,
                            district: setDefaultModalData.targetAddress.district,
                            postalCode: '', // Optional field
                            isDefault: false // Will be set to true after mutation
                        });
                    }
                }}
            />
        </div>
    );
} 