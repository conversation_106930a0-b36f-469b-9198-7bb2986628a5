'use client';

import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';
import "./globals.css";
import Header from "@/components/ui/Header";
import Footer from "@/components/ui/Footer";
import { AuthProvider } from "@/components/auth/AuthContext";
import { CartProvider } from "@/contexts/CartContext";
import { FavoritesProvider } from "@/contexts/FavoritesContext";
import { AnimatePresence, motion } from "framer-motion";
import { usePathname } from "next/navigation";
import { NetworkStatusHandler } from '@/components/NetworkStatusHandler';
import RegisterSuccessModal from '@/components/RegisterSuccessModal';

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();

  // 🚀 TanStack Query client - stable instance
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 dakika - data ne kadar süre fresh kalacak
        gcTime: 10 * 60 * 1000, // 10 dakika - memory'de ne kadar kalacak (cacheTime deprecated)
        retry: 3, // Hata durumunda 3 kez dene
        refetchOnWindowFocus: false, // Pencere focus olduğunda refetch yapma
      },
      mutations: {
        retry: 1, // Mutation hatasında 1 kez dene
      }
    }
  }));

  return (
    <html lang="tr" className="scroll-smooth">
      <body className={`${inter.className} bg-gray-50 min-h-screen flex flex-col`}>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <NetworkStatusHandler />
            <CartProvider>
              <FavoritesProvider>
                <Header />
                <AnimatePresence mode="wait">
                  <motion.main
                    key={pathname}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                    className="flex-grow"
                  >
                    {children}
                  </motion.main>
                </AnimatePresence>
                <Footer />

                {/* Global Modals */}
                <RegisterSuccessModal />
              </FavoritesProvider>
            </CartProvider>
          </AuthProvider>

          {/* 🚀 Development için ReactQuery DevTools */}
          {process.env.NODE_ENV === 'development' && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </QueryClientProvider>
      </body>
    </html>
  );
}
