'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, User, Calendar, MapPin, ArrowLeft, ArrowRight } from 'lucide-react';

// <PERSON><PERSON><PERSON><PERSON> bekleyen kişi tipi
interface PendingMember {
    id: number;
    name: string;
    email: string;
    phone: string;
    joinDate: string;
    sponsorId: number;
    sponsorName: string;
}

interface PendingPlacementModalProps {
    isOpen: boolean;
    onClose: () => void;
    pendingMembers: PendingMember[];
    onPlaceLeft: (memberId: number) => void;
    onPlaceRight: (memberId: number) => void;
}

const PendingPlacementModal: React.FC<PendingPlacementModalProps> = ({
    isOpen,
    onClose,
    pendingMembers,
    onPlaceLeft,
    onPlaceRight
}) => {
    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-white/20 backdrop-blur-lg flex items-center justify-center z-50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={onClose}
                >
                    <motion.div
                        className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
                        initial={{ scale: 0.9, opacity: 0, y: 20 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.9, opacity: 0, y: 20 }}
                        transition={{ duration: 0.3, type: "spring", damping: 25, stiffness: 300 }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Modal Header */}
                        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-2xl font-bold">Yerleşim Bekleyen Üyeler</h2>
                                    <p className="text-blue-100 mt-1">
                                        Ekibe yeni katılan üyeleri ağaca yerleştirin
                                    </p>
                                </div>
                                <button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/30 hover:bg-opacity-20 rounded-lg transition-colors"
                                >
                                    <X className="h-6 w-6" />
                                </button>
                            </div>
                        </div>

                        {/* Modal Content */}
                        <div className="p-6">
                            {pendingMembers.length === 0 ? (
                                <div className="text-center py-12">
                                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <User className="h-8 w-8 text-gray-400" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                        Yerleşim Bekleyen Üye Yok
                                    </h3>
                                    <p className="text-gray-500">
                                        Şu anda ağaca yerleştirilmeyi bekleyen yeni üye bulunmuyor.
                                    </p>
                                </div>
                            ) : (
                                <div>
                                    <div className="mb-6">
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <h3 className="font-medium text-blue-900 mb-2">Yerleştirme Kuralları</h3>
                                            <ul className="text-sm text-blue-700 space-y-1">
                                                <li>• <strong>Sola Ekle:</strong> Üyeyi ağacın en sol boş pozisyonuna yerleştirir</li>
                                                <li>• <strong>Sağa Ekle:</strong> Üyeyi ağacın en sağ boş pozisyonuna yerleştirir</li>
                                                <li>• Yerleştirme önceliği: Üst seviyelerden alt seviyelere doğru</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div className="space-y-4 max-h-96 overflow-y-auto">
                                        {pendingMembers.map((member) => (
                                            <div
                                                key={member.id}
                                                className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="w-12 h-12 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                                                            <User className="h-6 w-6 text-white" />
                                                        </div>
                                                        <div>
                                                            <h4 className="font-semibold text-gray-900">{member.name}</h4>
                                                            <p className="text-sm text-gray-600">{member.email}</p>
                                                            <p className="text-sm text-gray-600">{member.phone}</p>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="flex items-center text-sm text-gray-500 mb-2">
                                                            <Calendar className="h-4 w-4 mr-1" />
                                                            {new Date(member.joinDate).toLocaleDateString('tr-TR')}
                                                        </div>
                                                        <div className="flex items-center text-sm text-gray-500">
                                                            <MapPin className="h-4 w-4 mr-1" />
                                                            Sponsor: {member.sponsorName}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="mt-4 pt-4 border-t border-gray-200">
                                                    <div className="flex space-x-3">
                                                        <button
                                                            onClick={() => onPlaceLeft(member.id)}
                                                            className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                                                        >
                                                            <ArrowLeft className="h-4 w-4 mr-2" />
                                                            Sola Ekle
                                                        </button>
                                                        <button
                                                            onClick={() => onPlaceRight(member.id)}
                                                            className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                                                        >
                                                            <ArrowRight className="h-4 w-4 mr-2" />
                                                            Sağa Ekle
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default PendingPlacementModal;