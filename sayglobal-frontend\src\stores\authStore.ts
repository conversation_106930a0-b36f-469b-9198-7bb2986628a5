import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { AuthUser, LoginCredentials, RegisterData, MembershipLevelIds } from '@/types';
import { authService } from '@/services/authService';

interface AuthState {
    user: AuthUser | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;
}

// Artık store'un kendi içinde action'ları yok.
// Sadece state ve basit setter'lar var.
interface AuthActions {
    clearError: () => void;
    setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;



// Backend'den gelen user verisini frontend'e uygun hale getiren helper fonksiyon
const mapBackendUserToFrontend = (backendUser: any): AuthUser => {
    if (!backendUser) {
        throw new Error('Backend user data is null or undefined');
    }

    // Check for ID field in different formats
    console.log('🔍 Mapping user data for:', backendUser.email || 'unknown user');
    console.log('🔍 Backend user fields:', { userId: backendUser.userId, id: backendUser.id, role: backendUser.role, referenceId: backendUser.referenceId, referanceId: backendUser.referanceId });

    // Roller dizisinden veya tek role alanından ana rolü belirle
    let role: 'admin' | 'dealership' | 'customer' = 'customer';

    // Önce string role alanını kontrol et
    if (backendUser.role) {
        if (backendUser.role.toLowerCase() === 'admin') {
            role = 'admin';
        } else if (backendUser.role.toLowerCase() === 'dealership') {
            role = 'dealership';
        } else {
            role = 'customer';
        }
    }
    // Sonra roles dizisini kontrol et (fallback)
    else if (backendUser.roles && Array.isArray(backendUser.roles) && backendUser.roles.length > 0) {
        if (backendUser.roles.includes('Admin')) {
            role = 'admin';
        } else if (backendUser.roles.includes('Dealership')) {
            role = 'dealership';
        }
    }

    // 🔍 ID field mapping - Backend artık userId alanını kullanıyor
    let userId = backendUser.userId || backendUser.id || backendUser.ID || backendUser.user_id;

    // Eğer hiçbiri yoksa ve email varsa, geçici bir ID ata
    if (!userId && backendUser.email) {
        console.warn('⚠️ Backend user has no ID field, generating temporary ID from email hash');
        // Email'den basit bir hash oluştur
        userId = Math.abs(backendUser.email.split('').reduce((a: number, b: string) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
        }, 0));
    }

    const mappedUser = {
        ...backendUser,
        id: userId, // ID'yi explicit olarak set et
        role,
        membershipLevel: (backendUser.membershipLevelId !== undefined ? backendUser.membershipLevelId : 0) as MembershipLevelIds, // 0 değeri korunmalı
        joinDate: backendUser.registeredAt ? new Date(backendUser.registeredAt).toISOString().split('T')[0] : '',
        isDealershipApproved: role === 'dealership',
        roles: backendUser.roles || (backendUser.role ? [backendUser.role] : []),
        isActive: backendUser.isActive !== undefined ? backendUser.isActive : true,
        phoneNumber: backendUser.phoneNumber || '',
        referenceId: backendUser.referenceId || backendUser.referanceId || 0,
        careerRankId: backendUser.careerRankId || 1
    };

    console.log('✅ User mapped successfully with ID:', mappedUser.id);

    return mappedUser;
};

export const useAuthStore = create<AuthStore>()(
    devtools(
        (set, get) => ({
            // Initial state
            user: null,
            isLoading: false,
            isAuthenticated: false,
            error: null,

            // Actions
            // 🗑️ login fonksiyonu tamamen silindi. Artık useLoginMutation kullanılıyor.

            // 🗑️ logout fonksiyonu tamamen silindi. Artık useLogoutMutation kullanılıyor.

            clearError: () => set({ error: null }),

            setLoading: (loading: boolean) => set({ isLoading: loading }),
        }),
        {
            name: 'auth-store',
        }
    )
);

// 🗑️ Window event listener'ları silindi. Bu mantık artık AuthContext ve interceptor'da. 