'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import SuccessNotificationModal from '@/components/SuccessNotificationModal';
import { useModalActions } from '@/stores/modalStore';
import { motion } from 'framer-motion';
import {
    useBrands,
    useCategories,
    useSubCategories,
    useFeatureValues,
    useCategoriesByBrand,
    useSubCategoryFeatures,
    useAllFeatureDefinitions,
    useCreateBrand,
    useCreateCategory,
    useCreateSubCategory,
    useCreateFeatureDefinition,
    useCreateFeatureValue,
    useCreateBrandCategory,
    useCreateSubCategoryFeature
} from '@/hooks/useCategoryManagement';
import {
    Building2,
    Tag,
    Grid3x3,
    Settings,
    Plus,
    Edit,
    Trash2,
    Search,
    Filter,
    Link,
    ChevronRight
} from 'lucide-react';

const CategoryManagementPage = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const { openSuccessNotificationModal } = useModalActions();
    const [activeTab, setActiveTab] = useState('brands');
    const [searchTerm, setSearchTerm] = useState('');

    const [selectedCategoryIdForList, setSelectedCategoryIdForList] = useState<number | null>(null);
    const [selectedDefinitionIdForList, setSelectedDefinitionIdForList] = useState<number | null>(null);
    const [selectedCategoryIdForFeatures, setSelectedCategoryIdForFeatures] = useState<number | null>(null);
    const [selectedSubCategoryIdForFeatures, setSelectedSubCategoryIdForFeatures] = useState<number | null>(null);

    // State for Feature and FeatureValue tabs
    const [selectedCategoryIdForFeatureTabs, setSelectedCategoryIdForFeatureTabs] = useState<number | null>(null);
    const [selectedSubCategoryIdForFeatureTabs, setSelectedSubCategoryIdForFeatureTabs] = useState<number | null>(null);

    // State for the SubCategory-Feature relation form
    const [selectedCategoryIdForRelationForm, setSelectedCategoryIdForRelationForm] = useState<number | null>(null);
    const [selectedSubCategoryIdForRelationForm, setSelectedSubCategoryIdForRelationForm] = useState<number | null>(null);

    // State for the Create Feature Definition form
    const [selectedCategoryIdForCreateForm, setSelectedCategoryIdForCreateForm] = useState<number | null>(null);
    const [selectedSubCategoryIdForCreateForm, setSelectedSubCategoryIdForCreateForm] = useState<number | null>(null);

    // State for the Feature Value List
    const [selectedCategoryIdForValueList, setSelectedCategoryIdForValueList] = useState<number | null>(null);
    const [selectedSubCategoryIdForValueList, setSelectedSubCategoryIdForValueList] = useState<number | null>(null);


    // Queries
    const { data: brands = [], isLoading: brandsLoading } = useBrands();
    const { data: categories = [], isLoading: categoriesLoading } = useCategories();
    // Subcategories for the "Sub-Categories" tab list
    const { data: subCategories = [], isLoading: subCategoriesLoading } = useSubCategories(selectedCategoryIdForList ?? undefined);
    // Subcategories for the "Category-Feature Relations" tab filter
    const { data: subCategoriesForFeatures = [], isLoading: subCategoriesForFeaturesLoading } = useSubCategories(selectedCategoryIdForFeatures ?? undefined);
    // Subcategories for the "Features" and "Feature Values" tab filters
    const { data: subCategoriesForFeatureTabs = [], isLoading: subCategoriesForFeatureTabsLoading } = useSubCategories(selectedCategoryIdForFeatureTabs ?? undefined);
    // Subcategories for the relation form
    const { data: subCategoriesForRelationForm = [], isLoading: subCategoriesForRelationFormLoading } = useSubCategories(selectedCategoryIdForRelationForm ?? undefined);
    // Subcategories for the create feature form
    const { data: subCategoriesForCreateForm = [], isLoading: subCategoriesForCreateFormLoading } = useSubCategories(selectedCategoryIdForCreateForm ?? undefined);
    // Subcategories for the feature value list
    const { data: subCategoriesForValueList = [], isLoading: subCategoriesForValueListLoading } = useSubCategories(selectedCategoryIdForValueList ?? undefined);


    const { data: featureDefs = [], isLoading: featureDefsLoading } = useSubCategoryFeatures(selectedSubCategoryIdForFeatureTabs ?? undefined);
    // Feature definitions for the relation form
    const { data: featureDefsForRelationForm = [], isLoading: featureDefsForRelationFormLoading } = useSubCategoryFeatures(selectedSubCategoryIdForRelationForm ?? undefined);
    // Feature definitions for the value list
    const { data: featureDefsForValueList = [], isLoading: featureDefsForValueListLoading } = useSubCategoryFeatures(selectedSubCategoryIdForValueList ?? undefined);

    const { data: allFeatureDefinitions = [], isLoading: allFeatureDefinitionsLoading } = useAllFeatureDefinitions();

    const { data: featureValues = [], isLoading: featureValuesLoading } = useFeatureValues(selectedDefinitionIdForList ?? undefined);
    const [selectedBrandId, setSelectedBrandId] = useState<number | null>(null);
    const { data: brandCategories = [], isLoading: brandCategoriesLoading } = useCategoriesByBrand(selectedBrandId || undefined);
    const { data: subCategoryFeatures = [], isLoading: subCategoryFeaturesLoading } = useSubCategoryFeatures(selectedSubCategoryIdForFeatures ?? undefined);

    // Mutations
    const createBrandMutation = useCreateBrand();
    const createCategoryMutation = useCreateCategory();
    const createSubCategoryMutation = useCreateSubCategory();
    const createFeatureDefinitionMutation = useCreateFeatureDefinition();
    const createFeatureValueMutation = useCreateFeatureValue();
    const createBrandCategoryMutation = useCreateBrandCategory();
    const createSubCategoryFeatureMutation = useCreateSubCategoryFeature();

    const [isCreating, setIsCreating] = useState(false);

    const isFetching = brandsLoading || categoriesLoading || subCategoriesLoading ||
        featureDefsLoading || featureValuesLoading || brandCategoriesLoading ||
        subCategoryFeaturesLoading;

    const [formData, setFormData] = useState({
        brand: { name: '', logoUrl: '' },
        category: { name: '' },
        subCategory: { name: '', categoryId: '' },
        featureDefinition: { name: '', description: '', isRequired: false, isMultiSelect: false },
        featureValue: { featureDefinitionId: '', value: '' },
        brandCategory: { brandId: '', categoryId: '' },
        subCategoryFeature: { subCategoryId: '', featureDefinitionId: '' }
    });

    // Hook for checking if the brand-category relationship already exists in the form
    const { data: existingCategoriesForForm = [] } = useCategoriesByBrand(
        formData.brandCategory.brandId ? parseInt(formData.brandCategory.brandId) : undefined
    );

    useEffect(() => {
        if (!isLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, isLoading, router]);



    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }

    const handleSubmit = async (type: string) => {
        setIsCreating(true);
        try {
            switch (type) {
                case 'featureDefinition':
                    if (!formData.featureDefinition.name || !selectedSubCategoryIdForCreateForm) {
                        openSuccessNotificationModal({
                            title: 'Eksik Bilgi',
                            message: 'Lütfen özellik adını girin ve bir alt kategori seçin.',
                            icon: 'warning',
                            autoClose: true,
                            duration: 5000
                        });
                        setIsCreating(false);
                        return;
                    }

                    const newFeatureResponse = await createFeatureDefinitionMutation.mutateAsync({
                        name: formData.featureDefinition.name,
                        description: formData.featureDefinition.description,
                        isRequired: formData.featureDefinition.isRequired,
                        isMultiSelect: formData.featureDefinition.isMultiSelect,
                    });

                    if (newFeatureResponse && newFeatureResponse.data) {
                        await createSubCategoryFeatureMutation.mutateAsync({
                            subCategoryId: selectedSubCategoryIdForCreateForm,
                            featureDefinitionId: Number(newFeatureResponse.data),
                        });

                        const subCategoryName = subCategoriesForCreateForm.find(sc => sc.id === selectedSubCategoryIdForCreateForm)?.name || '';
                        openSuccessNotificationModal({
                            title: 'Özellik Tanımı Oluşturuldu!',
                            message: `'${formData.featureDefinition.name}' özelliği oluşturuldu ve '${subCategoryName}' alt kategorisine bağlandı.`,
                            icon: 'success',
                            autoClose: true,
                            duration: 5000
                        });

                        setFormData(prev => ({ ...prev, featureDefinition: { name: '', description: '', isRequired: false, isMultiSelect: false } }));
                    } else {
                        throw new Error("Özellik oluşturuldu ancak ID alınamadı.");
                    }
                    break;

                case 'subCategoryFeature':
                    await createSubCategoryFeatureMutation.mutateAsync({
                        subCategoryId: parseInt(formData.subCategoryFeature.subCategoryId),
                        featureDefinitionId: parseInt(formData.subCategoryFeature.featureDefinitionId)
                    });
                    setFormData(prev => ({ ...prev, subCategoryFeature: { subCategoryId: '', featureDefinitionId: '' } }));
                    openSuccessNotificationModal({
                        title: 'Kategori-Özellik İlişkisi Oluşturuldu!',
                        message: 'Alt kategori ve özellik arasında ilişki başarıyla kuruldu.',
                        icon: 'success',
                        autoClose: true,
                        duration: 5000
                    });
                    break;

                case 'featureValue':
                    await createFeatureValueMutation.mutateAsync({
                        featureDefinitionId: parseInt(formData.featureValue.featureDefinitionId),
                        value: formData.featureValue.value
                    });
                    setFormData(prev => ({ ...prev, featureValue: { featureDefinitionId: '', value: '' } }));
                    openSuccessNotificationModal({
                        title: 'Özellik Değeri Oluşturuldu!',
                        message: `"${formData.featureValue.value}" değeri başarıyla eklendi.`,
                        icon: 'success',
                        autoClose: true,
                        duration: 5000
                    });
                    break;

                case 'brand':
                    await createBrandMutation.mutateAsync(formData.brand);
                    setFormData(prev => ({ ...prev, brand: { name: '', logoUrl: '' } }));
                    openSuccessNotificationModal({
                        title: 'Marka Oluşturuldu!',
                        message: `"${formData.brand.name}" markası başarıyla oluşturuldu.`,
                        icon: 'success',
                        autoClose: true,
                        duration: 5000
                    });
                    break;

                case 'category':
                    await createCategoryMutation.mutateAsync(formData.category);
                    setFormData(prev => ({ ...prev, category: { name: '' } }));
                    openSuccessNotificationModal({
                        title: 'Kategori Oluşturuldu!',
                        message: `"${formData.category.name}" kategorisi başarıyla oluşturuldu.`,
                        icon: 'success',
                        autoClose: true,
                        duration: 5000
                    });
                    break;

                case 'subCategory':
                    await createSubCategoryMutation.mutateAsync({
                        name: formData.subCategory.name,
                        categoryId: parseInt(formData.subCategory.categoryId)
                    });
                    setFormData(prev => ({ ...prev, subCategory: { name: '', categoryId: '' } }));
                    openSuccessNotificationModal({
                        title: 'Alt Kategori Oluşturuldu!',
                        message: `"${formData.subCategory.name}" alt kategorisi başarıyla oluşturuldu.`,
                        icon: 'success',
                        autoClose: true,
                        duration: 5000
                    });
                    break;

                case 'brandCategory':
                    await createBrandCategoryMutation.mutateAsync({
                        brandId: parseInt(formData.brandCategory.brandId),
                        categoryId: parseInt(formData.brandCategory.categoryId)
                    });
                    setFormData(prev => ({ ...prev, brandCategory: { brandId: '', categoryId: '' } }));
                    openSuccessNotificationModal({
                        title: 'Marka-Kategori İlişkisi Oluşturuldu!',
                        message: 'Marka ve kategori arasında ilişki başarıyla kuruldu.',
                        icon: 'success',
                        autoClose: true,
                        duration: 5000
                    });
                    break;

                // Diğer case'ler buraya eklenebilir...

                default:
                    console.log(`Unhandled form type: ${type}`);
                    break;
            }
        } catch (error) {
            console.error('Form gönderilirken hata:', error);
            openSuccessNotificationModal({
                title: 'İşlem Hatası',
                message: 'İşlem sırasında bir hata oluştu. Lütfen tekrar deneyin.',
                icon: 'error',
                autoClose: true,
                duration: 5000
            });
        } finally {
            setIsCreating(false);
        }
    };

    const tabs = [
        { id: 'brands', label: 'Markalar', icon: Building2 },
        { id: 'categories', label: 'Kategoriler', icon: Tag },
        { id: 'subCategories', label: 'Alt Kategoriler', icon: Grid3x3 },
        { id: 'features', label: 'Özellikler', icon: Settings },
        { id: 'featureValues', label: 'Özellik Değerleri', icon: Edit },
        { id: 'brandCategories', label: 'Marka-Kategori İlişkileri', icon: Link },
        { id: 'subCategoryFeatures', label: 'Kategori-Özellik İlişkileri', icon: ChevronRight }
    ];

    const renderBrandTab = () => (
        <div className="space-y-8">
            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold text-blue-900 mb-2">Marka Yönetimi Talimatları</h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                            <li>• Sistem genelinde kullanılacak markaları buradan ekleyebilirsiniz</li>
                            <li>• Marka adı zorunlu alan olup, logo URL'si opsiyoneldir</li>
                            <li>• Eklenen markalar daha sonra marka-kategori ilişkileri tabında kategorilerle eşleştirilebilir</li>
                            <li>• Marka logosu için geçerli bir URL adresi kullanın (örn: https://example.com/logo.png)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Marka Ekle</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Marka Adı</label>
                        <input
                            type="text"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.brand.name}
                            onChange={(e) => setFormData(prev => ({ ...prev, brand: { ...prev.brand, name: e.target.value } }))}
                            placeholder="Marka adını girin"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Logo URL (Opsiyonel)</label>
                        <input
                            type="url"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.brand.logoUrl}
                            onChange={(e) => setFormData(prev => ({ ...prev, brand: { ...prev.brand, logoUrl: e.target.value } }))}
                            placeholder="Logo URL'sini girin"
                        />
                    </div>
                </div>
                <button
                    onClick={() => handleSubmit('brand')}
                    disabled={!formData.brand.name || isCreating}
                    className="mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                >
                    {isCreating ? 'Oluşturuluyor...' : 'Marka Oluştur'}
                </button>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Mevcut Markalar</h3>
                <div className="space-y-3">
                    {brands.length > 0 ? brands.map(brand => (
                        <div key={brand.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <div className="flex items-center space-x-4">
                                {brand.logoUrl ? (
                                    <img src={brand.logoUrl} alt={brand.name} className="w-12 h-12 object-contain rounded-md bg-white p-1 border" />
                                ) : (
                                    <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                                        <Building2 className="w-6 h-6 text-gray-400" />
                                    </div>
                                )}
                                <span className="font-semibold text-gray-800">{brand.name}</span>
                            </div>
                        </div>
                    )) : (
                        <p className="text-gray-500 text-center py-4">Henüz marka eklenmemiş.</p>
                    )}
                </div>
            </div>
        </div>
    );

    const renderCategoryTab = () => (
        <div className="space-y-8">
            {/* Instructions */}
            <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <Tag className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold text-green-900 mb-2">Kategori Yönetimi Talimatları</h4>
                        <ul className="text-sm text-green-800 space-y-1">
                            <li>• Ana kategorileri buradan oluşturabilirsiniz (örn: Elektronik, Giyim, Ev & Yaşam)</li>
                            <li>• Kategori adı zorunlu alan olup, benzersiz olmalıdır</li>
                            <li>• Oluşturulan kategoriler alt kategoriler tabında ana kategori olarak seçilebilir</li>
                            <li>• Kategoriler marka-kategori ilişkileri tabında markalarla eşleştirilebilir</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Kategori Ekle</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Kategori Adı</label>
                        <input
                            type="text"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.category.name}
                            onChange={(e) => setFormData(prev => ({ ...prev, category: { ...prev.category, name: e.target.value } }))}
                            placeholder="Kategori adını girin"
                        />
                    </div>
                </div>
                <button
                    onClick={() => handleSubmit('category')}
                    disabled={!formData.category.name || isCreating}
                    className="mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                >
                    {isCreating ? 'Oluşturuluyor...' : 'Kategori Oluştur'}
                </button>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Mevcut Kategoriler</h3>
                <div className="space-y-3">
                    {categories.length > 0 ? categories.map(category => (
                        <div key={category.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <span className="font-semibold text-gray-800">{category.name}</span>
                        </div>
                    )) : (
                        <p className="text-gray-500 text-center py-4">Henüz kategori eklenmemiş.</p>
                    )}
                </div>
            </div>
        </div>
    );

    const renderSubCategoryTab = () => (
        <div className="space-y-8">
            {/* Instructions */}
            <div className="bg-purple-50 border border-purple-200 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <Grid3x3 className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold text-purple-900 mb-2">Alt Kategori Yönetimi Talimatları</h4>
                        <ul className="text-sm text-purple-800 space-y-1">
                            <li>• Alt kategoriler bir ana kategoriye bağlı olarak oluşturulur (örn: Elektronik > Telefon)</li>
                            <li>• Alt kategori adı ve ana kategori seçimi zorunlu alanlardır</li>
                            <li>• Alt kategoriler ürün özelliklerini tanımlamak için kategori-özellik ilişkileri tabında kullanılır</li>
                            <li>• Listeleme sırasında filtrelemek için ana kategori seçebilirsiniz</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Alt Kategori Ekle</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Alt Kategori Adı</label>
                        <input
                            type="text"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.subCategory.name}
                            onChange={(e) => setFormData(prev => ({ ...prev, subCategory: { ...prev.subCategory, name: e.target.value } }))}
                            placeholder="Alt kategori adını girin"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Ana Kategori</label>
                        <select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                            value={formData.subCategory.categoryId}
                            onChange={(e) => setFormData(prev => ({ ...prev, subCategory: { ...prev.subCategory, categoryId: e.target.value } }))}
                        >
                            <option value="">Kategori seçin</option>
                            {categories.map(category => (
                                <option key={category.id} value={category.id}>{category.name}</option>
                            ))}
                        </select>
                    </div>
                </div>
                <button
                    onClick={() => handleSubmit('subCategory')}
                    disabled={!formData.subCategory.name || !formData.subCategory.categoryId || isCreating}
                    className="mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                >
                    {isCreating ? 'Oluşturuluyor...' : 'Alt Kategori Oluştur'}
                </button>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Mevcut Alt Kategoriler</h3>
                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kategoriye Göre Filtrele</label>
                    <select
                        onChange={(e) => setSelectedCategoryIdForList(e.target.value ? Number(e.target.value) : null)}
                        value={selectedCategoryIdForList ?? ''}
                        className="w-full max-w-xs px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                    >
                        <option value="">Kategori Seçin</option>
                        {categories.map(category => (
                            <option key={category.id} value={category.id}>{category.name}</option>
                        ))}
                    </select>
                </div>

                {subCategoriesLoading && <p className="text-gray-500 text-center py-4">Alt kategoriler yükleniyor...</p>}

                {!subCategoriesLoading && selectedCategoryIdForList && (
                    <div className="space-y-3">
                        {subCategories.length > 0 ? subCategories.map(subCategory => (
                            <div key={subCategory.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                                <div className="flex items-center space-x-2">
                                    <span className="font-semibold text-gray-800">{subCategory.name}</span>
                                    <ChevronRight className="h-5 w-5 text-gray-400" />
                                    <span className="text-md text-gray-600">
                                        {categories.find(c => c.id === subCategory.categoryId)?.name}
                                    </span>
                                </div>
                            </div>
                        )) : (
                            <p className="text-gray-500 text-center py-4">Bu kategoriye ait alt kategori bulunamadı.</p>
                        )}
                    </div>
                )}

                {!selectedCategoryIdForList && !subCategoriesLoading && (
                    <p className="text-gray-500 text-center py-4">Alt kategorileri görüntülemek için bir kategori seçin.</p>
                )}
            </div>
        </div>
    );

    const renderFeatureTab = () => (
        <div className="space-y-8">
            {/* Instructions */}
            <div className="bg-orange-50 border border-orange-200 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <Settings className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold text-orange-900 mb-2">Özellik Tanımları Yönetimi Talimatları</h4>
                        <ul className="text-sm text-orange-800 space-y-1">
                            <li>• Özellik tanımları ürün özelliklerini kategorize etmek için kullanılır (örn: Renk, Beden, Marka)</li>
                            <li>• Özellik oluşturulurken mutlaka bir alt kategori seçmelisiniz</li>
                            <li>• "Zorunlu Özellik" işaretlenirse, bu özellik o kategorideki tüm ürünlerde bulunmak zorundadır</li>
                            <li>• "Çoklu Seçim" aktifse, bu özellik için birden fazla değer seçilebilir</li>
                            <li>• Özellik tanımları oluşturulduktan sonra özellik değerleri tabında değerler eklenebilir</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Özellik Tanımı Ekle</h3>

                {/* Filters for creating */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Eklenecek Kategori</label>
                        <select
                            value={selectedCategoryIdForCreateForm || ''}
                            onChange={(e) => {
                                const id = e.target.value ? Number(e.target.value) : null;
                                setSelectedCategoryIdForCreateForm(id);
                                setSelectedSubCategoryIdForCreateForm(null);
                            }}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                        >
                            <option value="">Önce Kategori Seçin</option>
                            {categories.map(cat => (
                                <option key={cat.id} value={cat.id}>{cat.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Eklenecek Alt Kategori</label>
                        <select
                            value={selectedSubCategoryIdForCreateForm || ''}
                            onChange={(e) => setSelectedSubCategoryIdForCreateForm(e.target.value ? Number(e.target.value) : null)}
                            disabled={!selectedCategoryIdForCreateForm || subCategoriesForCreateFormLoading}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                        >
                            <option value="">Alt Kategori Seçin</option>
                            {subCategoriesForCreateForm.map(subCat => (
                                <option key={subCat.id} value={subCat.id}>{subCat.name}</option>
                            ))}
                        </select>
                    </div>
                </div>


                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Özellik Adı</label>
                        <input
                            type="text"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.featureDefinition.name}
                            onChange={(e) => setFormData(prev => ({ ...prev, featureDefinition: { ...prev.featureDefinition, name: e.target.value } }))}
                            placeholder="Örn: Renk, Beden"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                        <input
                            type="text"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.featureDefinition.description}
                            onChange={(e) => setFormData(prev => ({ ...prev, featureDefinition: { ...prev.featureDefinition, description: e.target.value } }))}
                            placeholder="Özellik açıklaması"
                        />
                    </div>
                </div>
                <div className="mt-6 flex items-center space-x-8">
                    <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500"
                            checked={formData.featureDefinition.isRequired}
                            onChange={(e) => setFormData(prev => ({ ...prev, featureDefinition: { ...prev.featureDefinition, isRequired: e.target.checked } }))}
                        />
                        <span className="text-sm font-medium text-gray-700">Zorunlu Özellik</span>
                    </label>
                    <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500"
                            checked={formData.featureDefinition.isMultiSelect}
                            onChange={(e) => setFormData(prev => ({ ...prev, featureDefinition: { ...prev.featureDefinition, isMultiSelect: e.target.checked } }))}
                        />
                        <span className="text-sm font-medium text-gray-700">Çoklu Seçim</span>
                    </label>
                </div>
                <button
                    onClick={() => handleSubmit('featureDefinition')}
                    disabled={!formData.featureDefinition.name || !selectedSubCategoryIdForCreateForm || isCreating}
                    className="mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                >
                    {isCreating ? 'Oluşturuluyor...' : 'Özellik Tanımı Oluştur'}
                </button>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Mevcut Özellik Tanımları</h3>

                {/* Filters for listing */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Kategoriye Göre Filtrele</label>
                        <select
                            value={selectedCategoryIdForFeatureTabs || ''}
                            onChange={(e) => {
                                const id = e.target.value ? Number(e.target.value) : null;
                                setSelectedCategoryIdForFeatureTabs(id);
                                setSelectedSubCategoryIdForFeatureTabs(null); // Reset sub-category
                            }}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                        >
                            <option value="">Önce Kategori Seçin</option>
                            {categories.map(cat => (
                                <option key={cat.id} value={cat.id}>{cat.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Alt Kategoriye Göre Filtrele</label>
                        <select
                            value={selectedSubCategoryIdForFeatureTabs || ''}
                            onChange={(e) => setSelectedSubCategoryIdForFeatureTabs(e.target.value ? Number(e.target.value) : null)}
                            disabled={!selectedCategoryIdForFeatureTabs || subCategoriesForFeatureTabsLoading}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                        >
                            <option value="">Alt Kategori Seçin</option>
                            {subCategoriesForFeatureTabs.map(subCat => (
                                <option key={subCat.id} value={subCat.id}>{subCat.name}</option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="space-y-3">
                    {featureDefsLoading ? (
                        <p className="text-gray-500 text-center py-4">Özellikler yükleniyor...</p>
                    ) : selectedSubCategoryIdForFeatureTabs && featureDefs.length > 0 ? featureDefs.map(def => (
                        <div key={def.featureDefinitionId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <span className="font-semibold text-gray-800">{def.featureDefinitionName}</span>
                        </div>
                    )) : (
                        <p className="text-gray-500 text-center py-4">
                            {selectedSubCategoryIdForFeatureTabs ? 'Bu alt kategoriye ait özellik bulunamadı.' : 'Özellik tanımlarını görmek için bir kategori ve alt kategori seçin.'}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );

    const renderFeatureValueTab = () => (
        <div className="space-y-8">
            {/* Instructions */}
            <div className="bg-teal-50 border border-teal-200 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                        <Edit className="w-5 h-5 text-teal-600" />
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold text-teal-900 mb-2">Özellik Değerleri Yönetimi Talimatları</h4>
                        <ul className="text-sm text-teal-800 space-y-1">
                            <li>• Özellik değerleri, özellik tanımları altında yer alan seçilebilir değerlerdir</li>
                            <li>• Örneğin "Renk" özelliği için "Kırmızı, Mavi, Yeşil" gibi değerler ekleyebilirsiniz</li>
                            <li>• Değer eklemek için önce kategori ve alt kategori seçin, sonra o alt kategorideki özellik tanımlarından birini seçin</li>
                            <li>• Listeleme yaparken de aynı filtre sırasını takip edin: Kategori > Alt Kategori > Özellik Tanımı</li>
                            <li>• Bu değerler ürün ekleme sayfasında kullanıcıya seçenek olarak sunulur</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Özellik Değeri Ekle</h3>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                        <select
                            value={selectedCategoryIdForFeatureTabs || ''}
                            onChange={(e) => {
                                const id = e.target.value ? Number(e.target.value) : null;
                                setSelectedCategoryIdForFeatureTabs(id);
                                setSelectedSubCategoryIdForFeatureTabs(null); // Reset sub-category
                            }}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                        >
                            <option value="">Önce Kategori Seçin</option>
                            {categories.map(cat => (
                                <option key={cat.id} value={cat.id}>{cat.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Alt Kategori</label>
                        <select
                            value={selectedSubCategoryIdForFeatureTabs || ''}
                            onChange={(e) => setSelectedSubCategoryIdForFeatureTabs(e.target.value ? Number(e.target.value) : null)}
                            disabled={!selectedCategoryIdForFeatureTabs || subCategoriesForFeatureTabsLoading}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                        >
                            <option value="">Alt Kategori Seçin</option>
                            {subCategoriesForFeatureTabs.map(subCat => (
                                <option key={subCat.id} value={subCat.id}>{subCat.name}</option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Özellik Tanımı</label>
                        <select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                            value={formData.featureValue.featureDefinitionId}
                            onChange={(e) => setFormData(prev => ({ ...prev, featureValue: { ...prev.featureValue, featureDefinitionId: e.target.value } }))}
                            disabled={!selectedSubCategoryIdForFeatureTabs || featureDefsLoading}
                        >
                            <option value="">Özellik seçin</option>
                            {featureDefs.map(def => (
                                <option key={def.featureDefinitionId} value={def.featureDefinitionId}>{def.featureDefinitionName}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Değer</label>
                        <input
                            type="text"
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-black"
                            value={formData.featureValue.value}
                            onChange={(e) => setFormData(prev => ({ ...prev, featureValue: { ...prev.featureValue, value: e.target.value } }))}
                            placeholder="Örn: Kırmızı, XL"
                        />
                    </div>
                </div>
                <button
                    onClick={() => handleSubmit('featureValue')}
                    disabled={!formData.featureValue.featureDefinitionId || !formData.featureValue.value || isCreating}
                    className="mt-6 bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                >
                    {isCreating ? 'Oluşturuluyor...' : 'Değer Ekle'}
                </button>
            </div>

            <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Mevcut Özellik Değerleri</h3>

                {/* Filters for the list */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                        <select
                            value={selectedCategoryIdForValueList || ''}
                            onChange={(e) => {
                                const id = e.target.value ? Number(e.target.value) : null;
                                setSelectedCategoryIdForValueList(id);
                                setSelectedSubCategoryIdForValueList(null);
                                setSelectedDefinitionIdForList(null);
                            }}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                        >
                            <option value="">Kategori Seçin</option>
                            {categories.map(cat => (
                                <option key={cat.id} value={cat.id}>{cat.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Alt Kategori</label>
                        <select
                            value={selectedSubCategoryIdForValueList || ''}
                            onChange={(e) => {
                                const id = e.target.value ? Number(e.target.value) : null;
                                setSelectedSubCategoryIdForValueList(id);
                                setSelectedDefinitionIdForList(null);
                            }}
                            disabled={!selectedCategoryIdForValueList || subCategoriesForValueListLoading}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                        >
                            <option value="">Alt Kategori Seçin</option>
                            {subCategoriesForValueList.map(subCat => (
                                <option key={subCat.id} value={subCat.id}>{subCat.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Görüntülenecek Özelliği Seçin</label>
                        <select
                            value={selectedDefinitionIdForList || ''}
                            onChange={(e) => setSelectedDefinitionIdForList(e.target.value ? Number(e.target.value) : null)}
                            disabled={!selectedSubCategoryIdForValueList || featureDefsForValueListLoading}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                        >
                            <option value="">Özellik Seçin</option>
                            {featureDefsForValueList.map(def => (
                                <option key={def.featureDefinitionId} value={def.featureDefinitionId}>{def.featureDefinitionName}</option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="space-y-3">
                    {featureValuesLoading ? (
                        <p className="text-gray-500 text-center py-4">Değerler yükleniyor...</p>
                    ) : selectedDefinitionIdForList && featureValues.length > 0 ? featureValues.map(value => (
                        <div key={value.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <span className="font-semibold text-gray-800">{value.value}</span>
                        </div>
                    )) : (
                        <p className="text-gray-500 text-center py-4">
                            {selectedDefinitionIdForList ? 'Bu özelliğe ait değer bulunamadı.' : 'Değerleri görmek için yukarıdan bir kategori, alt kategori ve özellik tanımı seçin.'}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );

    const renderBrandCategoryTab = () => {
        const categoryId = formData.brandCategory.categoryId ? parseInt(formData.brandCategory.categoryId) : undefined;
        const brandCategoryExists = existingCategoriesForForm.some(c => c.id === categoryId);

        return (
            <div className="space-y-8">
                {/* Instructions */}
                <div className="bg-indigo-50 border border-indigo-200 rounded-xl p-6">
                    <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                            <Link className="w-5 h-5 text-indigo-600" />
                        </div>
                        <div>
                            <h4 className="text-lg font-semibold text-indigo-900 mb-2">Marka-Kategori İlişkileri Yönetimi Talimatları</h4>
                            <ul className="text-sm text-indigo-800 space-y-1">
                                <li>• Markalar ve kategoriler arasında ilişki kurmak için bu bölümü kullanın</li>
                                <li>• Bu ilişkiler, ürün ekleme sırasında hangi markanın hangi kategorilerde kullanılabileceğini belirler</li>
                                <li>• Bir marka birden fazla kategoriyle ilişkilendirilebilir</li>
                                <li>• Sistem mevcut ilişkileri kontrol eder ve tekrar ilişki kurulmasını engeller</li>
                                <li>• Marka seçerek o markaya ait kategorileri görüntüleyebilirsiniz</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                    <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Marka-Kategori İlişkisi Ekle</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Marka</label>
                            <select
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                                value={formData.brandCategory.brandId}
                                onChange={(e) => setFormData(prev => ({ ...prev, brandCategory: { ...prev.brandCategory, brandId: e.target.value } }))}
                            >
                                <option value="">Marka seçin</option>
                                {brands.map(brand => (
                                    <option key={brand.id} value={brand.id}>{brand.name}</option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                            <select
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                                value={formData.brandCategory.categoryId}
                                onChange={(e) => setFormData(prev => ({ ...prev, brandCategory: { ...prev.brandCategory, categoryId: e.target.value } }))}
                            >
                                <option value="">Kategori seçin</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.id}>{category.name}</option>
                                ))}
                            </select>
                        </div>
                    </div>
                    <div className="flex items-center space-x-4 mt-6">
                        <button
                            onClick={() => handleSubmit('brandCategory')}
                            disabled={!formData.brandCategory.brandId || !formData.brandCategory.categoryId || isCreating || brandCategoryExists}
                            className="bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                        >
                            {isCreating ? 'Oluşturuluyor...' : 'İlişki Oluştur'}
                        </button>
                        {brandCategoryExists && (
                            <p className="text-sm text-yellow-600 font-medium">Bu ilişki zaten mevcut.</p>
                        )}
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                    <h3 className="text-xl font-bold text-gray-800 mb-6">Marka Kategorilerini Görüntüle</h3>
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Marka Seçin
                        </label>
                        <select
                            value={selectedBrandId || ''}
                            onChange={(e) => setSelectedBrandId(e.target.value ? Number(e.target.value) : null)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                        >
                            <option value="">Marka seçin</option>
                            {brands.map(brand => (
                                <option key={brand.id} value={brand.id}>{brand.name}</option>
                            ))}
                        </select>
                    </div>
                    {brandCategoriesLoading ? (
                        <div className="text-center py-4 text-gray-500">Yükleniyor...</div>
                    ) : selectedBrandId ? (
                        <div className="space-y-3">
                            {brandCategories.length > 0 ? (
                                brandCategories.map(category => (
                                    <div key={category.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                                        <div className="flex items-center space-x-2">
                                            <span className="font-semibold text-gray-800">{category.name}</span>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="text-center py-4 text-gray-500">Bu markaya ait kategori bulunamadı.</div>
                            )}
                        </div>
                    ) : (
                        <div className="text-center py-4 text-gray-500">Kategorileri görüntülemek için bir marka seçin.</div>
                    )}
                </div>
            </div>
        );
    };

    const renderSubCategoryFeatureTab = () => {
        const selectedFeatureId = formData.subCategoryFeature.featureDefinitionId ? parseInt(formData.subCategoryFeature.featureDefinitionId) : undefined;
        const relationExists = featureDefsForRelationForm.some(
            def => def.featureDefinitionId === selectedFeatureId
        );

        return (
            <div className="space-y-8">
                {/* Instructions */}
                <div className="bg-pink-50 border border-pink-200 rounded-xl p-6">
                    <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                            <ChevronRight className="w-5 h-5 text-pink-600" />
                        </div>
                        <div>
                            <h4 className="text-lg font-semibold text-pink-900 mb-2">Kategori-Özellik İlişkileri Yönetimi Talimatları</h4>
                            <ul className="text-sm text-pink-800 space-y-1">
                                <li>• Alt kategoriler ve özellik tanımları arasında ilişki kurmak için bu bölümü kullanın</li>
                                <li>• Bu ilişkiler, o alt kategorideki ürünlerde hangi özelliklerin kullanılabileceğini belirler</li>
                                <li>• Bir alt kategori birden fazla özellik tanımıyla ilişkilendirilebilir</li>
                                <li>• Sistem mevcut ilişkileri kontrol eder ve tekrar ilişki kurulmasını engeller</li>
                                <li>• Özellik tanımları "Özellikler" tabından daha kolay bir şekilde alt kategoriye bağlı olarak oluşturulabilir</li>
                                <li>• Listeleme yaparken kategori ve alt kategori seçerek ilişkileri görüntüleyebilirsiniz</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                    <h3 className="text-xl font-bold text-gray-800 mb-6">Yeni Kategori-Özellik İlişkisi Ekle</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                            <select
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                                value={selectedCategoryIdForRelationForm || ''}
                                onChange={(e) => {
                                    const id = e.target.value ? Number(e.target.value) : null;
                                    setSelectedCategoryIdForRelationForm(id);
                                    setSelectedSubCategoryIdForRelationForm(null);
                                    setFormData(prev => ({ ...prev, subCategoryFeature: { subCategoryId: '', featureDefinitionId: '' } }));
                                }}
                            >
                                <option value="">Kategori seçin</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.id}>{category.name}</option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Alt Kategori</label>
                            <select
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                                value={formData.subCategoryFeature.subCategoryId}
                                disabled={!selectedCategoryIdForRelationForm || subCategoriesForRelationFormLoading}
                                onChange={(e) => {
                                    const subId = e.target.value;
                                    setSelectedSubCategoryIdForRelationForm(subId ? Number(subId) : null);
                                    setFormData(prev => ({ ...prev, subCategoryFeature: { ...prev.subCategoryFeature, subCategoryId: subId, featureDefinitionId: '' } }));
                                }}
                            >
                                <option value="">Alt Kategori seçin</option>
                                {subCategoriesForRelationForm.map(subCategory => (
                                    <option key={subCategory.id} value={subCategory.id}>{subCategory.name}</option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Özellik Tanımı</label>
                            <select
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                                value={formData.subCategoryFeature.featureDefinitionId}
                                disabled={!selectedSubCategoryIdForRelationForm || allFeatureDefinitionsLoading}
                                onChange={(e) => setFormData(prev => ({ ...prev, subCategoryFeature: { ...prev.subCategoryFeature, featureDefinitionId: e.target.value } }))}
                            >
                                <option value="">Özellik seçin</option>
                                {allFeatureDefinitions.map(feature => (
                                    <option key={feature.featureDefinitionId} value={feature.featureDefinitionId}>{feature.featureDefinitionName}</option>
                                ))}
                            </select>
                        </div>
                    </div>
                    <div className="flex items-center space-x-4 mt-6">
                        <button
                            onClick={() => handleSubmit('subCategoryFeature')}
                            disabled={!formData.subCategoryFeature.subCategoryId || !formData.subCategoryFeature.featureDefinitionId || isCreating || relationExists}
                            className="bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                        >
                            {isCreating ? 'Oluşturuluyor...' : 'İlişki Oluştur'}
                        </button>
                        {relationExists && (
                            <p className="text-sm text-yellow-600 font-medium">Bu ilişki zaten mevcut.</p>
                        )}
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow p-6 border border-gray-200">
                    <h3 className="text-xl font-bold text-gray-800 mb-6">Mevcut Kategori-Özellik İlişkileri</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Kategoriye Göre Filtrele</label>
                            <select
                                value={selectedCategoryIdForFeatures || ''}
                                onChange={(e) => {
                                    const id = e.target.value ? Number(e.target.value) : null;
                                    setSelectedCategoryIdForFeatures(id);
                                    setSelectedSubCategoryIdForFeatures(null); // Kategori değişince alt kategori seçimini sıfırla
                                }}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700"
                            >
                                <option value="">Önce Kategori Seçin</option>
                                {categories.map(cat => (
                                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Alt Kategoriye Göre Filtrele</label>
                            <select
                                value={selectedSubCategoryIdForFeatures || ''}
                                onChange={(e) => setSelectedSubCategoryIdForFeatures(e.target.value ? Number(e.target.value) : null)}
                                disabled={!selectedCategoryIdForFeatures || subCategoriesForFeaturesLoading}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition text-gray-700 disabled:bg-gray-100"
                            >
                                <option value="">Alt Kategori Seçin</option>
                                {subCategoriesForFeatures.map(subCat => (
                                    <option key={subCat.id} value={subCat.id}>{subCat.name}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div className="space-y-3">
                        {subCategoryFeaturesLoading ? (
                            <p className="text-gray-500 text-center py-4">İlişkiler yükleniyor...</p>
                        ) : selectedSubCategoryIdForFeatures && subCategoryFeatures.length > 0 ? subCategoryFeatures.map(relation => (
                            <div key={relation.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                                <div className="flex items-center space-x-2">
                                    <span className="font-semibold text-gray-800">
                                        {relation.subCategoryName}
                                    </span>
                                    <ChevronRight className="h-5 w-5 text-gray-400" />
                                    <span className="text-md text-gray-600">
                                        {relation.featureDefinitionName}
                                    </span>
                                </div>
                            </div>
                        )) : (
                            <p className="text-gray-500 text-center py-4">
                                {selectedSubCategoryIdForFeatures ? 'Bu alt kategoriye ait özellik ilişkisi bulunamadı.' : 'İlişkileri görmek için bir kategori ve alt kategori seçin.'}
                            </p>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    const renderTabContent = () => {
        switch (activeTab) {
            case 'brands':
                return renderBrandTab();
            case 'categories':
                return renderCategoryTab();
            case 'subCategories':
                return renderSubCategoryTab();
            case 'features':
                return renderFeatureTab();
            case 'featureValues':
                return renderFeatureValueTab();
            case 'brandCategories':
                return renderBrandCategoryTab();
            case 'subCategoryFeatures':
                return renderSubCategoryFeatureTab();
            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Kategori Yönetimi
                    </h1>
                    <p className="text-gray-600">
                        Marka, kategori ve özellik tanımlarını yönetin
                    </p>
                </div>

                {/* Tabs */}
                <div className="mb-8">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8 overflow-x-auto">
                            {tabs.map(tab => (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`whitespace-nowrap py-3 px-1 border-b-2 font-semibold text-sm transition-colors ${activeTab === tab.id
                                        ? 'border-red-500 text-red-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                >
                                    <div className="flex items-center space-x-2">
                                        <tab.icon className="h-5 w-5" />
                                        <span>{tab.label}</span>
                                    </div>
                                </button>
                            ))}
                        </nav>
                    </div>
                </div>

                {/* Tab Content */}
                <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                >
                    {renderTabContent()}
                </motion.div>
            </div>

            {/* Success Notification Modal */}
            <SuccessNotificationModal />
        </div>
    );
};

export default CategoryManagementPage; 