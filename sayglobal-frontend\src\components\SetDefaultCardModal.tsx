'use client';

import { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSetDefaultCardModal, useSetDefaultCardData, useModalActions } from '@/stores/modalStore';

// ⭐ Set Default Card Modal - Modal Store entegrasyonu ile
export default function SetDefaultCardModal() {
    // 🏪 Modal Store hooks
    const isOpen = useSetDefaultCardModal();
    const cardData = useSetDefaultCardData();
    const { closeSetDefaultCardModal } = useModalActions();
    // Modal açıkken body scroll'unu engelle ve titreme önle
    useEffect(() => {
        if (isOpen) {
            // Scrollbar genişliğini hesapla
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

            // Body'yi kilitle ve scrollbar genişliği kadar padding ekle
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            // Normal duruma döndür
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        }

        // Cleanup function - component unmount olduğunda
        return () => {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        };
    }, [isOpen]);

    // CardData'dan card ve onConfirm'u al
    const card = cardData?.card || null;
    const onConfirm = cardData?.onConfirm || (() => { });

    if (!card) return null;

    const getCardTypeDisplay = (type: string) => {
        switch (type.toLowerCase()) {
            case 'visa': return 'Visa';
            case 'mastercard': return 'MasterCard';
            case 'troy': return 'Troy';
            default: return type;
        }
    };

    const getCardGradient = (color: string) => {
        switch (color) {
            case 'blue': return 'from-blue-500 to-blue-700';
            case 'green': return 'from-green-500 to-green-700';
            case 'purple': return 'from-purple-500 to-purple-700';
            case 'orange': return 'from-orange-500 to-orange-700';
            default: return 'from-gray-500 to-gray-700';
        }
    };

    const handleConfirm = () => {
        onConfirm();
        closeSetDefaultCardModal();
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <>
                    {/* Backdrop */}
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={closeSetDefaultCardModal}
                        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
                    />

                    {/* Modal */}
                    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.95, y: 20 }}
                            className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6"
                        >
                            <div className="text-center">
                                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6 text-purple-600">
                                        <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                                    </svg>
                                </div>

                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                    Varsayılan Kart Olarak Ayarla
                                </h3>

                                <p className="text-sm text-gray-600 mb-6">
                                    Bu kartı varsayılan kart olarak ayarlamak istediğinizden emin misiniz?
                                </p>

                                {/* Kart Önizlemesi */}
                                <div className={`bg-gradient-to-r ${getCardGradient(card.color)} rounded-lg p-4 text-white mb-6 mx-auto max-w-xs`}>
                                    <div className="flex justify-between items-start mb-3">
                                        <div className="text-sm opacity-90">{getCardTypeDisplay(card.cardType)}</div>
                                    </div>
                                    <div className="text-base font-mono mb-2 tracking-wider">{card.cardNumber}</div>
                                    <div className="flex justify-between text-xs opacity-90">
                                        <span>{card.cardHolderName}</span>
                                        <span>{card.expirationDate}</span>
                                    </div>
                                </div>
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    onClick={closeSetDefaultCardModal}
                                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                                >
                                    İptal
                                </button>
                                <button
                                    onClick={handleConfirm}
                                    className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium"
                                >
                                    Onayla
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </>
            )}
        </AnimatePresence>
    );
} 