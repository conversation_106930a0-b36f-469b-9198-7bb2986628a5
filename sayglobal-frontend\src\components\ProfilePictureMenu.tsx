'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { authService } from '@/services/authService';
import { imageService } from '@/services/imageService';
import { useQueryClient } from '@tanstack/react-query';
import { authKeys } from '@/hooks/useAuth';

interface ProfilePictureMenuProps {
    isOpen: boolean;
    onClose: () => void;
    onFileSelect: (file: File) => void;
    hasProfilePicture: boolean;
    onDeleteSuccess?: () => void;
    onDeleteError?: (error: string) => void;
    isDeleting?: boolean;
    mode?: 'immediate' | 'edit'; // 'immediate' = direkt API çağrısı, 'edit' = sadece callback çağır
}

const ProfilePictureMenu: React.FC<ProfilePictureMenuProps> = ({
    isOpen,
    onClose,
    onFileSelect,
    hasProfilePicture,
    onDeleteSuccess,
    onDeleteError,
    isDeleting = false,
    mode = 'immediate'
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const menuRef = useRef<HTMLDivElement>(null);
    const queryClient = useQueryClient();
    const [isDeletingLocal, setIsDeletingLocal] = useState(false);

    // Menü dışına tıklandığında kapanır
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // ESC tuşu ile menüyü kapatır
    useEffect(() => {
        const handleEscKey = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscKey);
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
        };
    }, [isOpen, onClose]);

    const handleFileUpload = () => {
        fileInputRef.current?.click();
        onClose();
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            onFileSelect(file);
        }
        // Reset file input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleDeleteProfilePicture = async () => {
        if (!hasProfilePicture) return;

        if (mode === 'edit') {
            // Edit modunda sadece callback'leri çağır, API çağrısı yapma
            onDeleteSuccess?.();
            onClose();
            return;
        }

        // Immediate modunda direkt API çağrısı yap
        setIsDeletingLocal(true);

        try {
            await authService.deleteProfilePicture();

            // Cache'leri güncelle
            queryClient.invalidateQueries({ queryKey: authKeys.user() });
            queryClient.invalidateQueries({ queryKey: authKeys.profileInfo() });

            // Refetch to get updated data
            await Promise.all([
                queryClient.refetchQueries({ queryKey: authKeys.user() }),
                queryClient.refetchQueries({ queryKey: authKeys.profileInfo() })
            ]);

            onDeleteSuccess?.();
            onClose();
        } catch (error: any) {
            onDeleteError?.(error.response?.data?.message || 'Profil fotoğrafı silinirken bir hata oluştu');
        } finally {
            setIsDeletingLocal(false);
        }
    };

    return (
        <>
            <AnimatePresence>
                {isOpen && (
                    <motion.div
                        ref={menuRef}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        transition={{ duration: 0.1 }}
                        className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                    >
                        <div className="p-3 space-y-2">
                            {/* Fotoğraf Yükle - Mor Gradient Buton */}
                            <motion.button
                                type="button"
                                onClick={handleFileUpload}
                                className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center hover:shadow-lg transition-all duration-300"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Fotoğraf Yükle
                            </motion.button>

                            {/* Profil Fotoğrafını Kaldır - Kırmızı Metin */}
                            <motion.button
                                type="button"
                                onClick={handleDeleteProfilePicture}
                                disabled={!hasProfilePicture || isDeletingLocal || isDeleting}
                                className={`w-full py-2 px-3 rounded-lg text-sm flex items-center justify-center transition-all duration-300 ${!hasProfilePicture || isDeletingLocal || isDeleting
                                    ? 'text-gray-400 cursor-not-allowed bg-gray-50'
                                    : 'text-red-600 hover:bg-red-50 hover:text-red-700'
                                    }`}
                                whileHover={
                                    hasProfilePicture && !isDeletingLocal && !isDeleting
                                        ? { scale: 1.02 }
                                        : {}
                                }
                                whileTap={
                                    hasProfilePicture && !isDeletingLocal && !isDeleting
                                        ? { scale: 0.98 }
                                        : {}
                                }
                            >
                                {isDeletingLocal || isDeleting ? (
                                    <>
                                        <svg className="h-4 w-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Siliniyor...
                                    </>
                                ) : (
                                    <>
                                        <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        Profil Fotoğrafını Kaldır
                                    </>
                                )}
                            </motion.button>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Hidden file input */}
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
            />
        </>
    );
};

export default ProfilePictureMenu; 