'use client';

import React, { createContext, useContext, useEffect, useCallback, useState } from 'react';
import { AuthUser, LoginCredentials, RegisterData, AuthContextType } from '@/types';
import { useAuthStore } from '@/stores/authStore';
import { useLoginMutation, useLogoutMutation, useUserInfo, useRegisterMutation } from '@/hooks/useAuth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    // 💡 Tek doğruluk kaynağımız artık useUserInfo hook'u.
    const { data: user, isLoading, isSuccess } = useUserInfo();

    // Zustand'dan sadece action'ları ve error state'ini alabiliriz.
    const { error, clearError } = useAuthStore();

    // Login ve Logout için TanStack Query mutation'larını kullanıyoruz.
    const loginMutation = useLoginMutation();
    const logoutMutation = useLogoutMutation();
    const registerMutation = useRegisterMutation();

    // 🚨 Yeni Eklenen Kısım: Force Logout Event Listener'ı
    // api.ts'deki interceptor refresh token'da başarısız olursa bu event fırlatılır.
    useEffect(() => {
        const handleForceLogout = () => {
            // Zaten bir logout işlemi devam etmiyorsa logout yap.
            if (!logoutMutation.isPending) {
                logoutMutation.mutate();
            }
        };

        window.addEventListener('auth:force-logout', handleForceLogout);
        return () => {
            window.removeEventListener('auth:force-logout', handleForceLogout);
        };
    }, [logoutMutation]);

    const login = async (credentials: LoginCredentials): Promise<boolean> => {
        try {
            await loginMutation.mutateAsync(credentials);
            // Başarılı olduğunda, useUserInfo otomatik olarak yeniden tetiklenecek ve user state'i güncellenecek.
            return true;
        } catch (error) {
            console.error('❌ AuthContext login error:', error);
            // Hata yönetimi zaten useLoginMutation içinde yapılıyor.
            return false;
        }
    };

    const register = async (data: RegisterData): Promise<boolean> => {
        try {
            await registerMutation.mutateAsync(data);
            return true;
        } catch (error) {
            console.error('❌ AuthContext register error:', error);
            return false;
        }
    };

    const logout = async (): Promise<void> => {
        await logoutMutation.mutateAsync();
        // Başarılı olduğunda, useUserInfo query'si devre dışı kalacak ve cache temizlenecek.
    };

    // Bu fonksiyon artık mock olduğu için ve konumuz dışında olduğu için şimdilik dokunmuyoruz.
    const updateUserRole = async (
        userId: number,
        newRole: 'admin' | 'dealership' | 'customer',
        isDealershipApproved?: boolean,
        applicationStatus?: 'approved' | 'rejected'
    ): Promise<void> => {
        console.log('User role update requested:', { userId, newRole, isDealershipApproved, applicationStatus });
    };

    const contextValue: AuthContextType = {
        // `isSuccess` user'ın başarılı bir şekilde yüklendiğini gösterir.
        // Bu, `isAuthenticated`'in yerini alır.
        isAuthenticated: isSuccess && !!user,
        user: user || null,
        login,
        register,
        logout,
        updateUserRole,
        isLoading: loginMutation.isPending || registerMutation.isPending || logoutMutation.isPending || isLoading,
        error,
    };

    return (
        <AuthContext.Provider value={contextValue}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}; 