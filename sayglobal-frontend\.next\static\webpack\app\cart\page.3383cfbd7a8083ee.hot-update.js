"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cart/page",{

/***/ "(app-pages-browser)/./src/hooks/useCart.ts":
/*!******************************!*\
  !*** ./src/hooks/useCart.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDiscountedPrice: () => (/* binding */ calculateDiscountedPrice),\n/* harmony export */   calculatePoints: () => (/* binding */ calculatePoints),\n/* harmony export */   useCartItems: () => (/* binding */ useCartItems),\n/* harmony export */   useDiscountRate: () => (/* binding */ useDiscountRate),\n/* harmony export */   useRemoveFromCart: () => (/* binding */ useRemoveFromCart),\n/* harmony export */   useUpdateCartQuantity: () => (/* binding */ useUpdateCartQuantity),\n/* harmony export */   useUpdateCartType: () => (/* binding */ useUpdateCartType)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n\n\n// Sepet içeriklerini getir\nconst useCartItems = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            'cartItems'\n        ],\n        queryFn: {\n            \"useCartItems.useQuery\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.getCartItems();\n                if (response.success) {\n                    return response.data.data; // API response'u data wrapper'ı içinde geliyor\n                }\n                throw new Error(response.error || 'Sepet içerikleri alınamadı');\n            }\n        }[\"useCartItems.useQuery\"],\n        staleTime: 30 * 1000,\n        refetchOnWindowFocus: true,\n        refetchOnMount: true\n    });\n};\n// İndirim oranını getir\nconst useDiscountRate = ()=>{\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            'discountRate'\n        ],\n        queryFn: {\n            \"useDiscountRate.useQuery\": async ()=>{\n                try {\n                    const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.getDiscountRate();\n                    if (response.success) {\n                        return response.data.data || {\n                            discountRate: 0\n                        }; // API response'u data wrapper'ı içinde geliyor\n                    }\n                    // Hata durumunda 0 döndür, throw etme\n                    console.warn('İndirim oranı alınamadı:', response.error);\n                    return {\n                        discountRate: 0\n                    };\n                } catch (error) {\n                    // Network hatası vs. durumunda da 0 döndür\n                    console.warn('İndirim oranı alınırken hata:', error);\n                    return {\n                        discountRate: 0\n                    };\n                }\n            }\n        }[\"useDiscountRate.useQuery\"],\n        staleTime: 5 * 60 * 1000,\n        refetchOnWindowFocus: false,\n        refetchOnMount: true,\n        retry: false\n    });\n};\n// Sepetten ürün çıkarma mutation'ı\nconst useRemoveFromCart = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: {\n            \"useRemoveFromCart.useMutation\": async (productVariantId)=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.removeFromCart(productVariantId);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün sepetten çıkarılamadı');\n                }\n                return response.data;\n            }\n        }[\"useRemoveFromCart.useMutation\"],\n        onSuccess: {\n            \"useRemoveFromCart.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n            }\n        }[\"useRemoveFromCart.useMutation\"],\n        onError: {\n            \"useRemoveFromCart.useMutation\": (error)=>{\n                console.error('Sepetten ürün çıkarma hatası:', error);\n            }\n        }[\"useRemoveFromCart.useMutation\"]\n    });\n};\n// Sepet ürün miktarını güncelleme mutation'ı\nconst useUpdateCartQuantity = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: {\n            \"useUpdateCartQuantity.useMutation\": async (param)=>{\n                let { productVariantId, quantity } = param;\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.cartService.updateCartQuantity(productVariantId, quantity);\n                if (!response.success) {\n                    throw new Error(response.error || 'Ürün miktarı güncellenemedi');\n                }\n                return response.data;\n            }\n        }[\"useUpdateCartQuantity.useMutation\"],\n        onSuccess: {\n            \"useUpdateCartQuantity.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n            }\n        }[\"useUpdateCartQuantity.useMutation\"],\n        onError: {\n            \"useUpdateCartQuantity.useMutation\": (error)=>{\n                console.error('Sepet ürün miktarı güncelleme hatası:', error);\n            }\n        }[\"useUpdateCartQuantity.useMutation\"]\n    });\n};\n// Sepet tipini güncelleme mutation'ı (customer price toggle)\nconst useUpdateCartType = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: {\n            \"useUpdateCartType.useMutation\": async ()=>{\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__.userService.updateCartType();\n                if (!response.success) {\n                    throw new Error(response.error || 'Sepet tipi güncellenemedi');\n                }\n                return response.data;\n            }\n        }[\"useUpdateCartType.useMutation\"],\n        onSuccess: {\n            \"useUpdateCartType.useMutation\": ()=>{\n                // Sepet verilerini yenile\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'cartItems'\n                    ]\n                });\n            }\n        }[\"useUpdateCartType.useMutation\"],\n        onError: {\n            \"useUpdateCartType.useMutation\": (error)=>{\n                console.error('Sepet tipi güncelleme hatası:', error);\n            }\n        }[\"useUpdateCartType.useMutation\"]\n    });\n};\n// Puan hesaplama fonksiyonu\nconst calculatePoints = (ratio, price)=>{\n    return Math.round(ratio / 100 * price);\n};\n// Fiyat hesaplama fonksiyonu (indirim dahil)\nconst calculateDiscountedPrice = (originalPrice, discountRate, isCustomerPrice)=>{\n    if (isCustomerPrice || !discountRate || discountRate <= 0) {\n        return originalPrice;\n    }\n    return originalPrice * (1 - discountRate / 100);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useCart.ts\n"));

/***/ })

});