import { DistributorEarning, TeamMember, DistributorDashboard, TeamTreeNode, TeamTreeStats } from '../../types';

// Distribütör için kazanç verileri
export const distributorEarnings: DistributorEarning[] = [
    {
        id: 1,
        distributorId: 2,
        date: '2023-04-15',
        reference: '<PERSON><PERSON><PERSON>',
        points: 120,
        amount: 240.50,
        level: 1,
        percentage: 8
    },
    {
        id: 2,
        distributorId: 2,
        date: '2023-04-20',
        reference: '<PERSON><PERSON>',
        points: 85,
        amount: 170.00,
        level: 2,
        percentage: 5
    },
    {
        id: 3,
        distributorId: 2,
        date: '2023-04-28',
        reference: 'Arda <PERSON>',
        points: 150,
        amount: 300.75,
        level: 1,
        percentage: 8
    },
    {
        id: 4,
        distributorId: 2,
        date: '2023-05-05',
        reference: 'Burak Öztürk',
        points: 60,
        amount: 120.25,
        level: 3,
        percentage: 3
    },
    {
        id: 5,
        distributorId: 2,
        date: '2023-05-12',
        reference: '<PERSON><PERSON> Kara',
        points: 200,
        amount: 400.00,
        level: 1,
        percentage: 8
    }
];

// Distribütörün ekip üyeleri
export const teamMembers: TeamMember[] = [
    {
        id: 101,
        firstName: 'Aylin',
        lastName: 'Şahin',
        level: 1,
        joinDate: '2023-02-10',
        points: 450,
        isActive: true
    },
    {
        id: 102,
        firstName: 'Emre',
        lastName: 'Kılıç',
        level: 2,
        joinDate: '2023-02-15',
        points: 320,
        isActive: true
    },
    {
        id: 103,
        firstName: 'Arda',
        lastName: 'Altun',
        level: 1,
        joinDate: '2023-03-01',
        points: 580,
        isActive: true
    },
    {
        id: 104,
        firstName: 'Burak',
        lastName: 'Öztürk',
        level: 3,
        joinDate: '2023-03-12',
        points: 150,
        isActive: false
    },
    {
        id: 105,
        firstName: 'Selin',
        lastName: 'Kara',
        level: 1,
        joinDate: '2023-03-25',
        points: 650,
        isActive: true
    },
    {
        id: 106,
        firstName: 'Murat',
        lastName: 'Aydın',
        level: 2,
        joinDate: '2023-04-05',
        points: 280,
        isActive: true
    },
    {
        id: 107,
        firstName: 'Elif',
        lastName: 'Çelik',
        level: 3,
        joinDate: '2023-04-18',
        points: 120,
        isActive: false
    }
];

// Distribütör panosu için özet veriler
export const distributorDashboard: DistributorDashboard = {
    totalEarnings: 4250.75,
    monthlyPoints: 350,
    monthlyActivityPercentage: 75,
    teamSize: teamMembers.length,
    monthlyBalance: 1230.50,
    totalBalance: 4250.75,
    totalPoints: 2150,
    organizationPoints: 5680,
    monthlyEarnings: [
        { month: 'Ocak', earnings: 850.25, activity: 65 },
        { month: 'Şubat', earnings: 920.50, activity: 70 },
        { month: 'Mart', earnings: 1050.75, activity: 80 },
        { month: 'Nisan', earnings: 1230.50, activity: 75 },
        { month: 'Mayıs', earnings: 980.25, activity: 72 }
    ],
    monthlyPointsHistory: [
        { month: 'Ocak', points: 280, target: 300 },
        { month: 'Şubat', points: 320, target: 300 },
        { month: 'Mart', points: 390, target: 350 },
        { month: 'Nisan', points: 420, target: 350 },
        { month: 'Mayıs', points: 350, target: 400 },
        { month: 'Haziran', points: 310, target: 400 }
    ],
    monthlyActivityTrend: [
        { month: 'Ocak', activityPercentage: 65, teamSize: 4, newMembers: 1 },
        { month: 'Şubat', activityPercentage: 70, teamSize: 5, newMembers: 1 },
        { month: 'Mart', activityPercentage: 80, teamSize: 6, newMembers: 1 },
        { month: 'Nisan', activityPercentage: 75, teamSize: 6, newMembers: 0 },
        { month: 'Mayıs', activityPercentage: 72, teamSize: 7, newMembers: 1 },
        { month: 'Haziran', activityPercentage: 78, teamSize: 7, newMembers: 0 }
    ],
    nextLevelPoints: 500, // Bir sonraki seviyeye ulaşmak için gereken puan
    currentLevel: 'Gümüş', // Mevcut seviye
    nextLevel: 'Altın' // Bir sonraki seviye
};

// Ekip ağacı için binary tree yapısı
export const teamTreeData: TeamTreeNode = {
    id: 1,
    name: 'Distribütör (Sen)',
    level: 3,
    points: 2150,
    joinDate: '2023-01-01',
    isActive: true,
    totalEarnings: 4250.75,
    monthlyPoints: 350,
    children: {
        left: {
            id: 101,
            name: 'Aylin Şahin',
            level: 1,
            points: 450,
            joinDate: '2023-02-10',
            isActive: true,
            parentId: 1,
            position: 'left',
            totalEarnings: 1200.50,
            monthlyPoints: 120,
            children: {
                left: {
                    id: 103,
                    name: 'Arda Altun',
                    level: 1,
                    points: 580,
                    joinDate: '2023-03-01',
                    isActive: true,
                    parentId: 101,
                    position: 'left',
                    totalEarnings: 850.25,
                    monthlyPoints: 95,
                    children: {
                        left: {
                            id: 107,
                            name: 'Elif Çelik',
                            level: 3,
                            points: 120,
                            joinDate: '2023-04-18',
                            isActive: false,
                            parentId: 103,
                            position: 'left',
                            totalEarnings: 240.00,
                            monthlyPoints: 25
                        }
                    }
                },
                right: {
                    id: 105,
                    name: 'Selin Kara',
                    level: 1,
                    points: 650,
                    joinDate: '2023-03-25',
                    isActive: true,
                    parentId: 101,
                    position: 'right',
                    totalEarnings: 980.75,
                    monthlyPoints: 135
                }
            }
        },
        right: {
            id: 102,
            name: 'Emre Kılıç',
            level: 2,
            points: 320,
            joinDate: '2023-02-15',
            isActive: true,
            parentId: 1,
            position: 'right',
            totalEarnings: 750.25,
            monthlyPoints: 85,
            children: {
                left: {
                    id: 104,
                    name: 'Burak Öztürk',
                    level: 3,
                    points: 150,
                    joinDate: '2023-03-12',
                    isActive: false,
                    parentId: 102,
                    position: 'left',
                    totalEarnings: 320.00,
                    monthlyPoints: 40
                },
                right: {
                    id: 106,
                    name: 'Murat Aydın',
                    level: 2,
                    points: 280,
                    joinDate: '2023-04-05',
                    isActive: true,
                    parentId: 102,
                    position: 'right',
                    totalEarnings: 480.50,
                    monthlyPoints: 65
                }
            }
        }
    }
};

// Ekip ağacı istatistikleri
export const teamTreeStats: TeamTreeStats = {
    totalMembers: 7,
    activeMembers: 5,
    totalLevels: 3,
    totalPoints: 2550,
    totalEarnings: 8950.00,
    monthlyGrowth: 12.5
}; 