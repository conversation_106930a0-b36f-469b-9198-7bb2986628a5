'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useAuth } from '@/components/auth/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { MembershipLevelIds, AuthUser } from '@/types';
// 💡 useUserInfo ve useLogoutMutation artık doğrudan çağrılmıyor, useAuth içinden geliyor.

export default function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isSearchOpen, setIsSearchOpen] = useState(false);
    const [isUserMenuOpen, setUserMenuOpen] = useState(false);

    // 💡 YENİ ve TEMİZ Auth State'i
    // Artık tüm auth bilgileri tek bir yerden geliyor.
    const { user, isLoading, isAuthenticated, logout } = useAuth();
    const { getTotalItems } = useCart();

    // Refs for click outside functionality
    const searchRef = useRef<HTMLDivElement>(null);
    const userMenuRef = useRef<HTMLDivElement>(null);

    // İngilizce ve Türkçe menü eşleştirmeleri
    const menuItems = [
        { text: 'Anasayfa', route: '/' },
        { text: 'Ürünler', route: '/products' },
        { text: 'Duyurular', route: '/announcements' }
    ];

    // Click outside to close functionality (only for desktop, not mobile)
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            // Only run on desktop (not mobile) and when mobile menu is closed
            if (window.innerWidth >= 768 && !isMenuOpen) {
                // Close search menu if clicked outside
                if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
                    setIsSearchOpen(false);
                }
                // Close user menu if clicked outside
                if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                    setUserMenuOpen(false);
                }
            }
        }

        // Only add event listener when either menu is open and we're on desktop
        if ((isSearchOpen || isUserMenuOpen) && !isMenuOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => {
                document.removeEventListener('mousedown', handleClickOutside);
            };
        }
    }, [isSearchOpen, isUserMenuOpen, isMenuOpen]);

    return (
        <motion.header
            className="bg-white shadow-md sticky top-0 z-50 "
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            transition={{ type: "spring", stiffness: 100, damping: 20 }}
        >
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-between h-20">
                    {/* Logo */}
                    <Link href="/" className="flex items-center">
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            transition={{ type: "spring", stiffness: 400, damping: 17 }}
                            className="flex items-center"
                        >
                            <Image
                                src="/assets/sayglobal_logo.png"
                                alt="Say Global Logo"
                                width={180}
                                height={72}
                                className="h-16 w-auto object-contain"
                                priority
                            />
                            <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 text-transparent bg-clip-text hidden sm:block">
                                Say Global
                            </span>
                        </motion.div>
                    </Link>

                    {/* Mobil Menü Butonu */}
                    <motion.button
                        type="button"
                        className="md:hidden text-gray-600 hover:text-gray-900 focus:outline-none"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                        whileTap={{ scale: 0.9 }}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            {isMenuOpen ? (
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            ) : (
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 6h16M4 12h16M4 18h16"
                                />
                            )}
                        </svg>
                    </motion.button>

                    {/* Masaüstü Navigasyon */}
                    <nav className="hidden md:flex items-center space-x-8">
                        {menuItems.map((item, index) => (
                            <motion.div
                                key={index}
                                whileHover={{ y: -2 }}
                                whileTap={{ y: 0 }}
                            >
                                <Link
                                    href={item.route}
                                    className="text-gray-700 hover:text-purple-600 font-medium relative overflow-hidden group"
                                >
                                    {item.text}
                                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-purple-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"></span>
                                </Link>
                            </motion.div>
                        ))}

                        <div className="relative" ref={searchRef}>
                            <motion.button
                                className="text-gray-700 hover:text-purple-600 font-medium focus:outline-none p-2 rounded-full hover:bg-gray-100"
                                onClick={() => setIsSearchOpen(!isSearchOpen)}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-5 w-5"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    />
                                </svg>
                            </motion.button>
                            {isSearchOpen && (
                                <motion.div
                                    className="absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-xl p-3 border border-gray-100"
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: 10 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <div className="flex items-center bg-gray-50 rounded-md">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-5 w-5 text-gray-400 ml-3"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                            />
                                        </svg>
                                        <input
                                            type="text"
                                            placeholder="Ürün ara..."
                                            className="w-full px-3 py-2 text-black bg-transparent border-none focus:outline-none focus:ring-0"
                                        />
                                    </div>
                                </motion.div>
                            )}
                        </div>
                    </nav>

                    {/* Kullanıcı İşlemleri */}
                    <div className="hidden md:flex items-center space-x-4">
                        <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                            <Link href="/cart" className="text-gray-700 hover:text-purple-600 relative p-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                                    />
                                </svg>
                                <span className="absolute top-0 right-0 md:top-4 md:left-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                    {getTotalItems()}
                                </span>
                            </Link>
                        </motion.div>

                        <div className="relative" ref={userMenuRef}>
                            <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => setUserMenuOpen(!isUserMenuOpen)}
                                className="text-gray-700 hover:text-purple-600 p-2 flex items-center space-x-2"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                    />
                                </svg>
                                {isLoading ? (
                                    <div className="hidden md:block w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                                ) : isAuthenticated && user ? (
                                    <span className="hidden md:block text-sm font-medium">
                                        {user.firstName} {user.lastName}
                                    </span>
                                ) : null}
                            </motion.button>

                            {isUserMenuOpen && (
                                <motion.div
                                    className="absolute right-0 mt-2 py-2 w-56 bg-white rounded-lg shadow-xl z-20 border border-gray-100"
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: 10 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    {isAuthenticated && user ? (
                                        <>
                                            <div className="px-4 py-2 border-b border-gray-100">
                                                <p className="text-sm font-medium text-gray-900">
                                                    {user.firstName} {user.lastName}
                                                </p>
                                                <p className="text-xs text-gray-500">{user.email}</p>
                                                <span className={`inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' :
                                                    user.role === 'dealership' ? 'bg-green-100 text-green-800' :
                                                        'bg-blue-100 text-blue-800'
                                                    }`}>
                                                    {user.role === 'admin' ? 'Yönetici' :
                                                        user.role === 'dealership' ? 'Satıcı' : 'Müşteri'}
                                                </span>
                                            </div>
                                            <Link
                                                href="/account"
                                                className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600"
                                                onClick={() => setUserMenuOpen(false)}
                                            >
                                                Hesabım
                                            </Link>
                                            <Link
                                                href="/account?tab=orders"
                                                className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600"
                                                onClick={() => setUserMenuOpen(false)}
                                            >
                                                Siparişlerim
                                            </Link>
                                            <Link
                                                href="/account?tab=favorites"
                                                className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600"
                                                onClick={() => setUserMenuOpen(false)}
                                            >
                                                Favorilerim
                                            </Link>
                                            {/* Distribütör Paneli - customer, dealership ve admin rolleri için */}
                                            {(user.role === 'customer' || user.role === 'dealership' || user.role === 'admin') && (
                                                <Link
                                                    href="/panel"
                                                    className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600"
                                                    onClick={() => setUserMenuOpen(false)}
                                                >
                                                    Kontrol Paneli
                                                </Link>
                                            )}
                                            {user.role === 'admin' && (
                                                <Link
                                                    href="/admin"
                                                    className="block px-4 py-2 text-gray-700 hover:bg-red-50 hover:text-red-600"
                                                    onClick={() => setUserMenuOpen(false)}
                                                >
                                                    Yönetici Paneli
                                                </Link>
                                            )}
                                            <div className="border-t border-gray-100 my-2"></div>
                                            <button
                                                onClick={() => {
                                                    logout();
                                                    setIsMenuOpen(false);
                                                    setUserMenuOpen(false);
                                                }}
                                                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 rounded-md"
                                            >
                                                Çıkış Yap
                                            </button>
                                        </>
                                    ) : (
                                        <div className="px-2 py-1 space-y-2">
                                            <Link
                                                href="/login"
                                                className="block w-full text-center rounded-md bg-gradient-to-r from-purple-600 to-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-300 hover:from-purple-700 hover:to-indigo-700"
                                                onClick={() => setUserMenuOpen(false)}
                                            >
                                                Giriş Yap
                                            </Link>
                                            <Link
                                                href="/register"
                                                className="block w-full text-center rounded-md bg-white px-4 py-2 text-sm font-medium text-gray-800 border border-gray-300 transition-all duration-300 hover:bg-gray-100"
                                                onClick={() => setUserMenuOpen(false)}
                                            >
                                                Kayıt Ol
                                            </Link>
                                        </div>
                                    )}
                                </motion.div>
                            )}
                        </div>

                        {/* Satıcı Ol Butonu - sadece customer rolündeki kullanıcılar için */}
                        {isAuthenticated && user && user.role === 'customer' && !user.isDealershipApproved && (
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: "spring", stiffness: 400, damping: 17 }}
                            >
                                <Link
                                    href="/become-dealer"
                                    className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 flex items-center space-x-1"
                                >
                                    <span>Satıcı Ol</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-4 w-4"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                        />
                                    </svg>
                                </Link>
                            </motion.div>
                        )}

                        {isAuthenticated && user && user.role === 'admin' && (
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: "spring", stiffness: 400, damping: 17 }}
                            >
                                <Link
                                    href="/admin"
                                    className="bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 flex items-center space-x-1"
                                >
                                    <span>Yönetici Paneli</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-4 w-4"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </Link>
                            </motion.div>
                        )}
                    </div>
                </div>

                {/* Mobil Menü */}
                {isMenuOpen && (
                    <motion.div
                        className="md:hidden pb-4"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <div className="flex flex-col space-y-3">
                            <Link
                                href="/"
                                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Anasayfa
                            </Link>
                            <Link
                                href="/products"
                                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Ürünler
                            </Link>
                            <Link
                                href="/announcements"
                                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Duyurular
                            </Link>
                            <div className="py-2">
                                <div className="flex items-center bg-gray-50 rounded-md">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 text-gray-400 ml-3"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                        />
                                    </svg>
                                    <input
                                        type="text"
                                        placeholder="Ürün ara..."
                                        className="w-full px-3 py-2 bg-transparent border-none focus:outline-none focus:ring-0 text-black"
                                    />
                                </div>
                            </div>
                            <div className="flex items-center justify-between py-2">
                                <Link
                                    href="/cart"
                                    className="text-gray-700 hover:text-purple-600 relative p-3 bg-gray-50 rounded-lg flex items-center justify-center"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-7 w-7"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                                        />
                                    </svg>
                                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-medium shadow-lg">
                                        {getTotalItems()}
                                    </span>
                                </Link>
                                <button
                                    onClick={() => setUserMenuOpen(!isUserMenuOpen)}
                                    className="text-gray-700 hover:text-purple-600 flex items-center space-x-3 p-2 bg-gray-50 rounded-lg flex-1 ml-4"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-6 w-6"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                        />
                                    </svg>
                                    {isLoading ? (
                                        <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                                    ) : isAuthenticated && user ? (
                                        <span className="text-sm font-medium truncate">{user.firstName} {user.lastName}</span>
                                    ) : (
                                        <span className="text-sm font-medium">Giriş Yap</span>
                                    )}
                                </button>
                            </div>

                            {/* Mobil Kullanıcı Menüsü */}
                            {isUserMenuOpen && (
                                <motion.div
                                    className="bg-gray-50 rounded-lg p-4 mt-3"
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: "auto" }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    {isAuthenticated && user ? (
                                        <>
                                            <div className="border-b border-gray-200 pb-3 mb-3">
                                                <p className="text-sm font-medium text-gray-900">{user.firstName} {user.lastName}</p>
                                                <p className="text-xs text-gray-500">{user.email}</p>
                                                <span className={`inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' :
                                                    user.role === 'dealership' ? 'bg-green-100 text-green-800' :
                                                        'bg-blue-100 text-blue-800'
                                                    }`}>
                                                    {user.role === 'admin' ? 'Yönetici' :
                                                        user.role === 'dealership' ? 'Satıcı' : 'Müşteri'}
                                                </span>
                                            </div>
                                            <div className="space-y-2">
                                                <Link
                                                    href="/account"
                                                    className="block py-2 text-gray-700 hover:text-purple-600 font-medium"
                                                    onClick={() => {
                                                        setIsMenuOpen(false);
                                                        setUserMenuOpen(false);
                                                    }}
                                                >
                                                    Hesabım
                                                </Link>
                                                <Link
                                                    href="/account?tab=orders"
                                                    className="block py-2 text-gray-700 hover:text-purple-600 font-medium"
                                                    onClick={() => {
                                                        setIsMenuOpen(false);
                                                        setUserMenuOpen(false);
                                                    }}
                                                >
                                                    Siparişlerim
                                                </Link>
                                                <Link
                                                    href="/account?tab=favorites"
                                                    className="block py-2 text-gray-700 hover:text-purple-600 font-medium"
                                                    onClick={() => {
                                                        setIsMenuOpen(false);
                                                        setUserMenuOpen(false);
                                                    }}
                                                >
                                                    Favorilerim
                                                </Link>
                                                {(user.role === 'customer' || user.role === 'dealership' || user.role === 'admin') && (
                                                    <Link
                                                        href="/panel"
                                                        className="block py-2 text-gray-700 hover:text-purple-600 font-medium"
                                                        onClick={() => {
                                                            setIsMenuOpen(false);
                                                            setUserMenuOpen(false);
                                                        }}
                                                    >
                                                        Kontrol Paneli
                                                    </Link>
                                                )}
                                                {user.role === 'admin' && (
                                                    <Link
                                                        href="/admin"
                                                        className="block py-2 text-gray-700 hover:text-red-600 font-medium"
                                                        onClick={() => {
                                                            setIsMenuOpen(false);
                                                            setUserMenuOpen(false);
                                                        }}
                                                    >
                                                        Yönetici Paneli
                                                    </Link>
                                                )}
                                                <button
                                                    onClick={() => {
                                                        logout();
                                                        setIsMenuOpen(false);
                                                        setUserMenuOpen(false);
                                                    }}
                                                    className="w-full text-left py-2 text-red-600 hover:text-red-700 font-medium"
                                                >
                                                    Çıkış Yap
                                                </button>
                                            </div>
                                        </>
                                    ) : (
                                        <div className="space-y-2">
                                            <Link
                                                href="/login"
                                                className="block w-full rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 px-4 py-3 text-center text-base font-semibold text-white shadow-md transition-all duration-300 hover:from-purple-700 hover:to-indigo-700"
                                                onClick={() => {
                                                    setIsMenuOpen(false);
                                                    setUserMenuOpen(false);
                                                }}
                                            >
                                                Giriş Yap
                                            </Link>
                                            <Link
                                                href="/register"
                                                className="block w-full rounded-lg bg-white px-4 py-3 text-center text-base font-semibold text-gray-800 border border-gray-300 transition-all duration-300 hover:bg-gray-100"
                                                onClick={() => {
                                                    setIsMenuOpen(false);
                                                    setUserMenuOpen(false);
                                                }}
                                            >
                                                Kayıt Ol
                                            </Link>
                                        </div>
                                    )}
                                </motion.div>
                            )}
                            {/* Satıcı Ol Butonu - Mobil */}
                            {isAuthenticated && user && user.role === 'customer' && !user.isDealershipApproved && (
                                <Link
                                    href="/become-dealer"
                                    className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 text-center"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Satıcı Ol
                                </Link>
                            )}

                            {isAuthenticated && user && user.role === 'admin' && (
                                <Link
                                    href="/admin"
                                    className="bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow duration-300 text-center"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Yönetici Paneli
                                </Link>
                            )}
                        </div>
                    </motion.div>
                )}
            </div>
        </motion.header>
    );
} 