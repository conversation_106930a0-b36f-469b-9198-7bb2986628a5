# Say Global API Dokümantasyonu

Bu dokümantasyon, Say Global platformu için mevcut ve olması gereken tüm API endpoint'lerini içermektedir.

## Base URL
- **Development**: `http://localhost:3000`
- **Production**: `https://sayglobalweb.com`

## Authentication
API'ler JWT Bearer token kullanır:
```
Authorization: Bearer {token}
```

---

## 🔐 AUTHENTICATION & ACCOUNT APIs

### Mevcut APIs (Swagger'da mevcut)

#### POST /api/Account/register
Yeni kullanıcı kaydı
```json
{
  "email": "string",
  "password": "string", 
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string",
  "referansCode": "string"
}
```

#### POST /api/Account/login
Kullanıcı girişi
```json
{
  "email": "string",
  "password": "string"
}
```

#### POST /api/Account/refresh
Token yenileme
```json
"refreshToken"
```

#### POST /api/Account/logout
<PERSON> çıkışı
```json
{
  "refreshToken": "string"
}
```

#### POST /api/Account/make-admin
Kullanıcıyı admin yap
```json
"userIdOrEmail"
```

#### GET /api/Account/test
Test endpoint

#### GET /api/Account/debug-claims
Debug claims bilgileri

#### POST /api/Account/account/add-reference
Referans kodu ekleme
```json
{
  "referansCode": "string"
}
```

### 🚀 Eksik APIs (Eklenmesi Gerekiyor)

#### GET /api/account/profile
Kullanıcı profil bilgilerini getir
```json
Response: {
  "id": 1,
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "phoneNumber": "string",
  "membershipLevel": 0,
  "careerRank": 0,
  "joinDate": "string",
  "isActive": true,
  "totalOrders": 0,
  "totalSpent": 0,
  "totalPoints": 0
}
```

#### PUT /api/account/profile
Profil bilgilerini güncelle
```json
{
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string"
}
```

#### POST /api/account/change-password
Şifre değiştirme
```json
{
  "currentPassword": "string",
  "newPassword": "string"
}
```

#### POST /api/account/forgot-password
Şifre sıfırlama isteği
```json
{
  "email": "string"
}
```

#### POST /api/account/reset-password
Şifre sıfırlama
```json
{
  "token": "string",
  "newPassword": "string"
}
```

#### GET /api/account/addresses
Kullanıcı adreslerini getir
```json
Response: [{
  "id": 1,
  "title": "string",
  "addressLine": "string",
  "city": "string",
  "state": "string", 
  "postalCode": "string",
  "isDefault": true
}]
```

#### POST /api/account/addresses
Yeni adres ekleme
```json
{
  "title": "string",
  "addressLine": "string",
  "city": "string",
  "state": "string",
  "postalCode": "string",
  "isDefault": false
}
```

#### PUT /api/account/addresses/{id}
Adres güncelleme

#### DELETE /api/account/addresses/{id}
Adres silme

---

## 📦 PRODUCTS APIs

### Mevcut APIs (Swagger'da mevcut)

#### POST /api/Products/addproduct
Yeni ürün ekleme
```json
{
  "name": "string",
  "brandId": 0,
  "categoryId": 0,
  "stock": 0,
  "description": "string",
  "featureValueIds": [0]
}
```

#### POST /api/Products/addimage
Ürün resmi ekleme (multipart/form-data)
```
ProductId: integer
IsMain: boolean
SortOrder: integer
File: binary
```

#### DELETE /api/Products/image/{imageId}
Ürün resmi silme

#### PUT /api/Products/image/replace/{imageId}
Ürün resmi değiştirme

#### POST /api/Products/productstatus
Ürün durumu değiştirme
```json
{
  "productId": 0,
  "isApproved": true
}
```

#### POST /api/Products/createbrand
Marka oluşturma
```json
{
  "name": "string",
  "logoUrl": "string",
  "categoryIds": [0]
}
```

#### POST /api/Products/createcategory
Kategori oluşturma
```json
{
  "name": "string",
  "subCategoryNames": ["string"],
  "featureIds": [0]
}
```

#### POST /api/Products/createdefinition
Özellik tanımı oluşturma
```json
{
  "name": "string",
  "description": "string",
  "initialValues": ["string"]
}
```

#### POST /api/Products/createfeature
Kategori özelliği oluşturma
```json
{
  "categoryId": 0,
  "featureDefinitionId": 0
}
```

#### POST /api/Products/createvalue
Özellik değeri oluşturma
```json
{
  "featureDefinitionId": 0,
  "value": "string"
}
```

#### POST /api/Products/createproductvalue
Ürün özellik değeri oluşturma
```json
{
  "productId": 0,
  "featureValueId": 0
}
```

### 🚀 Eksik APIs (Eklenmesi Gerekiyor)

#### GET /api/products
Ürün listesi (filtreleme ve sayfalama ile)
```
Query Params:
- page: integer (default: 1)
- limit: integer (default: 20)
- category: string
- brand: string
- minPrice: number
- maxPrice: number
- search: string
- sortBy: string (featured, price-asc, price-desc, rating)
```

#### GET /api/products/{id}
Tek ürün detayı
```json
Response: {
  "id": 1,
  "title": "string",
  "description": "string",
  "price": 0,
  "discountPercentage": 0,
  "rating": 0,
  "stock": 0,
  "brand": "string",
  "category": "string",
  "images": ["string"],
  "features": [],
  "points": 0
}
```

#### PUT /api/products/{id}
Ürün güncelleme

#### DELETE /api/products/{id}
Ürün silme

#### GET /api/products/search
Ürün arama
```
Query: q=search_term
```

#### GET /api/products/categories
Kategori listesi
```json
Response: [{
  "id": 1,
  "name": "string",
  "slug": "string",
  "image": "string",
  "description": "string"
}]
```

#### GET /api/products/brands
Marka listesi
```json
Response: [{
  "id": 1,
  "name": "string",
  "logoUrl": "string"
}]
```

#### GET /api/products/pending
Onay bekleyen ürünler
```json
Response: [{
  "id": 1,
  "title": "string",
  "distributorName": "string",
  "submittedAt": "string",
  "status": "pending"
}]
```

---

## 🛒 CART APIs (Tümü Eksik)

#### GET /api/cart
Kullanıcı sepetini getir
```json
Response: {
  "id": 1,
  "userId": 1,
  "items": [{
    "id": 1,
    "productId": 1,
    "quantity": 2,
    "price": 100.00,
    "product": {}
  }],
  "total": 200.00
}
```

#### POST /api/cart/add
Sepete ürün ekleme
```json
{
  "productId": 1,
  "quantity": 1
}
```

#### PUT /api/cart/update
Sepet güncelleme
```json
{
  "productId": 1,
  "quantity": 3
}
```

#### DELETE /api/cart/remove/{productId}
Sepetten ürün çıkarma

#### DELETE /api/cart/clear
Sepeti temizleme

---

## 📋 ORDERS APIs (Tümü Eksik)

#### GET /api/orders
Kullanıcı siparişlerini getir
```json
Response: [{
  "id": "ORD-2024-1001",
  "date": "string",
  "status": "delivered",
  "total": 1245.00,
  "itemCount": 3,
  "trackingNumber": "string"
}]
```

#### GET /api/orders/{id}
Sipariş detayı
```json
Response: {
  "id": "ORD-2024-1001",
  "orderDate": "string",
  "status": "delivered",
  "items": [],
  "shippingInfo": {},
  "paymentInfo": {},
  "timeline": []
}
```

#### POST /api/orders
Yeni sipariş oluşturma
```json
{
  "items": [{
    "productId": 1,
    "quantity": 2
  }],
  "shippingAddressId": 1,
  "paymentMethodId": 1
}
```

#### PUT /api/orders/{id}/status
Sipariş durumu güncelleme
```json
{
  "status": "shipped",
  "trackingNumber": "string"
}
```

#### POST /api/orders/{id}/cancel
Sipariş iptali

---

## ⭐ FAVORITES APIs (Tümü Eksik)

#### GET /api/favorites
Kullanıcı favorilerini getir
```json
Response: [{
  "id": 1,
  "productId": 1,
  "product": {},
  "addedAt": "string"
}]
```

#### POST /api/favorites
Favorilere ürün ekleme
```json
{
  "productId": 1
}
```

#### DELETE /api/favorites/{productId}
Favorilerden ürün çıkarma

---

## 🔧 ADMIN APIs

### Mevcut APIs (Swagger'da mevcut)

#### POST /api/admin/tools/create-roles
Rol oluşturma

### 🚀 Eksik APIs (Eklenmesi Gerekiyor)

#### GET /api/admin/stats
Admin panel istatistikleri
```json
Response: {
  "totalUsers": 1247,
  "totalOrders": 2856,
  "totalRevenue": 185420.75,
  "totalProducts": 156,
  "monthlyStats": []
}
```

#### GET /api/admin/users
Kullanıcı listesi
```json
Response: [{
  "id": 1,
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "role": "customer",
  "membershipLevel": 0,
  "isActive": true,
  "totalOrders": 0,
  "totalSpent": 0
}]
```

#### PUT /api/admin/users/{id}
Kullanıcı bilgilerini güncelleme

#### POST /api/admin/users/{id}/status
Kullanıcı durumunu değiştirme
```json
{
  "isActive": true
}
```

#### GET /api/admin/orders
Tüm siparişler (admin)

#### GET /api/admin/products
Tüm ürünler (admin)

#### GET /api/admin/reports
Raporlar

---

## 🏢 DEALERSHIP APIs (Tümü Eksik)

#### POST /api/dealership/apply
Satıcı başvurusu
```json
{
  "companyName": "string",
  "taxNumber": "string",
  "taxOffice": "string",
  "companyAddress": "string",
  "mainProductCategory": "string",
  "estimatedProductCount": "string"
}
```

#### GET /api/dealership/applications
Satıcı başvuruları (admin)
```json
Response: [{
  "id": 1,
  "userName": "string",
  "userEmail": "string", 
  "status": "pending",
  "submittedAt": "string"
}]
```

#### PUT /api/dealership/applications/{id}
Başvuru onayla/reddet
```json
{
  "status": "approved",
  "adminNotes": "string"
}
```

#### GET /api/dealership/my-products
Satıcının ürünleri

---

## 💰 DISTRIBUTOR/MLM APIs (Tümü Eksik)

#### GET /api/distributor/dashboard
Distribütör paneli verileri
```json
Response: {
  "totalEarnings": 5420.75,
  "monthlyPoints": 245,
  "teamSize": 12,
  "currentLevel": "Bronze",
  "nextLevel": "Silver",
  "monthlyEarnings": [],
  "teamTree": {}
}
```

#### GET /api/distributor/earnings
Kazanç geçmişi
```json
Response: [{
  "id": 1,
  "date": "string",
  "reference": "string",
  "points": 50,
  "amount": 125.00,
  "level": 1,
  "percentage": 10
}]
```

#### GET /api/distributor/team
Takım üyeleri
```json
Response: [{
  "id": 1,
  "firstName": "string",
  "lastName": "string",
  "level": 1,
  "joinDate": "string",
  "points": 150,
  "isActive": true
}]
```

#### GET /api/distributor/tree
Takım ağacı yapısı

#### POST /api/distributor/withdraw
Para çekme isteği
```json
{
  "amount": 500.00,
  "bankAccountId": 1
}
```

---

## 📢 ANNOUNCEMENTS APIs (Tümü Eksik)

#### GET /api/announcements
Duyuru listesi
```json
Response: [{
  "id": 1,
  "title": "string",
  "content": "string", 
  "date": "string",
  "isImportant": false
}]
```

#### POST /api/announcements
Duyuru oluşturma (admin)
```json
{
  "title": "string",
  "content": "string",
  "isImportant": false
}
```

#### PUT /api/announcements/{id}
Duyuru güncelleme

#### DELETE /api/announcements/{id}
Duyuru silme

---

## 💳 PAYMENT & BANKING APIs (Tümü Eksik)

#### GET /api/payment/cards
Kullanıcı kartları
```json
Response: [{
  "id": 1,
  "cardLast4": "1234",
  "cardType": "Visa",
  "expiryMonth": 12,
  "expiryYear": 2025,
  "isDefault": true
}]
```

#### POST /api/payment/cards
Kart ekleme
```json
{
  "cardNumber": "string",
  "expiryMonth": 12,
  "expiryYear": 2025,
  "cvv": "123",
  "cardHolderName": "string"
}
```

#### DELETE /api/payment/cards/{id}
Kart silme

#### PUT /api/payment/cards/{id}/default
Varsayılan kart ayarlama

#### POST /api/payment/process
Ödeme işleme
```json
{
  "orderId": "string",
  "cardId": 1,
  "amount": 250.00
}
```

#### GET /api/banking/accounts
Banka hesapları

#### POST /api/banking/accounts
Banka hesabı ekleme

---

## 📊 REPORTS & ANALYTICS APIs (Tümü Eksik)

#### GET /api/reports/sales
Satış raporları

#### GET /api/reports/users
Kullanıcı raporları

#### GET /api/reports/products
Ürün raporları

#### GET /api/analytics/dashboard
Analitik verileri

---

## ⚠️ ÖNCELİKLİ EKLENMESI GEREKEN APIs

1. **GET /api/account/profile** - Hesap sayfası için kritik
2. **GET /api/products** - Ürün listesi için kritik  
3. **GET /api/products/{id}** - Ürün detay sayfası için kritik
4. **GET /api/products/categories** - Kategori filtreleme için kritik
5. **Sepet APIs (tümü)** - E-ticaret için kritik
6. **Sipariş APIs (GET endpoints)** - Hesap geçmişi için kritik
7. **GET /api/admin/stats** - Admin paneli için kritik
8. **Favoriler APIs** - Kullanıcı deneyimi için önemli

## 📝 NOTLAR

- Tüm POST/PUT endpoints'leri request validation gerektirir
- Rate limiting uygulanmalı
- API response'ları standart format kullanmalı:
```json
{
  "success": true,
  "message": "string",
  "data": {},
  "errors": []
}
```
- Pagination için standart format:
```json
{
  "data": [],
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 200,
    "itemsPerPage": 20
  }
}
``` 