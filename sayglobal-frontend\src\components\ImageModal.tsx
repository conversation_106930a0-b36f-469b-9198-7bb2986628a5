'use client';

import { useEffect, useRef } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { useCatalogProductDetailStore, useSlideDirection } from '@/stores/catalogProductDetailStore';

interface ImageData {
    id: number;
    url: string;
    isMain: boolean;
    sortOrder: number;
}

interface ImageModalProps {
    isOpen: boolean;
    onClose: () => void;
    images: ImageData[];
    currentImageIndex: number;
    onImageChange: (index: number) => void;
    productName: string;
}

export default function ImageModal({
    isOpen,
    onClose,
    images,
    currentImageIndex,
    onImageChange,
    productName
}: ImageModalProps) {
    const modalRef = useRef<HTMLDivElement>(null);
    const slideDirection = useSlideDirection();

    // Klavye navigasyonu
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (!isOpen) return;

            switch (e.key) {
                case 'Escape':
                    onClose();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    goToPreviousImage();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    goToNextImage();
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen, currentImageIndex, images.length]);

    // Body scroll'u engelle
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    const goToPreviousImage = () => {
        if (images.length > 0) {
            const previousIndex = currentImageIndex > 0 ? currentImageIndex - 1 : images.length - 1;
            // Set direction first, then update image
            useCatalogProductDetailStore.setState({ slideDirection: -1 });
            setTimeout(() => {
                onImageChange(previousIndex);
            }, 0);
        }
    };

    const goToNextImage = () => {
        if (images.length > 0) {
            const nextIndex = currentImageIndex < images.length - 1 ? currentImageIndex + 1 : 0;
            // Set direction first, then update image
            useCatalogProductDetailStore.setState({ slideDirection: 1 });
            setTimeout(() => {
                onImageChange(nextIndex);
            }, 0);
        }
    };

    const handleBackdropClick = (e: React.MouseEvent) => {
        if (e.target === modalRef.current) {
            onClose();
        }
    };

    if (!isOpen || images.length === 0) return null;

    const currentImage = images[currentImageIndex];

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    ref={modalRef}
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    onClick={handleBackdropClick}
                >
                    {/* Close Button */}
                    <motion.button
                        className="absolute top-4 right-4 z-10 bg-white/10 hover:bg-white/20 text-white rounded-full p-2 transition-colors"
                        onClick={onClose}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <X className="h-6 w-6" />
                    </motion.button>

                    {/* Image Counter */}
                    <div className="absolute top-4 left-4 z-10 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                        {currentImageIndex + 1} / {images.length}
                    </div>

                    {/* Main Image Container */}
                    <div className="relative w-full h-full max-w-6xl max-h-[90vh] mx-4">
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={currentImageIndex}
                                className="relative w-full h-full"
                                initial={slideDirection !== 0 ? { x: slideDirection > 0 ? 300 : -300, opacity: 0 } : { opacity: 1 }}
                                animate={{ x: 0, opacity: 1 }}
                                exit={slideDirection !== 0 ? { x: slideDirection > 0 ? -300 : 300, opacity: 0 } : { opacity: 0 }}
                                transition={{
                                    type: "tween",
                                    ease: "easeOut",
                                    duration: slideDirection !== 0 ? 0.2 : 0,
                                    delay: slideDirection !== 0 ? 0.1 : 0
                                }}
                            >
                                <Image
                                    src={currentImage.url}
                                    alt={`${productName} - ${currentImageIndex + 1}`}
                                    fill
                                    className="object-contain"
                                    priority
                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                                />
                            </motion.div>
                        </AnimatePresence>

                        {/* Navigation Arrows - Only show if there are multiple images */}
                        {images.length > 1 && (
                            <>
                                {/* Left Arrow */}
                                <motion.button
                                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white rounded-full p-3 transition-colors"
                                    onClick={goToPreviousImage}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <ChevronLeft className="h-8 w-8" />
                                </motion.button>

                                {/* Right Arrow */}
                                <motion.button
                                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white rounded-full p-3 transition-colors"
                                    onClick={goToNextImage}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <ChevronRight className="h-8 w-8" />
                                </motion.button>
                            </>
                        )}
                    </div>

                    {/* Thumbnail Navigation */}
                    {images.length > 1 && (
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
                            <div className="flex space-x-2 bg-black/50 rounded-lg p-2 max-w-xs overflow-x-auto">
                                {images.map((image, index) => (
                                    <motion.button
                                        key={image.id}
                                        className={`relative w-12 h-12 rounded-md overflow-hidden border-2 transition-all ${index === currentImageIndex
                                            ? 'border-white'
                                            : 'border-transparent opacity-60 hover:opacity-80'
                                            }`}
                                        onClick={() => {
                                            // Determine slide direction based on thumbnail position
                                            const direction = index > currentImageIndex ? 1 : index < currentImageIndex ? -1 : 0;
                                            useCatalogProductDetailStore.setState({ slideDirection: direction });
                                            setTimeout(() => {
                                                onImageChange(index);
                                            }, 0);
                                        }}
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <Image
                                            src={image.url}
                                            alt={`${productName} thumbnail ${index + 1}`}
                                            fill
                                            className="object-cover"
                                            sizes="48px"
                                        />
                                    </motion.button>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Product Name */}
                    <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10 bg-black/50 text-white px-4 py-2 rounded-lg text-center max-w-md">
                        <h3 className="font-medium truncate">{productName}</h3>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
