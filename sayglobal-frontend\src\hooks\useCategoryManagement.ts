import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '@/services/api';
import { API_ENDPOINTS } from '@/constants/apiEndpoints';

// Interfaces
interface Brand {
    id: number;
    name: string;
    logoUrl?: string;
}

interface Category {
    id: number;
    name: string;
}

interface SubCategory {
    id: number;
    name: string;
    categoryId: number;
}

interface FeatureDefinition {
    id: number;
    name: string;
    description?: string;
    isRequired?: boolean;
    isMultiSelect?: boolean;
}

interface FeatureValue {
    id: number;
    featureDefinitionId: number;
    value: string;
}

interface BrandCategory {
    id: number;
    brandId: number;
    categoryId: number;
}

interface SubCategoryFeature {
    id: number;
    subCategoryId: number;
    subCategoryName: string;
    featureDefinitionId: number;
    featureDefinitionName: string;
}

interface AllFeatureDefinition {
    featureDefinitionId: number;
    featureDefinitionName: string;
}

// Queries
export const useBrands = () => {
    return useQuery<Brand[]>({
        queryKey: ['brands'],
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_BRANDS);
            return response.data;
        },
        staleTime: 30 * 1000, // 30 saniye
    });
};

export const useCategories = () => {
    return useQuery<Category[]>({
        queryKey: ['categories'],
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_CATEGORIES);
            return response.data;
        },
        staleTime: 30 * 1000, // 30 saniye
    });
};

export const useSubCategories = (categoryId?: number) => {
    return useQuery<SubCategory[]>({
        queryKey: ['subCategories', categoryId],
        queryFn: async () => {
            // Bu fonksiyon yalnızca categoryId tanımlıysa çalışır (`enabled` flag'i sayesinde)
            const url = API_ENDPOINTS.GET_SUBCATEGORIES_BY_CATEGORY.replace('{categoryId}', categoryId!.toString());
            const response = await apiClient.get(url);
            return response.data;
        },
        enabled: !!categoryId, // Sorguyu yalnızca categoryId varsa çalıştır
        staleTime: 30 * 1000, // 30 saniye
    });
};

export const useFeatureValues = (definitionId?: number) => {
    return useQuery<FeatureValue[]>({
        queryKey: ['featureValues', definitionId],
        queryFn: async () => {
            // Bu fonksiyon yalnızca definitionId tanımlıysa çalışır (`enabled` flag'i sayesinde)
            const url = API_ENDPOINTS.GET_FEATURE_VALUES_BY_DEFINITION_ID.replace('{definitionId}', definitionId!.toString());
            const response = await apiClient.get(url);
            return response.data;
        },
        enabled: !!definitionId, // Sorguyu yalnızca definitionId varsa çalıştır
        staleTime: 30 * 1000, // 30 saniye
    });
};

export const useCategoriesByBrand = (brandId?: number) => {
    return useQuery<Category[]>({
        queryKey: ['categoriesByBrand', brandId],
        queryFn: async () => {
            if (!brandId) return [];
            const url = API_ENDPOINTS.GET_CATEGORIES_BY_BRAND_ID.replace('{brandId}', brandId.toString());
            const response = await apiClient.get(url);
            return response.data;
        },
        enabled: !!brandId,
        staleTime: 30 * 1000, // 30 saniye
    });
};

export const useSubCategoryFeatures = (subCategoryId?: number) => {
    return useQuery<SubCategoryFeature[]>({
        queryKey: ['subCategoryFeatures', subCategoryId],
        queryFn: async () => {
            const url = API_ENDPOINTS.GET_SUBCATEGORY_FEATURES_BY_ID.replace('{subCategoryId}', subCategoryId!.toString());
            const response = await apiClient.get(url);
            console.log(`SubCategory ${subCategoryId} features:`, response.data); // DEBUG LOG
            return response.data;
        },
        enabled: !!subCategoryId, // Sorguyu yalnızca subCategoryId varsa çalıştır
        staleTime: 30 * 1000, // 30 saniye
    });
};

export const useAllFeatureDefinitions = () => {
    return useQuery<AllFeatureDefinition[]>({
        queryKey: ['allFeatureDefinitions'],
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_ALL_FEATURE_DEFINITIONS);
            return response.data;
        },
        staleTime: 30 * 1000, // 30 saniye
    });
};

// Mutations
export const useCreateBrand = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (brandData: { name: string; logoUrl?: string }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_BRAND, brandData);
            return response.data;
        },
        onSuccess: () => {
            // Markalar listesini yenile
            queryClient.invalidateQueries({ queryKey: ['brands'] });
        },
        onError: (error) => {
            console.error('Marka oluşturma hatası:', error);
        }
    });
};

export const useCreateCategory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (categoryData: { name: string }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_CATEGORY, categoryData);
            return response.data;
        },
        onSuccess: () => {
            // Kategoriler listesini yenile
            queryClient.invalidateQueries({ queryKey: ['categories'] });
        },
        onError: (error) => {
            console.error('Kategori oluşturma hatası:', error);
        }
    });
};

export const useCreateSubCategory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (subCategoryData: { name: string; categoryId: number }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_SUBCATEGORY, subCategoryData);
            return response.data;
        },
        onSuccess: (data, variables) => {
            // Tüm alt kategori listelerini yenile
            queryClient.invalidateQueries({ queryKey: ['subCategories'] });
            // Özellikle o kategoriye ait alt kategorileri yenile
            queryClient.invalidateQueries({ queryKey: ['subCategories', variables.categoryId] });
        },
        onError: (error) => {
            console.error('Alt kategori oluşturma hatası:', error);
        }
    });
};

export const useCreateFeatureDefinition = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (definitionData: { name: string; description?: string; isRequired?: boolean; isMultiSelect?: boolean; }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_FEATURE_DEFINITION, definitionData);
            return response.data;
        },
        onSuccess: () => {
            // Tüm özellik tanımları listesini yenile
            queryClient.invalidateQueries({ queryKey: ['allFeatureDefinitions'] });
        },
        onError: (error) => {
            console.error('Özellik tanımı oluşturma hatası:', error);
        }
    });
};

export const useCreateFeatureValue = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (valueData: { featureDefinitionId: number; value: string }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_FEATURE_VALUE, valueData);
            return response.data;
        },
        onSuccess: (data, variables) => {
            // Tüm özellik değerleri listelerini yenile
            queryClient.invalidateQueries({ queryKey: ['featureValues'] });
            // Özellikle o özellik tanımına ait değerleri yenile
            queryClient.invalidateQueries({ queryKey: ['featureValues', variables.featureDefinitionId] });
        },
        onError: (error) => {
            console.error('Özellik değeri oluşturma hatası:', error);
        }
    });
};

export const useCreateSubCategoryFeature = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (subCategoryFeatureData: { subCategoryId: number; featureDefinitionId: number }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_SUBCATEGORY_FEATURE, subCategoryFeatureData);
            return response.data;
        },
        onSuccess: (data, variables) => {
            // Tüm alt kategori-özellik ilişkilerini yenile
            queryClient.invalidateQueries({ queryKey: ['subCategoryFeatures'] });
            // Özellikle o alt kategoriye ait özellikleri yenile
            queryClient.invalidateQueries({ queryKey: ['subCategoryFeatures', variables.subCategoryId] });
        },
        onError: (error) => {
            console.error('Alt kategori-özellik ilişkisi oluşturma hatası:', error);
        }
    });
};

export const useCreateBrandCategory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (brandCategoryData: { brandId: number; categoryId: number }) => {
            const response = await apiClient.post(API_ENDPOINTS.CREATE_BRAND_CATEGORY, brandCategoryData);
            return response.data;
        },
        onSuccess: (data, variables) => {
            // Marka-kategori ilişkilerini yenile
            queryClient.invalidateQueries({ queryKey: ['categoriesByBrand'] });
            // Özellikle o markaya ait kategorileri yenile
            queryClient.invalidateQueries({ queryKey: ['categoriesByBrand', variables.brandId] });
        },
        onError: (error) => {
            console.error('Marka-kategori ilişkisi oluşturma hatası:', error);
        }
    });
}; 