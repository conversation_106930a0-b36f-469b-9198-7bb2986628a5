'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { CartItem, CartContextType } from '@/types';

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
    const [items, setItems] = useState<CartItem[]>([]);

    // LocalStorage'dan sepeti yükle
    useEffect(() => {
        if (typeof window !== 'undefined') {
            const savedCart = localStorage.getItem('sayGlobalCart');
            if (savedCart) {
                try {
                    const parsedCart = JSON.parse(savedCart);
                    // Eski sepet verilerinde points bilgisi eksikse 0 olarak ekle
                    const migratedCart = parsedCart.map((item: any) => ({
                        ...item,
                        points: item.points || 0
                    }));
                    setItems(migratedCart);
                } catch (error) {
                    console.error('Sepet verileri yüklenirken hata:', error);
                }
            }
        }
    }, []);

    // Sepet değişikliklerini LocalStorage'a kaydet
    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem('sayGlobalCart', JSON.stringify(items));
        }
    }, [items]);

    const addToCart = (product: any) => {
        setItems(currentItems => {
            const existingItem = currentItems.find(item => item.id === product.id);
            const quantityToAdd = product.quantity || 1; // Gelen quantity parametresini kullan, yoksa 1

            if (existingItem) {
                // Ürün zaten sepette varsa miktarını artır ve points bilgisini güncelle
                return currentItems.map(item =>
                    item.id === product.id
                        ? {
                            ...item,
                            quantity: item.quantity + quantityToAdd, // Belirtilen miktarı ekle
                            points: product.points || item.points || 0 // points bilgisini güncelle
                        }
                        : item
                );
            } else {
                // Yeni ürün ekle
                const newItem: CartItem = {
                    id: product.id,
                    title: product.title,
                    price: product.price,
                    thumbnail: product.thumbnail,
                    brand: product.brand,
                    quantity: quantityToAdd, // Belirtilen miktarı kullan
                    discountPercentage: product.discountPercentage,
                    points: product.points || 0
                };
                return [...currentItems, newItem];
            }
        });
    };

    const removeFromCart = (productId: number) => {
        setItems(currentItems =>
            currentItems.filter(item => item.id !== productId)
        );
    };

    const updateQuantity = (productId: number, quantity: number) => {
        if (quantity <= 0) {
            removeFromCart(productId);
            return;
        }

        setItems(currentItems =>
            currentItems.map(item =>
                item.id === productId
                    ? { ...item, quantity }
                    : item
            )
        );
    };

    const clearCart = () => {
        setItems([]);
    };

    const getTotalItems = () => {
        return items.reduce((total, item) => total + item.quantity, 0);
    };

    const getTotalPrice = () => {
        return items.reduce((total, item) => {
            const itemPrice = item.discountPercentage
                ? item.price * (1 - item.discountPercentage / 100)
                : item.price;
            return total + (itemPrice * item.quantity);
        }, 0);
    };

    const getTotalPoints = () => {
        return items.reduce((total, item) => {
            const points = item.points || 0; // points undefined ise 0 kullan
            return total + (points * item.quantity);
        }, 0);
    };

    const value: CartContextType = {
        items,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        getTotalItems,
        getTotalPrice,
        getTotalPoints
    };

    return (
        <CartContext.Provider value={value}>
            {children}
        </CartContext.Provider>
    );
}

export function useCart() {
    const context = useContext(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
} 