'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, DollarSign, Archive, Info, Lock, Upload, Image as ImageIcon, Trash2 } from 'lucide-react';
import {
    VariantFormData,
    VariantCreationModalProps,
    ProductRatios,
    ProductPoints,
    VariantPricing,
} from '@/types';

const ProductVariantModal: React.FC<VariantCreationModalProps> = ({
    isOpen,
    onClose,
    onSave,
    editingVariant,
    hidePvCvSp = false,
    colorScheme = 'red'
}) => {
    const [formData, setFormData] = useState<VariantFormData | null>(null);
    const [error, setError] = useState<string | null>(null);

    // Renk sınıfları
    const colors = {
        primary: colorScheme === 'blue' ? 'blue' : 'red',
        ring: colorScheme === 'blue' ? 'focus:ring-blue-500' : 'focus:ring-red-500',
        button: colorScheme === 'blue' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'
    };

    // Initialize form data when modal opens with an editing variant
    useEffect(() => {
        if (isOpen && editingVariant) {
            // Eğer points alanı yoksa, hesapla
            const variantData = { ...editingVariant };
            if (!variantData.pricing.points) {
                const { price, ratios } = variantData.pricing;
                variantData.pricing.points = {
                    pv: Math.round(price * (ratios.pvRatio / 100)),
                    cv: Math.round(price * (ratios.cvRatio / 100)),
                    sp: Math.round(price * (ratios.spRatio / 100))
                };
            }
            setFormData(variantData);
        } else {
            setFormData(null);
            setError(null);
        }
    }, [isOpen, editingVariant]);

    // Calculate points whenever price or ratios change
    useEffect(() => {
        if (formData && formData.pricing.price > 0) {
            const { price, ratios } = formData.pricing;
            const newPv = Math.round(price * (ratios.pvRatio / 100));
            const newCv = Math.round(price * (ratios.cvRatio / 100));
            const newSp = Math.round(price * (ratios.spRatio / 100));

            // Sadece değerler değiştiyse güncelle
            if (!formData.pricing.points ||
                formData.pricing.points.pv !== newPv ||
                formData.pricing.points.cv !== newCv ||
                formData.pricing.points.sp !== newSp) {
                setFormData(prev => prev ? {
                    ...prev,
                    pricing: {
                        ...prev.pricing,
                        points: { pv: newPv, cv: newCv, sp: newSp }
                    }
                } : null);
            }
        }
    }, [formData?.pricing.price, formData?.pricing.ratios.pvRatio, formData?.pricing.ratios.cvRatio, formData?.pricing.ratios.spRatio]);

    // Prevent body scroll when modal is open
    useEffect(() => {
        if (isOpen) {
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        }
        return () => {
            document.body.style.overflow = 'unset';
            document.body.style.paddingRight = '0px';
        };
    }, [isOpen]);

    if (!formData) return null;

    const handlePricingChange = (field: keyof VariantPricing, value: number) => {
        setFormData(prev => prev ? { ...prev, pricing: { ...prev.pricing, [field]: value } } : null);
    };

    const handleRatioChange = (field: keyof ProductRatios, value: number) => {
        setFormData(prev => prev ? { ...prev, pricing: { ...prev.pricing, ratios: { ...prev.pricing.ratios, [field]: value } } } : null);
    };

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && formData) {
            const newImages = Array.from(files).map((file, index) => ({
                url: URL.createObjectURL(file),
                isMain: formData.images.length === 0 && index === 0,
                sortOrder: formData.images.length + index,
                file: file
            }));
            setFormData(prev => prev ? { ...prev, images: [...prev.images, ...newImages] } : null);
        }
    };

    const removeImage = (index: number) => {
        setFormData(prev => {
            if (!prev) return null;
            const newImages = prev.images.filter((_, i) => i !== index);
            if (prev.images[index].isMain && newImages.length > 0) {
                newImages[0].isMain = true;
            }
            return { ...prev, images: newImages };
        });
    };

    const setMainImage = (index: number) => {
        setFormData(prev => prev ? { ...prev, images: prev.images.map((img, i) => ({ ...img, isMain: i === index })) } : null);
    };

    const validateForm = (data: VariantFormData): boolean => {
        if (data.pricing.price <= 0) {
            setError('Fiyat 0\'dan büyük olmalıdır');
            return false;
        }
        if (data.pricing.stock < 0) {
            setError('Stok miktarı negatif olamaz');
            return false;
        }
        return true;
    };

    const handleSave = () => {
        if (!formData) return;

        const cleanedPricing = {
            ...formData.pricing,
            price: isNaN(formData.pricing.price) ? 0 : formData.pricing.price,
            stock: isNaN(formData.pricing.stock) ? 0 : formData.pricing.stock,
            extraDiscount: isNaN(formData.pricing.extraDiscount) ? 0 : formData.pricing.extraDiscount,
            ratios: {
                ...formData.pricing.ratios,
                pvRatio: isNaN(formData.pricing.ratios.pvRatio) ? 0 : formData.pricing.ratios.pvRatio,
                cvRatio: isNaN(formData.pricing.ratios.cvRatio) ? 0 : formData.pricing.ratios.cvRatio,
                spRatio: isNaN(formData.pricing.ratios.spRatio) ? 0 : formData.pricing.ratios.spRatio,
            },
        };

        const cleanedData: VariantFormData = { ...formData, pricing: cleanedPricing };

        if (!validateForm(cleanedData)) return;

        onSave(cleanedData);
        onClose();
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
                    <motion.div
                        className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.2 }}
                    >
                        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
                            <div>
                                <h2 className="text-2xl font-semibold text-gray-900">Varyant Detayları</h2>
                                <p className="text-gray-600 mt-1">Varyantın fiyat, stok ve görsellerini düzenleyin.</p>
                            </div>
                            <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
                                <X className="h-6 w-6" />
                            </button>
                        </div>

                        {error && (
                            <div className="p-4 bg-red-50 border-b border-red-200 flex-shrink-0">
                                <p className="text-red-800 text-sm">{error}</p>
                            </div>
                        )}

                        <div className="p-6 overflow-y-auto flex-grow">
                            <div className="space-y-6">
                                <div className="p-4 bg-gray-50 rounded-lg">
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">Varyant Bilgisi</h3>
                                    <p className="text-gray-800 font-semibold text-xl">{formData.name}</p>
                                    <p className="text-gray-600 text-sm mt-1">
                                        {formData.featureDetails.map(f => `${f.featureName}: ${f.featureValue}`).join(', ')}
                                    </p>
                                </div>

                                <div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">Fiyat ve Stok</h3>
                                    <div className={`grid grid-cols-1 gap-4 ${hidePvCvSp ? 'md:grid-cols-2' : 'md:grid-cols-3'}`}>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Fiyat (₺) *</label>
                                            <input
                                                type="number"
                                                value={isNaN(formData.pricing.price) ? '' : formData.pricing.price}
                                                onChange={(e) => handlePricingChange('price', parseFloat(e.target.value))}
                                                className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${colors.ring} focus:border-transparent text-black`}
                                                placeholder="0.00"
                                            />
                                        </div>
                                        {!hidePvCvSp && (
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">İndirim (%)</label>
                                                <input
                                                    type="number"
                                                    value={isNaN(formData.pricing.extraDiscount) ? '' : formData.pricing.extraDiscount}
                                                    onChange={(e) => handlePricingChange('extraDiscount', parseInt(e.target.value))}
                                                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${colors.ring} focus:border-transparent text-black`}
                                                    placeholder="0"
                                                />
                                            </div>
                                        )}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Stok Miktarı *</label>
                                            <input
                                                type="number"
                                                value={isNaN(formData.pricing.stock) ? '' : formData.pricing.stock}
                                                onChange={(e) => handlePricingChange('stock', parseInt(e.target.value))}
                                                className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${colors.ring} focus:border-transparent text-black`}
                                                placeholder="0"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {!hidePvCvSp && (
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900 mb-4">Puan Oranları</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">PV Oranı (%)</label>
                                                <input
                                                    type="number"
                                                    value={isNaN(formData.pricing.ratios.pvRatio) ? '' : formData.pricing.ratios.pvRatio}
                                                    onChange={(e) => handleRatioChange('pvRatio', parseInt(e.target.value))}
                                                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${colors.ring} focus:border-transparent text-black`}
                                                    placeholder="0"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">CV Oranı (%)</label>
                                                <input
                                                    type="number"
                                                    value={isNaN(formData.pricing.ratios.cvRatio) ? '' : formData.pricing.ratios.cvRatio}
                                                    onChange={(e) => handleRatioChange('cvRatio', parseInt(e.target.value))}
                                                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${colors.ring} focus:border-transparent text-black`}
                                                    placeholder="0"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">SP Oranı (%)</label>
                                                <input
                                                    type="number"
                                                    value={isNaN(formData.pricing.ratios.spRatio) ? '' : formData.pricing.ratios.spRatio}
                                                    onChange={(e) => handleRatioChange('spRatio', parseInt(e.target.value))}
                                                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 ${colors.ring} focus:border-transparent text-black`}
                                                    placeholder="0"
                                                />
                                            </div>
                                        </div>
                                        <div className="mt-4 p-3 bg-gray-100 rounded-lg">
                                            <h4 className="text-sm font-medium text-gray-800 mb-2">Hesaplanan Puanlar:</h4>
                                            <div className="grid grid-cols-3 gap-4 text-center text-sm">
                                                <div className="font-semibold text-blue-600">PV: {formData.pricing.points?.pv || 0}</div>
                                                <div className="font-semibold text-green-600">CV: {formData.pricing.points?.cv || 0}</div>
                                                <div className="font-semibold text-purple-600">SP: {formData.pricing.points?.sp || 0}</div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {hidePvCvSp && (
                                    <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                                        <div className="flex">
                                            <div className="flex-shrink-0">
                                                <Info className="h-5 w-5 text-blue-400" />
                                            </div>
                                            <div className="ml-3">
                                                <p className="text-sm text-blue-700">
                                                    <strong>Not:</strong> PV, CV, SP oranları ve indirim yüzdesi admin tarafından belirlenecektir.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                <div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">Varyant Görselleri</h3>
                                    <div className="mb-4">
                                        <label htmlFor="variant-images" className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                <Upload className="w-8 h-8 mb-4 text-gray-500" />
                                                <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Görsel yükle</span></p>
                                                <p className="text-xs text-gray-500">PNG, JPG (MAX. 5MB)</p>
                                            </div>
                                            <input id="variant-images" type="file" multiple accept="image/*" onChange={handleImageUpload} className="hidden" />
                                        </label>
                                    </div>
                                    {formData.images.length > 0 && (
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                            {formData.images.map((image, index) => (
                                                <div key={index} className="relative group aspect-square">
                                                    <img src={image.url} alt={`Varyant görseli ${index + 1}`} className="w-full h-full object-cover rounded-lg" />
                                                    {image.isMain && (
                                                        <div className="absolute top-1 left-1 bg-red-600 text-white text-xs px-2 py-1 rounded">Ana</div>
                                                    )}
                                                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2">
                                                        {!image.isMain && (
                                                            <button type="button" onClick={() => setMainImage(index)} className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">Ana Yap</button>
                                                        )}
                                                        <button type="button" onClick={() => removeImage(index)} className="p-1 bg-red-600 text-white rounded-full hover:bg-red-700"><Trash2 className="h-4 w-4" /></button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 flex-shrink-0">
                            <button onClick={onClose} className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                İptal
                            </button>
                            <button onClick={handleSave} className={`px-6 py-2 ${colors.button} text-white rounded-lg transition-colors flex items-center`}>
                                <Save className="h-4 w-4 mr-2" />
                                Değişiklikleri Kaydet
                            </button>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default ProductVariantModal;
