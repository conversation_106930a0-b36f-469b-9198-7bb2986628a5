# Modern Authentication System

## Genel Bakış

Uygulamamız artık modern ve güvenli bir authentication sistemi kullanıyor. Bu sistem şu özellikleri içeriyor:

- ✅ **HTTP-only Cookies**: Token'lar artık localStorage yerine HTTP-only cookie'lerde saklanıyor
- ✅ **Zustand State Management**: Modern state management ile AuthContext
- ✅ **Otomatik Token Yenileme**: Interceptor ile otomatik refresh token yönetimi
- ✅ **withCredentials: true**: Axios instance'ı cookie desteği ile yapılandırıldı
- ✅ **Token Rotasyonu**: Backend'de token rotation desteği

## Dosya Yapı<PERSON>ı

```
src/
├── stores/
│   └── authStore.ts          # Zustand auth store
├── services/
│   ├── api.ts               # Axios instance (withCredentials: true)
│   └── authService.ts       # Auth API servisleri
├── components/auth/
│   └── AuthContext.tsx      # React Context wrapper
└── types/
    └── index.ts             # TypeScript tipleri
```

## Ana Değişiklikler

### 1. Axios Instance (api.ts)
- `withCredentials: true` eklendi
- localStorage'dan token okuma kaldırıldı
- Otomatik refresh token interceptor'ı güncellendi
- Hata durumunda `auth:logout` event'i fırlatıyor

### 2. Auth Store (authStore.ts)
- Zustand ile modern state management
- `checkAuth()` fonksiyonu uygulama başlangıcında çalışır
- HTTP-only cookie'lerle çalışır
- Error handling ve loading states

### 3. Auth Service (authService.ts)
- localStorage kullanımı tamamen kaldırıldı
- `getUserInfo()` fonksiyonu eklendi (/me, /test, /debug-claims endpoint'lerini dener)
- Sadece API çağrıları yapıyor
- Cookie-based token management

### 4. AuthContext (AuthContext.tsx)
- Zustand store'u sarmalayan wrapper
- Mevcut interface korundu (backward compatibility)
- Uygulama başlangıcında `checkAuth()` otomatik çalışır

## API Endpoint'leri

Sistem şu endpoint'leri kullanıyor:

- `POST /api/Account/login` - Giriş (cookie set edilir)
- `POST /api/Account/register` - Kayıt
- `POST /api/Account/logout` - Çıkış (cookie temizlenir)
- `POST /api/Account/refresh` - Token yenileme (otomatik)
- `GET /api/Account/me` - Kullanıcı bilgileri (öncelikli)
- `GET /api/Account/test` - Test endpoint'i (fallback)
- `GET /api/Account/debug-claims` - Debug bilgileri (fallback)

## Güvenlik Avantajları

### 1. XSS Koruması
- HTTP-only cookie'ler JavaScript ile erişilemez
- Token'lar LocalStorage'da değil, güvenli cookie'lerde

### 2. CSRF Koruması
- `withCredentials: true` ile same-origin policy
- Backend'de CSRF token kontrolü

### 3. Token Rotasyonu
- Her refresh'te yeni token pair
- Eski refresh token'lar geçersiz kılınır
- Replay attack koruması

## Kullanım

### Hook Kullanımı
```tsx
import { useAuth } from '@/components/auth/AuthContext';

function MyComponent() {
  const { user, login, logout, isLoading } = useAuth();
  
  // Kullanım aynı kaldı, arkada modern sistem çalışıyor
}
```

### Direct Store Kullanımı
```tsx
import { useAuthStore } from '@/stores/authStore';

function MyComponent() {
  const { user, isAuthenticated, checkAuth } = useAuthStore();
}
```

## Migration Notes

### Kaldırılan localStorage Keys
- ~~`sayglobal_token`~~ (artık HTTP-only cookie)
- ~~`sayglobal_refresh_token`~~ (artık HTTP-only cookie)
- ~~`sayglobal_user`~~ (state'ten alınıyor)

### Korunan localStorage Keys
- `rememberedEmail` - "Beni Hatırla" özelliği için korundu
- `sayglobal_register_info` - Geçici kayıt bilgileri için korundu

## Backend Gereksinimleri

Backend'in şu özellikleri desteklemesi gerekiyor:

1. **HTTP-only Cookie Support**
   ```javascript
   // Access token cookie
   Set-Cookie: access_token=xxx; HttpOnly; Secure; SameSite=Strict
   
   // Refresh token cookie  
   Set-Cookie: refresh_token=xxx; HttpOnly; Secure; SameSite=Strict
   ```

2. **CORS Configuration**
   ```javascript
   app.use(cors({
     origin: 'http://localhost:3000', // Frontend URL
     credentials: true, // withCredentials için gerekli
   }));
   ```

3. **Token Rotation**
   - Her `/refresh` çağrısında yeni token pair döndür
   - Eski refresh token'ı geçersiz kıl

## Test Etme

1. Giriş yapın - cookies kontrol edin
2. Sayfa yenileyin - oturum korunmalı
3. Token expire olduğunda otomatik yenilenmeli
4. Logout yapın - cookies temizlenmeli

## Troubleshooting

### Cookie Set Edilmiyor
- Backend CORS ayarlarını kontrol edin
- `credentials: true` ayarı var mı?
- Domain/port uyuşmazlığı var mı?

### 401 Hatası
- Backend'de endpoint'ler mevcut mu?
- Cookie'ler doğru gönderiliyor mu?
- Token format doğru mu?

### Auto-refresh Çalışmıyor
- `/api/Account/refresh` endpoint'i çalışıyor mu?
- Interceptor doğru yapılandırılmış mı?
- Infinite loop oluşmuyor mu? 