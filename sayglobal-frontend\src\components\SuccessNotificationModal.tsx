'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, Info, AlertTriangle, XCircle } from 'lucide-react';
import { useSuccessNotificationModal, useSuccessNotificationData, useModalActions } from '@/stores/modalStore';

const SuccessNotificationModal = () => {
    const isOpen = useSuccessNotificationModal();
    const data = useSuccessNotificationData();
    const { closeSuccessNotificationModal } = useModalActions();
    const [progress, setProgress] = useState(0);

    // Auto-close functionality with progress bar
    useEffect(() => {
        if (isOpen && data?.autoClose) {
            setProgress(0);
            const duration = data.duration || 5000; // Default 5 seconds

            // Progress bar update (50ms intervals)
            const progressInterval = setInterval(() => {
                setProgress((prev) => {
                    if (prev >= 99) {
                        clearInterval(progressInterval);
                        return 100;
                    }
                    return prev + (100 / (duration / 50)); // Calculate increment based on duration
                });
            }, 50);

            return () => {
                clearInterval(progressInterval);
            };
        }
    }, [isOpen, data]);

    // Close modal when progress reaches 100%
    useEffect(() => {
        if (progress >= 100 && isOpen && data?.autoClose) {
            const timeoutId = setTimeout(() => {
                closeSuccessNotificationModal();
            }, 100);

            return () => clearTimeout(timeoutId);
        }
    }, [progress, isOpen, data, closeSuccessNotificationModal]);

    const getIconComponent = () => {
        if (!data) return CheckCircle;
        switch (data.icon) {
            case 'success':
                return CheckCircle;
            case 'info':
                return Info;
            case 'warning':
                return AlertTriangle;
            case 'error':
                return XCircle;
            default:
                return CheckCircle;
        }
    };

    const getIconColor = () => {
        if (!data) return 'text-green-500';
        switch (data.icon) {
            case 'success':
                return 'text-green-500';
            case 'info':
                return 'text-blue-500';
            case 'warning':
                return 'text-yellow-500';
            case 'error':
                return 'text-red-500';
            default:
                return 'text-green-500';
        }
    };

    const getIconBgColor = () => {
        if (!data) return 'bg-green-100';
        switch (data.icon) {
            case 'success':
                return 'bg-green-100';
            case 'info':
                return 'bg-blue-100';
            case 'warning':
                return 'bg-yellow-100';
            case 'error':
                return 'bg-red-100';
            default:
                return 'bg-green-100';
        }
    };

    const getGradientClass = () => {
        if (!data) return 'from-green-600 to-emerald-600';
        switch (data.icon) {
            case 'success':
                return 'from-green-600 to-emerald-600';
            case 'info':
                return 'from-blue-600 to-indigo-600';
            case 'warning':
                return 'from-yellow-600 to-orange-600';
            case 'error':
                return 'from-red-600 to-pink-600';
            default:
                return 'from-green-600 to-emerald-600';
        }
    };

    const getProgressColor = () => {
        if (!data) return 'from-green-600 to-emerald-600';
        switch (data.icon) {
            case 'success':
                return 'from-green-600 to-emerald-600';
            case 'info':
                return 'from-blue-600 to-indigo-600';
            case 'warning':
                return 'from-yellow-600 to-orange-600';
            case 'error':
                return 'from-red-600 to-pink-600';
            default:
                return 'from-green-600 to-emerald-600';
        }
    };

    const IconComponent = getIconComponent();

    return (
        <AnimatePresence mode="wait">
            {isOpen && data && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={closeSuccessNotificationModal}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{
                            scale: 1,
                            opacity: 1,
                            y: 0,
                            transition: {
                                type: "spring",
                                stiffness: 300,
                                damping: 25,
                                duration: 0.5
                            }
                        }}
                        exit={{
                            scale: 0.7,
                            opacity: 0,
                            y: 50,
                            transition: {
                                type: "spring",
                                stiffness: 300,
                                damping: 25,
                                duration: 0.5
                            }
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Close button */}
                        <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={closeSuccessNotificationModal}
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <X className="w-6 h-6" />
                        </motion.button>

                        {/* Icon */}
                        <div className="flex justify-center mb-6">
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.1, duration: 0.3, type: "spring", stiffness: 300 }}
                                className={`w-20 h-20 ${getIconBgColor()} rounded-full flex items-center justify-center`}
                            >
                                <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.2, duration: 0.5, ease: "easeInOut" }}
                                >
                                    <IconComponent className={`w-12 h-12 ${getIconColor()}`} />
                                </motion.div>
                            </motion.div>
                        </div>

                        {/* Content */}
                        <div className="text-center">
                            <motion.h2
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.15, duration: 0.3 }}
                                className="text-2xl font-bold text-gray-800 mb-4"
                            >
                                {data.title}
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2, duration: 0.3 }}
                                className="text-gray-600 mb-6 leading-relaxed"
                            >
                                {data.message}
                            </motion.p>

                            {/* Action Button */}
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.25, duration: 0.3 }}
                                className="flex flex-col gap-3"
                            >
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={closeSuccessNotificationModal}
                                    className={`w-full bg-gradient-to-r ${getGradientClass()} text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300`}
                                >
                                    Tamam
                                </motion.button>
                            </motion.div>
                        </div>

                        {/* Progress Bar */}
                        {data.autoClose && (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.35, duration: 0.3 }}
                                className="mt-6"
                            >
                                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                    <motion.div
                                        className={`h-full bg-gradient-to-r ${getProgressColor()} rounded-full`}
                                        initial={{ width: "0%" }}
                                        animate={{ width: `${progress}%` }}
                                        transition={{ duration: 0.1, ease: "linear" }}
                                    />
                                </div>
                            </motion.div>
                        )}

                        {/* Auto close info */}
                        {data.autoClose && (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.4, duration: 0.3 }}
                                className="mt-3 text-center"
                            >
                                <p className="text-sm text-gray-500">
                                    {Math.ceil((100 - progress) / 20)} saniye sonra otomatik olarak kapanacak.
                                </p>
                            </motion.div>
                        )}
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default SuccessNotificationModal; 