'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";

export default function ResetPasswordPage() {
    const searchParams = useSearchParams();
    const token = searchParams.get('token');

    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [errors, setErrors] = useState<{
        token?: string;
        password?: string;
        confirmPassword?: string;
        general?: string;
    }>({});

    useEffect(() => {
        if (!token) {
            setErrors(prev => ({ ...prev, token: "Geçersiz veya eksik sıfırlama bağlantısı. Lütfen e-postanızdaki bağlantıyı kontrol edin." }));
        }
    }, [token]);

    const validateForm = () => {
        const newErrors: typeof errors = {};

        if (!token) {
            newErrors.token = "Geçersiz sıfırlama bağlantısı";
        }

        if (!password) {
            newErrors.password = "Şifre gereklidir";
        } else if (password.length < 8) {
            newErrors.password = "Şifre en az 8 karakter olmalıdır";
        }

        if (password !== confirmPassword) {
            newErrors.confirmPassword = "Şifreler eşleşmiyor";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // Burada gerçek bir API çağrısı yapılacak
            await new Promise(resolve => setTimeout(resolve, 1500)); // Simüle edilmiş gecikme
            setIsSuccess(true);
        } catch (err) {
            setErrors(prev => ({ ...prev, general: "Bir hata oluştu. Lütfen daha sonra tekrar deneyin." }));
        } finally {
            setIsLoading(false);
        }
    };

    const getPasswordStrength = () => {
        if (!password) return { strength: 0, label: "", color: "" };

        // Basit bir şifre gücü kontrolü
        let strength = 0;

        if (password.length >= 8) strength += 1;
        if (password.length >= 12) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;

        const strengthMap = [
            { label: "Çok zayıf", color: "bg-red-500" },
            { label: "Zayıf", color: "bg-orange-500" },
            { label: "Orta", color: "bg-yellow-500" },
            { label: "İyi", color: "bg-lime-500" },
            { label: "Güçlü", color: "bg-green-500" },
            { label: "Çok güçlü", color: "bg-emerald-500" }
        ];

        return {
            strength: Math.min(strength, 5),
            label: strengthMap[strength].label,
            color: strengthMap[strength].color
        };
    };

    const passwordStrength = getPasswordStrength();

    if (isSuccess) {
        return (
            <div className="container mx-auto px-4 py-16">
                <div className="max-w-md mx-auto">
                    <motion.div
                        className="bg-white rounded-2xl shadow-lg overflow-hidden"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="p-8 text-center">
                            <div className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                            </div>

                            <h1 className="text-3xl font-bold mb-4 text-gray-800">Şifreniz Sıfırlandı!</h1>
                            <p className="text-gray-700 mb-8">
                                Şifreniz başarıyla değiştirildi. Artık yeni şifrenizle giriş yapabilirsiniz.
                            </p>

                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <Link
                                    href="/login"
                                    className="inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300"
                                >
                                    Giriş Yap
                                </Link>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-16">
            <div className="max-w-md mx-auto">
                <motion.div
                    className="bg-white rounded-2xl shadow-lg overflow-hidden"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="p-8">
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold mb-2 text-gray-800">Şifrenizi Sıfırlayın</h1>
                            <p className="text-gray-700">
                                Lütfen hesabınız için yeni bir şifre belirleyin
                            </p>
                        </div>

                        {errors.token ? (
                            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                                <p>{errors.token}</p>
                                <div className="mt-4 text-center">
                                    <Link
                                        href="/forgot-password"
                                        className="text-purple-600 font-medium hover:text-purple-800 transition"
                                    >
                                        Yeni bir şifre sıfırlama bağlantısı iste
                                    </Link>
                                </div>
                            </div>
                        ) : (
                            <form onSubmit={handleSubmit}>
                                {errors.general && (
                                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                                        <p>{errors.general}</p>
                                    </div>
                                )}

                                <div className="space-y-6">
                                    <div>
                                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                            Yeni Şifre
                                        </label>
                                        <input
                                            id="password"
                                            type="password"
                                            value={password}
                                            onChange={(e) => {
                                                setPassword(e.target.value);
                                                if (errors.password) setErrors(prev => ({ ...prev, password: undefined }));
                                            }}
                                            className={`w-full px-4 py-3 rounded-lg border ${errors.password ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500`}
                                            placeholder="En az 8 karakter"
                                        />
                                        {errors.password && (
                                            <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                                        )}

                                        {password && (
                                            <div className="mt-2">
                                                <div className="flex items-center justify-between mb-1">
                                                    <div className="h-2 flex-1 bg-gray-200 rounded-full overflow-hidden">
                                                        <div
                                                            className={`h-full ${passwordStrength.color}`}
                                                            style={{ width: `${(passwordStrength.strength + 1) * 16.67}%` }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-xs text-gray-500 ml-2 min-w-[70px]">
                                                        {passwordStrength.label}
                                                    </span>
                                                </div>
                                                <ul className="text-xs text-gray-600 mt-2 space-y-1">
                                                    <li className={`${password.length >= 8 ? 'text-green-500' : ''}`}>
                                                        • En az 8 karakter
                                                    </li>
                                                    <li className={`${/[A-Z]/.test(password) ? 'text-green-500' : ''}`}>
                                                        • En az bir büyük harf
                                                    </li>
                                                    <li className={`${/[0-9]/.test(password) ? 'text-green-500' : ''}`}>
                                                        • En az bir rakam
                                                    </li>
                                                    <li className={`${/[^A-Za-z0-9]/.test(password) ? 'text-green-500' : ''}`}>
                                                        • En az bir özel karakter
                                                    </li>
                                                </ul>
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                            Şifre Tekrar
                                        </label>
                                        <input
                                            id="confirmPassword"
                                            type="password"
                                            value={confirmPassword}
                                            onChange={(e) => {
                                                setConfirmPassword(e.target.value);
                                                if (errors.confirmPassword) setErrors(prev => ({ ...prev, confirmPassword: undefined }));
                                            }}
                                            className={`w-full px-4 py-3 rounded-lg border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500`}
                                            placeholder="Şifrenizi tekrar girin"
                                        />
                                        {errors.confirmPassword && (
                                            <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                                        )}
                                    </div>

                                    <motion.button
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                        type="submit"
                                        disabled={isLoading}
                                        className={`w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                                    >
                                        {isLoading ? (
                                            <span className="flex items-center justify-center">
                                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                İşleniyor...
                                            </span>
                                        ) : (
                                            "Şifreyi Sıfırla"
                                        )}
                                    </motion.button>
                                </div>
                            </form>
                        )}

                        <div className="mt-8 text-center">
                            <p className="text-gray-700">
                                <Link
                                    href="/login"
                                    className="text-purple-600 font-medium hover:text-purple-800 transition"
                                >
                                    Giriş sayfasına dön
                                </Link>
                            </p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
} 