import { useQuery, keepPreviousData, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/api';
import { AdminUser, UserRoleStatistics } from '@/types';

// API'den admin kullanıcı istatistiklerini çeker
export const useUserRoleStatistics = () => {
    return useQuery<UserRoleStatistics>({
        queryKey: ['userRoleStatistics'],
        queryFn: async () => {
            const response = await userService.getUserRoleCounts();
            if (response.success) {
                return response.data.data; // API response'u data wrapper'ı içinde geliyor
            }
            throw new Error(response.error || 'Kullanıcı istatistikleri alınamadı');
        },
        staleTime: 30 * 1000, // 30 saniye boyunca veriyi taze kabul et
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
        refetchInterval: 60 * 1000, // 1 dakikada bir otomatik yenile
    });
};

// Bu hook, tablo için sayfalanmış ve aranmış kullanıcı verilerini çeker.
export const useAdminUsers = (page: number, searchTerm: string, roleFilter: number = 0, statusFilter: number = 0) => {
    return useQuery<AdminUser[]>({
        queryKey: ['adminUsers', page, searchTerm, roleFilter, statusFilter],
        queryFn: async () => {
            // statusFilter: 0=Tümü, 1=Aktif, 2=Pasif
            const requestParams: any = {
                page: page, // Backend 1-based indexing kullanıyor
                pageSize: 10,
                search: searchTerm,
                roleId: roleFilter
            };

            // Sadece belirli bir durum seçildiyse isActive parametresini ekle
            if (statusFilter === 1) {
                requestParams.isActive = true; // Aktif kullanıcılar
            } else if (statusFilter === 2) {
                requestParams.isActive = false; // Pasif kullanıcılar
            }
            // statusFilter === 0 ise isActive parametresi eklenmez (tüm durumlar)

            const response = await userService.getUsers(requestParams);

            if (response.success) {
                return response.data.data?.items || []; // API response'u data.items içinde geliyor
            }
            throw new Error(response.error || 'Kullanıcılar alınamadı');
        },
        placeholderData: keepPreviousData, // Yeni veri yüklenirken eski veriyi göstermeye devam et
        staleTime: 30 * 1000, // 30 saniye boyunca taze kabul et
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
        refetchInterval: 2 * 60 * 1000, // 2 dakikada bir otomatik yenile
    });
};

// Cache yenileme fonksiyonları
export const useUserCacheRefresh = () => {
    const queryClient = useQueryClient();

    const refreshUserLists = () => {
        // Tüm kullanıcı listelerini yenile
        console.log('🔄 Kullanıcı cache\'leri yenileniyor...');
        queryClient.invalidateQueries({ queryKey: ['userRoleStatistics'] });
        queryClient.invalidateQueries({ queryKey: ['adminUsers'] });
    };

    return {
        refreshUserLists
    };
};
