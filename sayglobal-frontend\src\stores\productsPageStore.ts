import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { CategoryItem, SubCategoryItem, FilteredProduct, ProductFilterRequest, ProductSortOption, BrandReference, FeatureDefinitionReference, FeatureValueReference } from '@/types';

// State Interface
interface ProductsPageState {
    // Categories & Subcategories
    categories: CategoryItem[];
    subCategories: { [categoryId: number]: SubCategoryItem[] };

    // Products
    products: FilteredProduct[];
    isLoadingProducts: boolean;

    // Reference Data
    brands: BrandReference[];
    featureDefinitions: FeatureDefinitionReference[];
    featureValues: FeatureValueReference[];
    isLoadingReferenceData: boolean;

    // Filters
    selectedCategoryId: number | null;
    selectedSubCategoryId: number | null;
    selectedBrandIds: number[];
    selectedFeatureValueIds: number[];
    priceRange: [number, number];
    sortBy: ProductSortOption;

    // UI State
    isFilterOpen: boolean;
    hoveredCategoryId: number | null;

    // Error handling
    error: string | null;
}

// Actions Interface
interface ProductsPageActions {
    // Category actions
    setCategories: (categories: CategoryItem[]) => void;
    setSubCategories: (categoryId: number, subCategories: SubCategoryItem[]) => void;

    // Product actions
    setProducts: (products: FilteredProduct[]) => void;
    setLoadingProducts: (loading: boolean) => void;

    // Reference data actions
    setBrands: (brands: BrandReference[]) => void;
    setFeatureDefinitions: (featureDefinitions: FeatureDefinitionReference[]) => void;
    setFeatureValues: (featureValues: FeatureValueReference[]) => void;
    setLoadingReferenceData: (loading: boolean) => void;

    // Filter actions
    setSelectedCategory: (categoryId: number | null) => void;
    setSelectedSubCategory: (subCategoryId: number | null) => void;
    setSelectedBrandIds: (brandIds: number[]) => void;
    setSelectedFeatureValueIds: (featureValueIds: number[]) => void;
    setPriceRange: (range: [number, number]) => void;
    setSortBy: (sortBy: ProductSortOption) => void;
    resetFilters: () => void;

    // UI actions
    setFilterOpen: (open: boolean) => void;
    setHoveredCategory: (categoryId: number | null) => void;

    // Error actions
    setError: (error: string | null) => void;

    // Helper actions
    getCurrentFilterRequest: () => ProductFilterRequest;
}

// Combined store type
type ProductsPageStore = ProductsPageState & ProductsPageActions;

// Initial State
const initialState: ProductsPageState = {
    // Categories & Subcategories
    categories: [],
    subCategories: {},

    // Products
    products: [],
    isLoadingProducts: false,

    // Reference Data
    brands: [],
    featureDefinitions: [],
    featureValues: [],
    isLoadingReferenceData: false,

    // Filters
    selectedCategoryId: null,
    selectedSubCategoryId: null,
    selectedBrandIds: [],
    selectedFeatureValueIds: [],
    priceRange: [0, 10000],
    sortBy: ProductSortOption.Default,

    // UI State
    isFilterOpen: false,
    hoveredCategoryId: null,

    // Error handling
    error: null,
};

// Create store
export const useProductsPageStore = create<ProductsPageStore>()(
    devtools(
        (set, get) => ({
            ...initialState,

            // Category actions
            setCategories: (categories) => {
                console.log('🏪 Zustand: Setting categories:', categories);
                set({ categories }, false, 'productsPage/setCategories');
            },

            setSubCategories: (categoryId, subCategories) => {
                console.log('🏪 Zustand: Setting subcategories for category', categoryId, ':', subCategories);
                set(state => ({
                    subCategories: {
                        ...state.subCategories,
                        [categoryId]: subCategories
                    }
                }), false, 'productsPage/setSubCategories');
            },

            // Product actions
            setProducts: (products) => {
                console.log('🏪 Zustand: Setting products:', products.length, 'items');
                set({ products }, false, 'productsPage/setProducts');
            },

            setLoadingProducts: (loading) => {
                set({ isLoadingProducts: loading }, false, 'productsPage/setLoadingProducts');
            },

            // Reference data actions
            setBrands: (brands) => {
                console.log('🏪 Zustand: Setting brands:', brands);
                set({ brands }, false, 'productsPage/setBrands');
            },

            setFeatureDefinitions: (featureDefinitions) => {
                console.log('🏪 Zustand: Setting feature definitions:', featureDefinitions);
                set({ featureDefinitions }, false, 'productsPage/setFeatureDefinitions');
            },

            setFeatureValues: (featureValues) => {
                console.log('🏪 Zustand: Setting feature values:', featureValues);
                set({ featureValues }, false, 'productsPage/setFeatureValues');
            },

            setLoadingReferenceData: (loading) => {
                set({ isLoadingReferenceData: loading }, false, 'productsPage/setLoadingReferenceData');
            },

            // Filter actions
            setSelectedCategory: (categoryId) => {
                console.log('🏪 Zustand: Setting selected category:', categoryId);
                set({
                    selectedCategoryId: categoryId,
                    selectedSubCategoryId: null // Reset subcategory when category changes
                }, false, 'productsPage/setSelectedCategory');
            },

            setSelectedSubCategory: (subCategoryId) => {
                console.log('🏪 Zustand: Setting selected subcategory:', subCategoryId);
                set({ selectedSubCategoryId: subCategoryId }, false, 'productsPage/setSelectedSubCategory');
            },

            setSelectedBrandIds: (brandIds) => {
                console.log('🏪 Zustand: Setting selected brand IDs:', brandIds);
                set({ selectedBrandIds: brandIds }, false, 'productsPage/setSelectedBrandIds');
            },

            setSelectedFeatureValueIds: (featureValueIds) => {
                console.log('🏪 Zustand: Setting selected feature value IDs:', featureValueIds);
                set({ selectedFeatureValueIds: featureValueIds }, false, 'productsPage/setSelectedFeatureValueIds');
            },

            setPriceRange: (range) => {
                console.log('🏪 Zustand: Setting price range:', range);
                set({ priceRange: range }, false, 'productsPage/setPriceRange');
            },

            setSortBy: (sortBy) => {
                console.log('🏪 Zustand: Setting sort by:', sortBy);
                set({ sortBy }, false, 'productsPage/setSortBy');
            },

            resetFilters: () => {
                console.log('🏪 Zustand: Resetting all filters');
                set({
                    selectedCategoryId: null,
                    selectedSubCategoryId: null,
                    selectedBrandIds: [],
                    selectedFeatureValueIds: [],
                    priceRange: [0, 10000],
                    sortBy: ProductSortOption.Default,
                }, false, 'productsPage/resetFilters');
            },

            // UI actions
            setFilterOpen: (open) => {
                set({ isFilterOpen: open }, false, 'productsPage/setFilterOpen');
            },

            setHoveredCategory: (categoryId) => {
                set({ hoveredCategoryId: categoryId }, false, 'productsPage/setHoveredCategory');
            },

            // Error actions
            setError: (error) => {
                console.log('🏪 Zustand: Setting error:', error);
                set({ error }, false, 'productsPage/setError');
            },

            // Helper actions
            getCurrentFilterRequest: () => {
                const state = get();
                const request: ProductFilterRequest = {
                    sortBy: state.sortBy
                };

                // Add category filter if selected
                if (state.selectedCategoryId) {
                    request.categoryIds = [state.selectedCategoryId];
                }

                // Add subcategory filter if selected
                if (state.selectedSubCategoryId) {
                    request.subCategoryIds = [state.selectedSubCategoryId];
                }

                // Add brand filter if selected
                if (state.selectedBrandIds.length > 0) {
                    request.brandIds = state.selectedBrandIds;
                }

                // Add feature value filter if selected
                if (state.selectedFeatureValueIds.length > 0) {
                    request.featureValueIds = state.selectedFeatureValueIds;
                }

                // Add price range if not default
                if (state.priceRange[0] > 0) {
                    request.minPrice = state.priceRange[0];
                }
                if (state.priceRange[1] < 10000) {
                    request.maxPrice = state.priceRange[1];
                }

                console.log('🏪 Zustand: Generated filter request:', request);
                return request;
            },
        }),
        {
            name: 'products-page-store',
            enabled: process.env.NODE_ENV === 'development',
        }
    )
);

// Selector hooks for performance
export const useProductsPageCategories = () => useProductsPageStore(state => state.categories);
export const useProductsPageProducts = () => useProductsPageStore(state => state.products);
export const useProductsPageReferenceData = () => useProductsPageStore(state => ({
    brands: state.brands,
    featureDefinitions: state.featureDefinitions,
    featureValues: state.featureValues,
    isLoadingReferenceData: state.isLoadingReferenceData,
}));
export const useProductsPageFilters = () => useProductsPageStore(state => ({
    selectedCategoryId: state.selectedCategoryId,
    selectedSubCategoryId: state.selectedSubCategoryId,
    selectedBrandIds: state.selectedBrandIds,
    selectedFeatureValueIds: state.selectedFeatureValueIds,
    priceRange: state.priceRange,
    sortBy: state.sortBy,
}));
export const useProductsPageUI = () => useProductsPageStore(state => ({
    isFilterOpen: state.isFilterOpen,
    hoveredCategoryId: state.hoveredCategoryId,
    isLoadingProducts: state.isLoadingProducts,
    error: state.error,
}));
export const useProductsPageActions = () => useProductsPageStore(state => ({
    setCategories: state.setCategories,
    setSubCategories: state.setSubCategories,
    setProducts: state.setProducts,
    setLoadingProducts: state.setLoadingProducts,
    setBrands: state.setBrands,
    setFeatureDefinitions: state.setFeatureDefinitions,
    setFeatureValues: state.setFeatureValues,
    setLoadingReferenceData: state.setLoadingReferenceData,
    setSelectedCategory: state.setSelectedCategory,
    setSelectedSubCategory: state.setSelectedSubCategory,
    setSelectedBrandIds: state.setSelectedBrandIds,
    setSelectedFeatureValueIds: state.setSelectedFeatureValueIds,
    setPriceRange: state.setPriceRange,
    setSortBy: state.setSortBy,
    resetFilters: state.resetFilters,
    setFilterOpen: state.setFilterOpen,
    setHoveredCategory: state.setHoveredCategory,
    setError: state.setError,
    getCurrentFilterRequest: state.getCurrentFilterRequest,
}));
