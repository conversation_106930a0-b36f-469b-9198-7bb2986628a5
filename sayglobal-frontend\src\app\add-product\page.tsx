'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
    ArrowLeft,
    Save,
    Info,
    Tag,
    Shield,
    Image as ImageIcon,
    Trash2,
    Zap
} from 'lucide-react';
import Link from 'next/link';
import {
    VariantFormData
} from '@/types';
import { productService } from '@/services/api';
import ProductCategorySelector from '@/components/ProductCategorySelector';
import ProductVariantModal from '@/components/ProductVariantModal';
import { useAddProductStore } from '@/stores/addProductStore';
import {
    useModalActions,
    useProductCategorySelectorModal,
    useProductCategorySelectorData,
    useProductVariantModal,
    useProductVariantData,
} from '@/stores/modalStore';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const AddProductPage = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const queryClient = useQueryClient();

    const {
        formData,
        selectedNames,
        variants,
        error,
        availableFeatures,
    } = useAddProductStore();

    const {
        handleInputChange,
        setCategorySelection,
        clearAllSelections,
        deleteVariant,
        setError,
        reset: resetStore,
        setVariants,
        saveVariant,
    } = useAddProductStore(state => state);

    const {
        openProductCategorySelector,
        closeProductCategorySelector,
        openProductVariant,
        closeProductVariant,
    } = useModalActions();

    const showCategorySelector = useProductCategorySelectorModal();
    const categorySelectorData = useProductCategorySelectorData();
    const showVariantModal = useProductVariantModal();
    const variantModalData = useProductVariantData();

    // Varyantlardan selectedFeatures'ı derive eden fonksiyon
    const getSelectedFeaturesFromVariants = (variants: VariantFormData[]) => {
        const selected: { [key: number]: number[] } = {};
        variants.forEach(variant => {
            variant.featureDetails.forEach(detail => {
                if (!selected[detail.featureDefinitionId]) selected[detail.featureDefinitionId] = [];
                if (!selected[detail.featureDefinitionId].includes(detail.featureValueId)) {
                    selected[detail.featureDefinitionId].push(detail.featureValueId);
                }
            });
        });
        return selected;
    };

    const { mutate: createProduct, isPending: isSubmitting } = useMutation({
        mutationFn: (productFormData: FormData) => productService.createDealershipProduct(productFormData),
        onSuccess: () => {
            console.log('✅ Ürün başarıyla eklendi. Cache temizleniyor...');
            queryClient.invalidateQueries({ queryKey: ['myProducts'] });
            queryClient.invalidateQueries({ queryKey: ['products'] });
            router.push('/pending-products');
            resetStore();
        },
        onError: (err: any) => {
            setError(err.message || 'Ürün eklenirken bir hata oluştu');
        }
    });

    useEffect(() => {
        return () => {
            resetStore();
        };
    }, [resetStore]);

    useEffect(() => {
        if (user && user.role !== 'dealership' && user.role !== 'admin') {
            router.push('/');
        }
        if (!isLoading && !user) {
            router.push('/login');
        }
    }, [user, isLoading, router]);

    const handleCategorySelection = (data: {
        brandId: number;
        categoryId: number;
        subCategoryId: number;
        brandName: string;
        categoryName: string;
        subCategoryName: string;
        generatedVariants: VariantFormData[];
        selectedFeatures: { [key: number]: number[] };
        selectedFeatureDetails: { featureName: string; featureValue: string }[];
    }) => {
        setCategorySelection({
            brandId: data.brandId,
            categoryId: data.categoryId,
            subCategoryId: data.subCategoryId,
            brandName: data.brandName,
            categoryName: data.categoryName,
            subCategoryName: data.subCategoryName,
            selectedFeatures: data.selectedFeatures,
            selectedFeatureDetails: data.selectedFeatureDetails,
        });
        setVariants(data.generatedVariants);
        closeProductCategorySelector();
        setError(null);
    };

    const handleEditVariant = (variant: VariantFormData) => {
        openProductVariant({
            editingVariant: variant,
            availableFeatures: availableFeatures,
            existingVariants: variants
        });
    };

    const handleSaveVariant = (variant: VariantFormData) => {
        saveVariant(variant, typeof variant.id === 'number' ? variant.id : undefined);
        closeProductVariant();
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        try {
            if (!formData.name.trim()) throw new Error('Ürün adı gereklidir');
            if (!formData.description.trim()) throw new Error('Ürün açıklaması gereklidir');
            if (formData.brandId <= 0) throw new Error('Marka ve kategori seçimi gereklidir');
            if (variants.length === 0) throw new Error('En az bir varyant oluşturmalısınız.');

            for (const variant of variants) {
                if (variant.pricing.price <= 0) throw new Error(`${variant.name} varyantı için fiyat 0'dan büyük olmalıdır`);
                if (variant.pricing.stock < 0) throw new Error(`${variant.name} varyantı için stok miktarı negatif olamaz`);
            }

            const productFormData = new FormData();
            productFormData.append('Product.Name', formData.name);
            productFormData.append('Product.Description', formData.description);
            productFormData.append('Product.BrandId', formData.brandId.toString());
            productFormData.append('Product.SubCategoryId', formData.subCategoryId.toString());
            productFormData.append('Product.Stock', formData.stock.toString());

            variants.forEach((variant, variantIndex) => {
                productFormData.append(`Variant[${variantIndex}].stock`, variant.pricing.stock.toString());
                productFormData.append(`Variant[${variantIndex}].price`, variant.pricing.price.toString());
                // PV, CV, SP ve extraDiscount gönderilmiyor - sadece satıcılar için

                Object.values(variant.selectedFeatures).flat().forEach((featureValueId, featureIndex) => {
                    productFormData.append(`Variant[${variantIndex}].featureValueIds[${featureIndex}]`, featureValueId.toString());
                });

                variant.images.forEach((image, imageIndex) => {
                    if (image.file) {
                        productFormData.append(`Variant[${variantIndex}].images[${imageIndex}].file`, image.file);
                        productFormData.append(`Variant[${variantIndex}].images[${imageIndex}].isMain`, image.isMain.toString());
                        productFormData.append(`Variant[${variantIndex}].images[${imageIndex}].sortOrder`, image.sortOrder.toString());
                    }
                });
            });

            createProduct(productFormData);

        } catch (error: any) {
            setError(error.message || 'Ürün eklenirken bir hata oluştu');
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || (user.role !== 'dealership' && user.role !== 'admin')) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/pending-products"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Ürün Listesi
                            </Link>
                            <span className="text-gray-300">/</span>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Yeni Ürün Ekle
                            </h1>
                        </div>
                        <div className="flex items-center space-x-2 bg-blue-100 px-4 py-2 rounded-lg">
                            <Shield className="h-5 w-5 text-blue-600" />
                            <span className="text-blue-800 font-medium">Satıcı Erişimi</span>
                        </div>
                    </div>
                </div>

                {error && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-800">{error}</p>
                        <button
                            onClick={() => setError(null)}
                            className="mt-2 text-red-600 hover:text-red-800 underline"
                        >
                            Kapat
                        </button>
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-8">
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        <div className="flex items-center mb-6">
                            <Info className="h-6 w-6 text-blue-600 mr-3" />
                            <h2 className="text-xl font-semibold text-gray-900">Temel Bilgiler</h2>
                        </div>

                        <div className="grid grid-cols-1 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Ürün Adı *
                                </label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                                    placeholder="Ürün adını girin..."
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Açıklama *
                                </label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                    rows={4}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                                    placeholder="Ürün açıklamasını girin..."
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Marka, Kategori ve Detaylar
                                </label>
                                <div className="flex items-center space-x-2">
                                    <button
                                        type="button"
                                        onClick={() => openProductCategorySelector({
                                            initialData: formData ? {
                                                brandId: formData.brandId,
                                                categoryId: formData.categoryId,
                                                subCategoryId: formData.subCategoryId,
                                                selectedFeatures: getSelectedFeaturesFromVariants(variants)
                                            } : { brandId: 0, categoryId: 0, subCategoryId: 0, selectedFeatures: {} }
                                        })}
                                        className="flex-grow px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors flex items-center justify-center text-gray-600 hover:text-blue-600"
                                    >
                                        <Tag className="h-5 w-5 mr-2" />
                                        {formData.brandId > 0 ? 'Kategori Seçimini Düzenle' : 'Marka, Kategori ve Detaylar Seçin'}
                                    </button>
                                    {formData.brandId > 0 && (
                                        <button
                                            type="button"
                                            onClick={clearAllSelections}
                                            title="Tüm seçimleri temizle"
                                            className="flex-shrink-0 p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors"
                                        >
                                            <Trash2 className="h-5 w-5" />
                                        </button>
                                    )}
                                </div>

                                {formData.brandId > 0 && (
                                    <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                                        <p className="text-sm text-gray-600">
                                            <strong>Seçili:</strong> Marka: {selectedNames.brandName}
                                            {formData.categoryId > 0 && `, Kategori: ${selectedNames.categoryName}`}
                                            {formData.subCategoryId > 0 && `, Alt Kategori: ${selectedNames.subCategoryName}`}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center">
                                <Zap className="h-6 w-6 text-blue-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Varyant Yönetimi</h2>
                            </div>
                        </div>

                        {formData.subCategoryId <= 0 && (
                            <div className="p-4 text-center bg-gray-50 rounded-lg">
                                <p className="text-gray-500 font-medium">Tekli ürün eklemek veya varyant oluşturmak için lütfen önce marka, kategori ve alt kategori seçin.</p>
                            </div>
                        )}

                        {formData.subCategoryId > 0 && variants.length === 0 && (
                            <div className="p-8 text-center bg-gray-50 rounded-lg">
                                <Zap className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                                <p className="text-gray-500 font-medium">Henüz varyant oluşturulmadı.</p>
                                <p className="text-sm text-gray-400 mt-1 mb-4">
                                    Yukarıdaki "Marka, Kategori ve Detaylar Seçin" kısmı ile düzenleyebilirsiniz.
                                </p>
                            </div>
                        )}

                        {variants.length > 0 && (
                            <div className="space-y-4">
                                <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                                    <div className="flex items-start">
                                        <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                                        <p className="text-sm text-blue-800">
                                            Oluşturulan her bir varyant kombinasyonu için Fiyat ve Stok bilgilerini girin.
                                            Tek bir ürün ekliyorsanız bile, bu ürün bir varyant olarak kabul edilir.
                                            <br />
                                            <strong>Not:</strong> PV, CV, SP ve indirim oranları admin tarafından belirlenecektir.
                                        </p>
                                    </div>
                                </div>

                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Varyant</th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fiyat (₺)</th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stok</th>
                                                <th scope="col" className="relative px-6 py-3"><span className="sr-only">Actions</span></th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {variants.map((variant) => (
                                                <tr key={variant.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">{variant.name}</div>
                                                        <div className="text-sm text-gray-500">{variant.featureDetails.map(f => f.featureValue).join(', ')}</div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className="text-sm text-gray-900">{variant.pricing.price}</span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className="text-sm text-gray-900">{variant.pricing.stock}</span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <button type="button" onClick={() => handleEditVariant(variant)} className="text-blue-600 hover:text-blue-900 mr-3">Detay</button>
                                                        <button type="button" onClick={() => deleteVariant(typeof variant.id === 'number' ? variant.id : 0)} className="text-gray-500 hover:text-gray-800">Sil</button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex items-center mb-6">
                            <ImageIcon className="h-6 w-6 text-purple-600 mr-3" />
                            <h2 className="text-xl font-semibold text-gray-900">Ürün Fotoğrafları</h2>
                        </div>

                        <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <Info className="h-5 w-5 text-blue-400" />
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-blue-700">
                                        Her bir varyantın fotoğraflarını, varyant tablosundaki "Detay" butonuna tıklayarak açılan pencereden yönetebilirsiniz.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {variants.some(v => v.images.length > 0) && (
                            <div className="mt-6">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">
                                    Tüm Varyant Görselleri (Önizleme)
                                </h3>
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                    {variants.flatMap(variant =>
                                        variant.images.map(image => ({ ...image, variantName: variant.name }))
                                    ).map((image, index) => (
                                        <div key={index} className="relative group">
                                            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                                                <img
                                                    src={image.url}
                                                    alt={`${image.variantName} görseli`}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                            <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                                {image.variantName}
                                            </div>
                                            {image.isMain && (
                                                <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
                                                    Ana
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </motion.div>

                    <motion.div
                        className="flex justify-end space-x-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                    >
                        <Link
                            href="/pending-products"
                            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                            İptal
                        </Link>
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                        >
                            {isSubmitting ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Kaydediliyor...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    Ürünü Kaydet
                                </>
                            )}
                        </button>
                    </motion.div>
                </form>
            </div>

            <ProductCategorySelector
                isOpen={showCategorySelector}
                onClose={closeProductCategorySelector}
                onSelect={handleCategorySelection as any}
                initialData={categorySelectorData?.initialData && typeof categorySelectorData.initialData === 'object' && 'selectedFeatures' in categorySelectorData.initialData
                    ? categorySelectorData.initialData
                    : { brandId: 0, categoryId: 0, subCategoryId: 0, selectedFeatures: {} }}
                colorScheme="blue"
            />

            <ProductVariantModal
                isOpen={showVariantModal}
                onClose={closeProductVariant}
                onSave={handleSaveVariant}
                editingVariant={variantModalData?.editingVariant}
                availableFeatures={availableFeatures}
                existingVariants={variants}
                hidePvCvSp={true}
                colorScheme="blue"
            />
        </div>
    );
};

export default AddProductPage; 