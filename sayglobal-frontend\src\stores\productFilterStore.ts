import { create } from 'zustand';

interface ProductFilterState {
    searchTerm: string;
    categoryFilter: string;
    statusFilter: string;
    setSearchTerm: (term: string) => void;
    setCategoryFilter: (category: string) => void;
    setStatusFilter: (status: string) => void;
    resetFilters: () => void;
}

export const useProductFilterStore = create<ProductFilterState>((set) => ({
    searchTerm: '',
    categoryFilter: 'all',
    statusFilter: 'all',
    setSearchTerm: (term) => set({ searchTerm: term }),
    setCategoryFilter: (category) => set({ categoryFilter: category }),
    setStatusFilter: (status) => set({ statusFilter: status }),
    resetFilters: () => set({ searchTerm: '', categoryFilter: 'all', statusFilter: 'all' }),
})); 