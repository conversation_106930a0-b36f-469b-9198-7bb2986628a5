import imageCompression from 'browser-image-compression';

export interface ImageCompressionOptions {
    maxSizeMB?: number;
    maxWidthOrHeight?: number;
    quality?: number;
    fileType?: 'image/webp' | 'image/jpeg' | 'image/png';
    useWebWorker?: boolean;
}

export interface ImageProcessingResult {
    file: File;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    processingTime: number;
}

export class ImageService {
    // Default WebP optimizasyonu ayarları
    private static readonly DEFAULT_WEBP_OPTIONS: ImageCompressionOptions = {
        maxSizeMB: 1,
        maxWidthOrHeight: 800,
        quality: 0.8,
        fileType: 'image/webp',
        useWebWorker: true
    };

    // Profile fotoğrafları için özel ayarlar
    private static readonly PROFILE_WEBP_OPTIONS: ImageCompressionOptions = {
        maxSizeMB: 0.5,
        maxWidthOrHeight: 400,
        quality: 0.85,
        fileType: 'image/webp',
        useWebWorker: true
    };

    // Product fotoğrafları için özel ayarlar
    private static readonly PRODUCT_WEBP_OPTIONS: ImageCompressionOptions = {
        maxSizeMB: 1.5,
        maxWidthOrHeight: 1200,
        quality: 0.9,
        fileType: 'image/webp',
        useWebWorker: true
    };

    /**
     * Resmi WebP formatına dönüştürür ve optimize eder
     */
    static async convertToWebP(
        file: File,
        options: ImageCompressionOptions = this.DEFAULT_WEBP_OPTIONS
    ): Promise<ImageProcessingResult> {
        const startTime = performance.now();

        console.log('🖼️ WebP dönüştürme başlıyor...', {
            originalName: file.name,
            originalSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
            originalType: file.type
        });

        try {
            const compressionOptions = {
                maxSizeMB: options.maxSizeMB || 1,
                maxWidthOrHeight: options.maxWidthOrHeight || 800,
                useWebWorker: options.useWebWorker !== false,
                fileType: options.fileType || 'image/webp',
                quality: options.quality || 0.8,
                alwaysKeepResolution: false,
                initialQuality: options.quality || 0.8
            };

            const compressedFile = await imageCompression(file, compressionOptions);
            const endTime = performance.now();
            const processingTime = endTime - startTime;

            const result: ImageProcessingResult = {
                file: compressedFile,
                originalSize: file.size,
                compressedSize: compressedFile.size,
                compressionRatio: ((1 - compressedFile.size / file.size) * 100),
                processingTime
            };

            console.log('✅ WebP dönüştürme tamamlandı!', {
                compressedName: compressedFile.name,
                compressedSize: `${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`,
                compressedType: compressedFile.type,
                compressionRatio: `${result.compressionRatio.toFixed(1)}%`,
                processingTime: `${processingTime.toFixed(1)}ms`
            });

            return result;
        } catch (error) {
            console.error('❌ WebP dönüştürme hatası:', error);
            throw new Error('Resim dönüştürülürken hata oluştu');
        }
    }

    /**
     * Profil fotoğrafı için optimize edilmiş WebP dönüştürme
     */
    static async convertProfilePictureToWebP(file: File): Promise<ImageProcessingResult> {
        return this.convertToWebP(file, this.PROFILE_WEBP_OPTIONS);
    }

    /**
     * Ürün fotoğrafı için optimize edilmiş WebP dönüştürme
     */
    static async convertProductImageToWebP(file: File): Promise<ImageProcessingResult> {
        return this.convertToWebP(file, this.PRODUCT_WEBP_OPTIONS);
    }

    /**
     * Resim dosyasını validate eder
     */
    static validateImageFile(file: File, maxSizeMB: number = 10): { isValid: boolean; error?: string } {
        // Dosya tipi kontrolü
        if (!file.type.startsWith('image/')) {
            return {
                isValid: false,
                error: 'Lütfen bir resim dosyası seçin'
            };
        }

        // Boyut kontrolü
        if (file.size > maxSizeMB * 1024 * 1024) {
            return {
                isValid: false,
                error: `Dosya boyutu ${maxSizeMB}MB'dan küçük olmalıdır`
            };
        }

        // Desteklenen formatlar
        const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
        if (!supportedFormats.includes(file.type.toLowerCase())) {
            return {
                isValid: false,
                error: 'Desteklenen formatlar: JPEG, PNG, WebP, GIF'
            };
        }

        return { isValid: true };
    }

    /**
     * Resim preview URL'si oluşturur
     */
    static createPreviewUrl(file: File): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * Çoklu resim işleme için batch processing
     */
    static async convertMultipleToWebP(
        files: File[],
        options: ImageCompressionOptions = this.DEFAULT_WEBP_OPTIONS
    ): Promise<ImageProcessingResult[]> {
        console.log(`🖼️ ${files.length} resim dönüştürülüyor...`);

        const results = await Promise.allSettled(
            files.map(file => this.convertToWebP(file, options))
        );

        const processedResults: ImageProcessingResult[] = [];
        const errors: string[] = [];

        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                processedResults.push(result.value);
            } else {
                errors.push(`${files[index].name}: ${result.reason.message}`);
            }
        });

        if (errors.length > 0) {
            console.warn('⚠️ Bazı resimler işlenirken hata oluştu:', errors);
        }

        console.log(`✅ ${processedResults.length}/${files.length} resim başarıyla dönüştürüldü`);
        return processedResults;
    }

    /**
     * Dosya boyutunu formatlar
     */
    static formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Compression ratio'yu yüzde olarak formatlar
     */
    static formatCompressionRatio(ratio: number): string {
        return `${ratio.toFixed(1)}%`;
    }
}

// Export default instance
export const imageService = ImageService; 