import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { Address, CreateAddressRequest, AuthUser } from '@/types';
import { addressService } from '@/services/api';
import { useAuth } from '@/components/auth/AuthContext';
import { useUserInfo } from '@/hooks/useAuth';
import { useAuthStore } from '@/stores/authStore';

// 🚀 Modal store imports  
import { useModalActions } from '@/stores/modalStore';

// 🏭 Query Key Factory - Organized and type-safe
export const addressKeys = {
    all: ['addresses'] as const,
    lists: () => [...addressKeys.all, 'list'] as const,
    list: (userId: number) => [...addressKeys.lists(), { userId }] as const,
    detail: (id: number) => [...addressKeys.all, 'detail', id] as const,
    // Future endpoints için hazır
    favorites: () => [...addressKeys.all, 'favorites'] as const,
    search: (query: string) => [...addressKeys.all, 'search', { query }] as const,
} as const;

// 🔄 Auth State Sync Helper - Login sonrası state senkronizasyonu için
export const useAddressesAuthSync = () => {
    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al - TanStack Query race condition'ını bypas et
    const authStore = useAuthStore();
    const { data: userData, isLoading: userLoading } = useUserInfo();



    // 🔄 IMPROVED: Auth store priority - race condition'ı önle
    const user = authStore.user || (userData as AuthUser | null);

    // 🎯 Auth durumunu kontrol et - auth store'dan authenticated ise hazır sayılır
    const isAuthReady = authStore.isAuthenticated && !authStore.isLoading && !!user?.id;

    if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Auth sync status:', {
            isAuthenticated: authStore.isAuthenticated,
            authLoading: authStore.isLoading,
            userLoading,
            hasUser: !!user,
            userId: user?.id,
            isAuthReady,
            sourceUser: authStore.user ? 'authStore' : 'tanstackQuery'
        });
    }

    return { isAuthReady, user };
};

// 📡 GET - Adres listesi (Optimized)
export const useAddresses = () => {
    // 🚀 CRITICAL FIX: Auth state senkronizasyonu için helper kullan
    const { isAuthReady, user } = useAddressesAuthSync();
    const queryClient = useQueryClient();

    // 🔍 Enhanced debug info (only in development)
    if (process.env.NODE_ENV === 'development') {
        console.log('🔍 useAddresses hook status:', {
            hasUser: !!user,
            userId: user?.id,
            isAuthReady
        });
    }

    // TanStack Query otomatik olarak user ID'ye göre cache'i yönetiyor

    return useQuery<Address[], Error>({
        queryKey: user?.id ? addressKeys.list(user.id) : ['addresses', 'no-user'],
        queryFn: async (): Promise<Address[]> => {
            if (!user?.id) {
                console.log('⚠️ useAddresses: No user ID available, returning empty array');
                return [];
            }

            console.log('📍 Fetching addresses for user:', user.id);
            const result = await addressService.getAddresses();

            if (!result.success) {
                const error = new Error(result.error || 'Adresler yüklenemedi');
                error.name = 'AddressLoadError';
                throw error;
            }

            // ✅ ID'ye göre sıralama (küçük ID'ler önce - eklenme sırasına göre)
            const addresses = result.data || [];
            const sortedAddresses = addresses.sort((a: Address, b: Address) => {
                // 🏠 1. ÖNCE: Varsayılan adres kontrolü
                if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce
                if (!a.isDefault && b.isDefault) return 1;  // b varsayılan ise önce

                // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)
                if (!a.id && !b.id) return 0;
                if (!a.id) return 1;
                if (!b.id) return -1;
                return a.id - b.id; // Ascending sort (küçük ID'ler önce)
            });

            console.log('📍 Addresses sorted (default first, then by ID):',
                sortedAddresses.map((addr: Address) => ({
                    id: addr.id,
                    title: addr.title,
                    isDefault: addr.isDefault
                }))
            );

            return sortedAddresses;
        },
        enabled: isAuthReady, // 🎯 Improved condition to prevent race conditions
        staleTime: 5 * 60 * 1000, // 5 dakika - data fresh kalacak
        gcTime: 10 * 60 * 1000, // 10 dakika cache
        refetchOnMount: true, // Mount'ta refetch
        refetchOnWindowFocus: false, // ❌ Focus değişiminde refetch YAPMA!
        refetchOnReconnect: true, // Network yeniden bağlandığında refetch
        retry: (failureCount, error) => {
            // Smart retry logic
            if (error.name === 'AddressLoadError') {
                return failureCount < 2; // Max 2 retry for API errors
            }
            return failureCount < 3; // Max 3 retry for other errors
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    });
};

// ➕ POST - Adres ekleme (Optimized)
export const useCreateAddress = () => {
    const queryClient = useQueryClient();
    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al
    const authStore = useAuthStore();
    const { data: userData } = useUserInfo();

    // 🔄 IMPROVED: Auth store priority - race condition'ı önle
    const user = authStore.user || (userData as AuthUser | null);

    return useMutation({
        mutationFn: async (addressData: CreateAddressRequest) => {
            console.log('🔄 TanStack Query: Creating address...', addressData);

            // 🔐 Auth check - prevent creating address for unauthenticated users
            if (!authStore.isAuthenticated || !user?.id) {
                console.error('❌ Authentication Error:', {
                    isAuthenticated: authStore.isAuthenticated,
                    hasUser: !!user,
                    userId: user?.id
                });
                const error = new Error('Kullanıcı oturum açmamış veya kullanıcı bilgileri yüklenmemiş');
                error.name = 'AuthenticationError';
                throw error;
            }



            // 🏠 İlk adres kontrolü - mevcut adresleri al
            const currentAddresses = queryClient.getQueryData<Address[]>(addressKeys.list(user.id)) || [];

            // ✅ Eğer hiç adres yoksa, bu adres varsayılan olarak ayarlanır
            const isFirstAddress = currentAddresses.length === 0;
            const finalAddressData = {
                ...addressData,
                isDefault: isFirstAddress ? true : addressData.isDefault
            };

            if (isFirstAddress) {
                console.log('🏠 İlk adres ekleniyor - otomatik varsayılan olarak ayarlandı');
            }

            const result = await addressService.createAddress(finalAddressData);

            if (!result.success) {
                const error = new Error(result.error || 'Adres eklenemedi');
                error.name = 'AddressCreateError';
                throw error;
            }

            console.log('✅ TanStack Query: Address created successfully');
            return result.data;
        },

        // 🚀 Optimistic Update
        onMutate: async (newAddressData) => {
            const queryKey = addressKeys.list(user?.id || 0);

            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey });

            // Snapshot the previous value for rollback
            const previousAddresses = queryClient.getQueryData<Address[]>(queryKey);

            // Optimistically update to the new value
            if (previousAddresses) {
                // 🏠 İlk adres kontrolü (optimistic update için)
                const isFirstAddress = previousAddresses.length === 0;

                const optimisticAddress: Address = {
                    id: Date.now(), // Temporary ID
                    title: newAddressData.title,
                    fullAddress: newAddressData.fullAddress,
                    city: newAddressData.city,
                    district: newAddressData.district,
                    postalCode: newAddressData.postalCode,
                    isDefault: isFirstAddress ? true : newAddressData.isDefault, // ✅ İlk adres varsayılan
                };

                if (isFirstAddress) {
                    console.log('🏠 Optimistic: İlk adres - otomatik varsayılan olarak ayarlandı');
                }

                // ✅ Doğru sıralama algoritması ile optimistic ekle
                const updatedAddresses = [...previousAddresses, optimisticAddress];
                const sortedAddresses = updatedAddresses.sort((a: Address, b: Address) => {
                    // 🏠 1. ÖNCE: Varsayılan adres kontrolü
                    if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce
                    if (!a.isDefault && b.isDefault) return 1;  // b varsayılan ise önce

                    // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)
                    if (!a.id && !b.id) return 0;
                    if (!a.id) return 1;
                    if (!b.id) return -1;
                    return a.id - b.id; // Ascending sort
                });

                queryClient.setQueryData<Address[]>(queryKey, sortedAddresses);
                console.log('🚀 Optimistic update: Address added to cache with smart sorting (default first)');
            }

            // Return context for rollback
            return { previousAddresses };
        },

        onSuccess: (newAddress, variables, context) => {
            console.log('✅ TanStack Query: Address creation confirmed by server');

            // Update cache with real server data
            const queryKey = addressKeys.list(user?.id || 0);
            queryClient.setQueryData<Address[]>(queryKey, (oldAddresses = []) => {
                // Remove optimistic entry and add real one
                const withoutOptimistic = oldAddresses.filter(addr =>
                    typeof addr.id === 'number' && addr.id > Date.now() - 10000 ? false : true
                );

                // ✅ Doğru sıralama algoritması ile ekle
                const updatedAddresses = [...withoutOptimistic, newAddress];
                const sortedAddresses = updatedAddresses.sort((a: Address, b: Address) => {
                    // 🏠 1. ÖNCE: Varsayılan adres kontrolü
                    if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce
                    if (!a.isDefault && b.isDefault) return 1;  // b varsayılan ise önce

                    // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)
                    if (!a.id && !b.id) return 0;
                    if (!a.id) return 1;
                    if (!b.id) return -1;
                    return a.id - b.id; // Ascending sort
                });

                console.log('📍 Cache updated with smart sorted addresses:',
                    sortedAddresses.map((addr: Address) => ({
                        id: addr.id,
                        title: addr.title,
                        isDefault: addr.isDefault
                    }))
                );
                return sortedAddresses;
            });
        },

        onError: (error, variables, context) => {
            console.error('❌ TanStack Query: Address creation failed:', error);

            // Rollback optimistic update
            if (context?.previousAddresses) {
                const queryKey = addressKeys.list(user?.id || 0);
                queryClient.setQueryData(queryKey, context.previousAddresses);
                console.log('🔄 Rollback: Optimistic update reverted');
            }
        },

        onSettled: () => {
            // Always refetch to ensure consistency
            const queryKey = addressKeys.list(user?.id || 0);
            queryClient.invalidateQueries({ queryKey });
        }
    });
};

// 🗑️ DELETE - Adres silme (Optimized)
export const useDeleteAddress = (onDefaultRemoved?: (nextDefaultAddress: Address, deletedAddress?: Address) => void) => {
    const queryClient = useQueryClient();
    const { data: userData } = useUserInfo();
    const user = userData as AuthUser | null;

    return useMutation({
        mutationFn: async (addressId: number) => {
            console.log('🔄 TanStack Query: Deleting address:', addressId);
            const result = await addressService.deleteAddress(addressId, user?.id || 0);

            if (!result.success) {
                const error = new Error(result.error || 'Adres silinemedi');
                error.name = 'AddressDeleteError';
                throw error;
            }

            console.log('✅ TanStack Query: Address deleted successfully');
            return addressId;
        },

        // 🚀 Optimistic Update
        onMutate: async (addressId) => {
            const queryKey = addressKeys.list(user?.id || 0);

            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey });

            // Snapshot the previous value
            const previousAddresses = queryClient.getQueryData<Address[]>(queryKey);

            // Find removed address info
            const removedAddress = previousAddresses?.find(addr => addr.id === addressId);

            // 🏠 Check if removing default address
            const isRemovingDefault = removedAddress?.isDefault;
            const remainingAddresses = previousAddresses?.filter(addr => addr.id !== addressId) || [];

            // Optimistically remove from cache
            if (previousAddresses) {
                let optimisticAddresses = previousAddresses.filter(addr => addr.id !== addressId);

                // 🚨 If we're removing the default address and there are other addresses
                if (isRemovingDefault && optimisticAddresses.length > 0) {
                    // Make the first remaining address default (by ID order)
                    const nextDefaultAddress = optimisticAddresses.sort((a, b) => (a.id || 0) - (b.id || 0))[0];
                    optimisticAddresses = optimisticAddresses.map(addr => ({
                        ...addr,
                        isDefault: addr.id === nextDefaultAddress.id
                    }));

                    console.log('🏠 Default address removed, setting next address as default:', nextDefaultAddress.title);
                }

                // Apply sorting
                const sortedAddresses = optimisticAddresses.sort((a: Address, b: Address) => {
                    if (a.isDefault && !b.isDefault) return -1;
                    if (!a.isDefault && b.isDefault) return 1;
                    return (a.id || 0) - (b.id || 0);
                });

                queryClient.setQueryData<Address[]>(queryKey, sortedAddresses);
                console.log('🚀 Optimistic update: Address removed from cache with auto-default handling');
            }

            return {
                previousAddresses,
                removedAddress,
                wasDefault: isRemovingDefault,
                remainingAddresses
            };
        },

        onSuccess: (deletedAddressId, addressId, context) => {
            console.log('✅ TanStack Query: Address deletion confirmed by server');

            // 🏠 Check if we removed a default address and need to set a new one
            if (context?.wasDefault && context?.remainingAddresses.length > 0) {
                // Find the next default address (first by ID order)
                const nextDefaultAddress = context.remainingAddresses.sort((a, b) => (a.id || 0) - (b.id || 0))[0];

                console.log('🏠 Default address was removed, next default should be:', nextDefaultAddress.title);

                // Call the callback if provided with both addresses
                if (onDefaultRemoved && nextDefaultAddress) {
                    onDefaultRemoved(nextDefaultAddress, context.removedAddress);
                }
            }
        },

        onError: (error, addressId, context) => {
            console.error('❌ TanStack Query: Address deletion failed:', error);

            // Rollback optimistic update
            if (context?.previousAddresses) {
                const queryKey = addressKeys.list(user?.id || 0);
                queryClient.setQueryData(queryKey, context.previousAddresses);
                console.log('🔄 Rollback: Deleted address restored to cache');
            }
        },

        onSettled: () => {
            // Always refetch to ensure consistency
            const queryKey = addressKeys.list(user?.id || 0);
            queryClient.invalidateQueries({ queryKey });
        }
    });
};

// 🏠 SET DEFAULT - Adres varsayılan yapma (Optimized)
export const useSetDefaultAddress = () => {
    const queryClient = useQueryClient();
    const { data: userData } = useUserInfo();
    const user = userData as AuthUser | null;

    return useMutation({
        mutationFn: async (addressId: number) => {
            console.log('🔄 TanStack Query: Setting address as default:', addressId);
            const result = await addressService.setDefaultAddress(addressId);

            if (!result.success) {
                const error = new Error(result.error || 'Varsayılan adres ayarlanamadı');
                error.name = 'SetDefaultAddressError';
                throw error;
            }

            console.log('✅ TanStack Query: Address set as default successfully');
            return result.data; // Updated address with isDefault: true
        },

        // 🚀 Optimistic Update
        onMutate: async (addressId) => {
            const queryKey = addressKeys.list(user?.id || 0);

            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey });

            // Snapshot the previous value
            const previousAddresses = queryClient.getQueryData<Address[]>(queryKey);

            // Optimistically update addresses
            if (previousAddresses) {
                const optimisticAddresses = previousAddresses.map((addr: Address) => ({
                    ...addr,
                    isDefault: addr.id === addressId, // Only target address becomes default
                }));

                // ✅ Apply sorting to optimistic data
                const sortedAddresses = optimisticAddresses.sort((a: Address, b: Address) => {
                    // 🏠 1. ÖNCE: Varsayılan adres kontrolü
                    if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce
                    if (!a.isDefault && b.isDefault) return 1;  // b varsayılan ise önce

                    // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)
                    if (!a.id && !b.id) return 0;
                    if (!a.id) return 1;
                    if (!b.id) return -1;
                    return a.id - b.id; // Ascending sort
                });

                queryClient.setQueryData<Address[]>(queryKey, sortedAddresses);
                console.log('🚀 Optimistic update: Default address changed with smart sorting');
            }

            return { previousAddresses };
        },

        onSuccess: (updatedAddress, addressId, context) => {
            console.log('✅ TanStack Query: Set default address confirmed by server');

            // Update cache with real server data
            const queryKey = addressKeys.list(user?.id || 0);
            queryClient.setQueryData<Address[]>(queryKey, (oldAddresses = []) => {
                const updatedAddresses = oldAddresses.map((addr: Address) => ({
                    ...addr,
                    isDefault: addr.id === addressId, // Server might return updated data
                }));

                // ✅ Apply sorting to server data
                const sortedAddresses = updatedAddresses.sort((a: Address, b: Address) => {
                    // 🏠 1. ÖNCE: Varsayılan adres kontrolü
                    if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce
                    if (!a.isDefault && b.isDefault) return 1;  // b varsayılan ise önce

                    // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)
                    if (!a.id && !b.id) return 0;
                    if (!a.id) return 1;
                    if (!b.id) return -1;
                    return a.id - b.id; // Ascending sort
                });

                console.log('📍 Cache updated with new default address (smart sorted):',
                    sortedAddresses.map((addr: Address) => ({
                        id: addr.id,
                        title: addr.title,
                        isDefault: addr.isDefault
                    }))
                );
                return sortedAddresses;
            });
        },

        onError: (error, addressId, context) => {
            console.error('❌ TanStack Query: Set default address failed:', error);

            // Rollback optimistic update
            if (context?.previousAddresses) {
                const queryKey = addressKeys.list(user?.id || 0);
                queryClient.setQueryData(queryKey, context.previousAddresses);
                console.log('🔄 Rollback: Default address change reverted');
            }
        },

        onSettled: () => {
            // Always refetch to ensure consistency
            const queryKey = addressKeys.list(user?.id || 0);
            queryClient.invalidateQueries({ queryKey });
        }
    });
};

// 🔄 Manual refetch helper
export const useRefreshAddresses = () => {
    const queryClient = useQueryClient();
    const { data: userData } = useUserInfo();
    const user = userData as AuthUser | null;

    return () => {
        queryClient.invalidateQueries({
            queryKey: addressKeys.list(user?.id || 0)
        });
    };
};

// 📊 Cache utilities
export const useAddressCacheUtils = () => {
    const queryClient = useQueryClient();
    const { data: userData } = useUserInfo();
    const user = userData as AuthUser | null;

    return {
        // Prefetch addresses
        prefetchAddresses: () => {
            return queryClient.prefetchQuery({
                queryKey: addressKeys.list(user?.id || 0),
                queryFn: async () => {
                    const result = await addressService.getAddresses();
                    return result.success ? result.data || [] : [];
                },
                staleTime: 5 * 60 * 1000,
            });
        },

        // Get cached addresses without triggering fetch
        getCachedAddresses: () => {
            return queryClient.getQueryData<Address[]>(addressKeys.list(user?.id || 0));
        },

        // Set addresses manually (for testing or initial data)
        setCachedAddresses: (addresses: Address[]) => {
            queryClient.setQueryData(addressKeys.list(user?.id || 0), addresses);
        },

        // Clear address cache
        clearAddressCache: () => {
            queryClient.removeQueries({ queryKey: addressKeys.all });
        }
    };
}; 