'use client';

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import {
    Package,
    X,
    ChevronLeft,
    ChevronRight,
    Loader2,
    AlertCircle,
    CheckCircle,
    XCircle,
    User,
    Calendar
} from 'lucide-react';
import { useOptimizedProductDetail, useProductMessage } from '@/hooks/useProducts';
import {
    useSelectedVariantIndex,
    useCurrentImageIndex,
    useProductDetailStore
} from '@/stores/productDetailStore';
import { ProductStatus, ProductMessage } from '@/types';

interface AdminProductDetailModalNewProps {
    productId: number | null;
    isOpen: boolean;
    onClose: () => void;
    showApprovalStatus?: boolean; // Ürün onay yönetimi sayfasından açıldığında true
    refreshOnClose?: boolean; // Modal kapatıldığında cache refresh edilsin mi
}

const AdminProductDetailModalNew: React.FC<AdminProductDetailModalNewProps> = ({
    productId,
    isOpen,
    onClose,
    showApprovalStatus = false,
    refreshOnClose = false
}) => {
    const router = useRouter();
    const queryClient = useQueryClient();

    // Zustand store hooks
    const selectedVariantIndex = useSelectedVariantIndex();
    const currentImageIndex = useCurrentImageIndex();
    const setCurrentImage = useProductDetailStore((state) => state.setCurrentImage);
    const setSelectedVariant = useProductDetailStore((state) => state.setSelectedVariant);
    const clearProductCache = useProductDetailStore((state) => state.clearProductCache);

    // Optimized product detail hook
    const { data: productDetail, isLoading, error } = useOptimizedProductDetail(productId);

    // Product message hook - sadece onay yönetimi sayfasından açıldığında çalışır
    const { data: productMessage, isLoading: messageLoading } = useProductMessage(
        productId,
        showApprovalStatus && isOpen
    );

    // Debug bilgisi
    useEffect(() => {
        if (productDetail) {
            console.log('🔍 Modal ProductDetail Data:', {
                id: productDetail.id,
                name: productDetail.name,
                variantsCount: productDetail.variants?.length || 0,
                variants: productDetail.variants,
                hasVariants: productDetail.variants && productDetail.variants.length > 0,
                firstVariant: productDetail.variants?.[0],
                firstVariantPrice: productDetail.variants?.[0]?.price,
                firstVariantPriceType: typeof productDetail.variants?.[0]?.price
            });
        }
    }, [productDetail]);

    // Modal açıldığında body scroll'unu kapat ve cache refresh
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';

            // Eğer onay yönetimi sayfasından açıldıysa cache'i refresh et
            if (showApprovalStatus && productId) {
                console.log('🔄 Onay yönetimi sayfasından modal açıldı, cache refresh ediliyor...');
                queryClient.invalidateQueries({ queryKey: ['productDetail', productId] });
                queryClient.invalidateQueries({ queryKey: ['productMessage', productId] });
            }
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, showApprovalStatus, productId, queryClient]);

    // Varyant değiştiğinde resim indeksini sıfırla
    useEffect(() => {
        setCurrentImage(0);
    }, [selectedVariantIndex, setCurrentImage]);

    // Force refresh cache every time modal opens to ensure fresh data
    useEffect(() => {
        if (isOpen && productId) {
            console.log('🔄 Modal opened, forcing fresh data fetch for product:', productId);
            // Clear both caches to ensure fresh data (for both product updates and status changes)
            clearProductCache(productId);
            queryClient.invalidateQueries({ queryKey: ['productDetail', productId] });
        }
    }, [isOpen, productId, clearProductCache, queryClient]);

    // Enhanced close handler with cache refresh
    const handleClose = () => {
        if (refreshOnClose && productId) {
            console.log('🔄 Modal kapatılıyor, cache refresh ediliyor...');
            queryClient.invalidateQueries({ queryKey: ['productDetail', productId] });
            queryClient.invalidateQueries({ queryKey: ['productMessage', productId] });
            queryClient.invalidateQueries({ queryKey: ['adminProducts'] });
        }
        onClose();
    };

    if (!isOpen) return null;

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    };

    const getApprovalStatusColor = (status: ProductStatus) => {
        switch (status) {
            case ProductStatus.Pending:
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case ProductStatus.Accepted:
                return 'bg-green-100 text-green-800 border-green-200';
            case ProductStatus.Rejected:
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getApprovalStatusText = (status: ProductStatus) => {
        switch (status) {
            case ProductStatus.Pending:
                return 'Onay Bekliyor';
            case ProductStatus.Accepted:
                return 'Onaylandı';
            case ProductStatus.Rejected:
                return 'Reddedildi';
            default:
                return 'Bilinmiyor';
        }
    };

    const getApprovalStatusIcon = (status: ProductStatus) => {
        switch (status) {
            case ProductStatus.Pending:
                return <AlertCircle className="h-4 w-4" />;
            case ProductStatus.Accepted:
                return <CheckCircle className="h-4 w-4" />;
            case ProductStatus.Rejected:
                return <XCircle className="h-4 w-4" />;
            default:
                return <AlertCircle className="h-4 w-4" />;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStockStatus = (stock: number) => {
        if (stock === 0) {
            return { text: 'Stokta Yok', color: 'bg-red-100 text-red-800', icon: <XCircle className="h-4 w-4" /> };
        } else if (stock < 20) {
            return { text: 'Az Stok', color: 'bg-yellow-100 text-yellow-800', icon: <AlertCircle className="h-4 w-4" /> };
        } else {
            return { text: 'Stokta', color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-4 w-4" /> };
        }
    };

    // Güvenli variant seçimi
    const hasVariants = productDetail?.variants && productDetail.variants.length > 0;
    const safeVariantIndex = hasVariants ? Math.min(selectedVariantIndex, productDetail.variants.length - 1) : 0;
    const selectedVariant = hasVariants ? productDetail.variants[safeVariantIndex] : null;
    // Yeni API response yapısında images array'i kullanılıyor
    const currentImages = selectedVariant?.images?.map(img => img.url) || [];
    const stockStatus = selectedVariant ? getStockStatus(selectedVariant.stock) : null;

    const nextImage = () => {
        if (!selectedVariant || !selectedVariant.images?.length) return;
        const imageCount = selectedVariant.images.length;
        const nextIndex = (currentImageIndex + 1) % imageCount;
        setCurrentImage(nextIndex);
    };

    const prevImage = () => {
        if (!selectedVariant || !selectedVariant.images?.length) return;
        const imageCount = selectedVariant.images.length;
        const prevIndex = (currentImageIndex - 1 + imageCount) % imageCount;
        setCurrentImage(prevIndex);
    };

    return (
        <div
            className="fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50"
            onClick={handleClose}
        >
            <motion.div
                className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Modal Header */}
                <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <h2 className="text-xl font-semibold text-gray-900">Ürün Detayları (Admin)</h2>
                        </div>
                        <button
                            onClick={handleClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                {/* Modal Content */}
                <div className="p-6">
                    {isLoading && (
                        <div className="flex items-center justify-center py-12">
                            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                            <span className="ml-2 text-gray-600">Ürün detayları yükleniyor...</span>
                        </div>
                    )}

                    {error && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                            <div className="flex items-start">
                                <AlertCircle className="h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0" />
                                <div className="flex-1">
                                    <h3 className="font-semibold text-red-800 mb-2">Ürün Detayları Yüklenemedi</h3>
                                    <p className="text-red-700 mb-4">
                                        Ürün detayları yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
                                    </p>
                                    <div className="text-sm text-red-600 bg-red-100 rounded p-2 font-mono">
                                        Hata: {error?.message || 'Bilinmeyen hata'}
                                    </div>
                                    <button
                                        onClick={() => window.location.reload()}
                                        className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                                    >
                                        Sayfayı Yenile
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {productDetail && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            {/* Left Column - Images and Variants */}
                            <div className="space-y-6">
                                {/* Variant Tabs */}
                                {hasVariants && productDetail.variants.length > 1 && (
                                    <div className="border-b border-gray-200">
                                        <nav className="-mb-px flex space-x-8">
                                            {productDetail.variants.map((variant, index) => (
                                                <button
                                                    key={variant.id}
                                                    onClick={() => setSelectedVariant(index)}
                                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${selectedVariantIndex === index
                                                        ? 'border-red-500 text-red-600'
                                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                                        }`}
                                                >
                                                    Varyant {index + 1}
                                                    {variant.features.length > 0 && (
                                                        <span className="ml-1 text-xs">
                                                            ({variant.features.map(f => f.featureValue).join(', ')})
                                                        </span>
                                                    )}
                                                </button>
                                            ))}
                                        </nav>
                                    </div>
                                )}

                                {/* Main Image */}
                                <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden">
                                    {currentImages.length > 0 ? (
                                        <>
                                            <img
                                                src={currentImages[currentImageIndex]}
                                                alt={productDetail.name}
                                                className="w-full h-full object-cover"
                                            />
                                            {currentImages.length > 1 && (
                                                <>
                                                    <button
                                                        onClick={prevImage}
                                                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                                                    >
                                                        <ChevronLeft className="h-4 w-4" />
                                                    </button>
                                                    <button
                                                        onClick={nextImage}
                                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                                                    >
                                                        <ChevronRight className="h-4 w-4" />
                                                    </button>
                                                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                                                        {currentImageIndex + 1} / {currentImages.length}
                                                    </div>
                                                </>
                                            )}
                                        </>
                                    ) : (
                                        <div className="w-full h-full flex items-center justify-center">
                                            <Package className="h-16 w-16 text-gray-400" />
                                        </div>
                                    )}
                                </div>

                                {/* Thumbnail Images */}
                                {currentImages.length > 1 && (
                                    <div className="grid grid-cols-4 gap-2">
                                        {currentImages.map((imageUrl, index) => (
                                            <button
                                                key={index}
                                                onClick={() => setCurrentImage(index)}
                                                className={`aspect-square rounded-lg overflow-hidden border-2 ${currentImageIndex === index
                                                    ? 'border-red-500'
                                                    : 'border-gray-200 hover:border-gray-300'
                                                    }`}
                                            >
                                                <img
                                                    src={imageUrl}
                                                    alt={`${productDetail.name} ${index + 1}`}
                                                    className="w-full h-full object-cover"
                                                />
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Right Column - Product Info */}
                            <div className="space-y-6">
                                {/* Product Title and Status */}
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="flex items-center space-x-2">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${productDetail.isActive
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                                }`}>
                                                {productDetail.isActive ? 'Aktif' : 'Pasif'}
                                            </span>
                                            {showApprovalStatus && productDetail.status !== undefined && (
                                                <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full border ${getApprovalStatusColor(productDetail.status)}`}>
                                                    {getApprovalStatusIcon(productDetail.status)}
                                                    <span className="ml-1">{getApprovalStatusText(productDetail.status)}</span>
                                                </span>
                                            )}
                                        </div>
                                        <span className="text-sm text-gray-500">{productDetail.categoryName}</span>
                                    </div>
                                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                        {productDetail.name}
                                    </h1>
                                    <p className="text-lg text-gray-600">{productDetail.brandName}</p>
                                </div>

                                {/* Selected Variant Info */}
                                {selectedVariant ? (
                                    <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                                        <h3 className="font-semibold text-gray-900">Varyant Bilgileri</h3>

                                        {/* Price and Stock */}
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <p className="text-sm text-gray-600">Fiyat</p>
                                                <p className="text-xl font-bold text-gray-900">
                                                    {(() => {
                                                        const price = selectedVariant.price;

                                                        console.log('💰 Fiyat Debug:', {
                                                            price: selectedVariant.price,
                                                            priceType: typeof selectedVariant.price,
                                                            isNull: selectedVariant.price === null,
                                                            isUndefined: selectedVariant.price === undefined,
                                                            isNaN: isNaN(selectedVariant.price),
                                                            variant: selectedVariant
                                                        });

                                                        if (price == null || price === undefined) {
                                                            return 'Fiyat Belirtilmemiş';
                                                        }

                                                        if (isNaN(price)) {
                                                            return 'Geçersiz Fiyat';
                                                        }

                                                        return formatCurrency(price);
                                                    })()}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-600">Stok</p>
                                                <div className="flex items-center space-x-2">
                                                    {stockStatus && (
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stockStatus.color}`}>
                                                            {stockStatus.icon}
                                                            <span className="ml-1">{selectedVariant.stock} - {stockStatus.text}</span>
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Points and Values */}
                                        <div className="grid grid-cols-3 gap-4">
                                            <div>
                                                <p className="text-sm text-gray-600">PV</p>
                                                <p className="text-lg font-semibold text-gray-900">{selectedVariant.pv}</p>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-600">CV</p>
                                                <p className="text-lg font-semibold text-gray-900">{selectedVariant.cv}</p>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-600">SP</p>
                                                <p className="text-lg font-semibold text-gray-900">{selectedVariant.sp}</p>
                                            </div>
                                        </div>

                                        {/* Extra Discount */}
                                        {selectedVariant.extraDiscount > 0 && (
                                            <div>
                                                <p className="text-sm text-gray-600">Ekstra İndirim</p>
                                                <p className="text-lg font-semibold text-green-600">%{selectedVariant.extraDiscount}</p>
                                            </div>
                                        )}

                                        {/* Features */}
                                        {selectedVariant.features.length > 0 && (
                                            <div>
                                                <p className="text-sm text-gray-600 mb-2">Özellikler</p>
                                                <div className="space-y-2">
                                                    {selectedVariant.features.map((feature, index) => (
                                                        <div key={index} className="flex justify-between">
                                                            <span className="text-sm text-gray-600">{feature.featureName}:</span>
                                                            <span className="text-sm font-medium text-gray-900">{feature.featureValue}</span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <div className="flex items-center">
                                            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                                            <div>
                                                <h3 className="font-semibold text-yellow-800">Varyant Bilgileri Eksik</h3>
                                                <p className="text-sm text-yellow-700 mt-1">
                                                    Bu ürün için varyant bilgileri bulunamadı. Ürünü düzenleyerek varyant ekleyebilirsiniz.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Description */}
                                {productDetail.description && (
                                    <div>
                                        <h3 className="font-semibold text-gray-900 mb-2">Açıklama</h3>
                                        <p className="text-gray-600 leading-relaxed">{productDetail.description}</p>
                                    </div>
                                )}

                                {/* Dates */}
                                <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                                    <h3 className="font-semibold text-gray-900 flex items-center">
                                        <Calendar className="h-5 w-5 mr-2 text-gray-600" />
                                        Tarihler
                                    </h3>
                                    <div className="grid grid-cols-1 gap-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Oluşturulma:</span>
                                            <span className="text-gray-900">{formatDate(productDetail.createdAt)}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Son Güncelleme:</span>
                                            <span className="text-gray-900">{formatDate(productDetail.updatedAt)}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Oluşturan Bilgileri */}
                                {productDetail.createdByUserId && (
                                    <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <User className="h-5 w-5 mr-2 text-gray-600" />
                                            Oluşturan
                                        </h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Kullanıcı Adı:</span>
                                                <p className="text-gray-900 font-medium">{productDetail.createdByUserName}</p>
                                            </div>
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Rol:</span>
                                                <p className="text-gray-900 font-medium">
                                                    {productDetail.createdByUserRole?.toLowerCase() === 'admin' ? 'Admin' :
                                                        productDetail.createdByUserRole?.toLowerCase() === 'dealership' ? 'Satıcı' :
                                                            productDetail.createdByUserRole?.toLowerCase() === 'customer' ? 'Müşteri' :
                                                                productDetail.createdByUserRole}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Admin Ürün Notu - Sadece onay yönetimi sayfasından açıldığında göster */}
                                {showApprovalStatus && (
                                    <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <Package className="h-5 w-5 mr-2 text-gray-600" />
                                            Admin Ürün Notu
                                        </h3>

                                        {messageLoading ? (
                                            <div className="flex items-center justify-center py-4">
                                                <Loader2 className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                                                <span className="text-gray-500">Not yükleniyor...</span>
                                            </div>
                                        ) : productMessage ? (
                                            <div className="space-y-3">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="mb-3">
                                                            {productMessage.message ? (
                                                                <p className="text-gray-800 font-medium">
                                                                    {productMessage.message}
                                                                </p>
                                                            ) : (
                                                                <p className="text-gray-500 italic">
                                                                    Admin notu girilmemiş
                                                                </p>
                                                            )}
                                                        </div>
                                                        <div className="text-sm text-gray-600">
                                                            <p>
                                                                <span className="font-medium">Değiştiren:</span> {productMessage.approvedByUser.fullName} ({productMessage.approvedByUser.role})
                                                            </p>
                                                            <p>
                                                                <span className="font-medium">Tarih:</span> {new Date(productMessage.changedAtUtc).toLocaleString('tr-TR')}
                                                            </p>
                                                            <p>
                                                                <span className="font-medium">Durum:</span>
                                                                <span className={`ml-1 ${productMessage.newStatus === 1 ? 'text-green-600' :
                                                                    productMessage.newStatus === 2 ? 'text-red-600' :
                                                                        'text-yellow-600'
                                                                    }`}>
                                                                    {productMessage.newStatus === 1 ? 'Onaylandı' :
                                                                        productMessage.newStatus === 2 ? 'Reddedildi' :
                                                                            'Beklemede'}
                                                                </span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        {productMessage.newStatus === 1 ? (
                                                            <CheckCircle className="h-5 w-5 text-green-500" />
                                                        ) : productMessage.newStatus === 2 ? (
                                                            <XCircle className="h-5 w-5 text-red-500" />
                                                        ) : (
                                                            <AlertCircle className="h-5 w-5 text-yellow-500" />
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="text-center py-4">
                                                {productDetail?.status === ProductStatus.Pending ? (
                                                    <div className="flex items-center justify-center text-yellow-600">
                                                        <AlertCircle className="h-5 w-5 mr-2" />
                                                        <span>Onay bekleniyor</span>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center justify-center text-gray-500">
                                                        <Package className="h-5 w-5 mr-2" />
                                                        <span>Admin notu girilmemiş</span>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* Action Buttons */}
                                <div className="flex space-x-3 pt-4">
                                    <button
                                        onClick={handleClose}
                                        className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                                    >
                                        Kapat
                                    </button>
                                    <button
                                        className="flex-1 px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                                        onClick={() => {
                                            handleClose();
                                            // Hangi sayfadan geldiğini belirle
                                            const referrer = showApprovalStatus ? 'pending-products' : 'products';
                                            router.push(`/admin/products/edit/${productDetail.id}?from=${referrer}`);
                                        }}
                                    >
                                        Düzenle
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </motion.div>
        </div>
    );
};

export default AdminProductDetailModalNew;
