import { create } from 'zustand';

interface UserFilterState {
    searchTerm: string;
    roleFilter: number; // 0: <PERSON>ü<PERSON><PERSON>, 1: <PERSON><PERSON>, 2: Dealership, 3: Customer
    statusFilter: number; // 0: <PERSON>ü<PERSON><PERSON>, 1: Aktif, 2: Pasif
    setSearchTerm: (term: string) => void;
    setRoleFilter: (role: number) => void;
    setStatusFilter: (status: number) => void;
    resetFilters: () => void;
}

export const useUserFilterStore = create<UserFilterState>((set) => ({
    searchTerm: '',
    roleFilter: 0, // Varsayılan olarak tüm roller
    statusFilter: 0, // Varsayılan olarak tüm durumlar
    setSearchTerm: (term) => set({ searchTerm: term }),
    setRoleFilter: (role) => set({ roleFilter: role }),
    setStatusFilter: (status) => set({ statusFilter: status }),
    resetFilters: () => set({ searchTerm: '', roleFilter: 0, statusFilter: 0 }),
}));
