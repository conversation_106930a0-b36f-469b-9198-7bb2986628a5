'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function Footer() {
    const socialIcons = [
        {
            name: 'Facebook',
            path: 'M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z'
        },
        {
            name: 'Instagram',
            path: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'
        },
        {
            name: 'Twitter',
            path: 'M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z'
        },
        {
            name: 'YouTube',
            path: 'M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z'
        }
    ];

    const fadeInUp = {
        initial: { y: 20, opacity: 0 },
        animate: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.6,
            }
        }
    };

    const staggerContainer = {
        animate: {
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    // Hızlı linkler
    const quickLinks = [
        { name: 'Anasayfa', path: '/' },
        { name: 'Ürünler', path: '/products' },
        { name: 'Duyurular', path: '/announcements' },
        { name: 'Distribütör Paneli', path: '/dashboard' },
        { name: 'Giriş Yap', path: '/login' },
        { name: 'Kayıt Ol', path: '/register' }
    ];

    // Kategoriler
    const categories = [
        { name: 'Cilt Bakımı', path: '/products?category=skin-care' },
        { name: 'Saç Bakımı', path: '/products?category=hair-care' },
        { name: 'Vücut Bakımı', path: '/products?category=body-care' },
        { name: 'Takviye Ürünler', path: '/products?category=supplements' },
        { name: 'Parfüm & Deodorant', path: '/products?category=perfume-deodorant' }
    ];

    // Alt bölüm linkleri
    const footerLinks = [
        { name: 'Gizlilik Politikası', path: '/privacy-policy' },
        { name: 'Kullanım Koşulları', path: '/terms-of-use' },
        { name: 'Çerez Politikası', path: '/cookie-policy' }
    ];

    return (
        <footer className="bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-16 pb-8">
            <div className="container mx-auto px-4">
                <motion.div
                    className="grid grid-cols-1 md:grid-cols-4 gap-10"
                    variants={staggerContainer}
                    initial="initial"
                    whileInView="animate"
                    viewport={{ once: true, amount: 0.3 }}
                >
                    {/* Logo ve Hakkında */}
                    <motion.div variants={fadeInUp} className="col-span-1">
                        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-indigo-400 text-transparent bg-clip-text">Say Global</h2>
                        <p className="text-gray-300 mb-6 text-sm leading-relaxed">
                            Sağlıklı yaşam ürünleri ve kozmetik alanında Türkiye'nin lider markası. Doğal içerikli ürünlerle sağlığınıza sağlık katın.
                        </p>
                        <div className="flex space-x-4">
                            {socialIcons.map((icon, index) => (
                                <motion.a
                                    key={index}
                                    href="#"
                                    className="text-gray-400 hover:text-white transition-colors duration-300 transform hover:scale-110"
                                    whileHover={{ scale: 1.2, rotate: 5 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-6 w-6"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d={icon.path} />
                                    </svg>
                                </motion.a>
                            ))}
                        </div>
                    </motion.div>

                    {/* Hızlı Linkler */}
                    <motion.div variants={fadeInUp} className="col-span-1">
                        <h3 className="text-lg font-semibold mb-6 relative inline-block">
                            Hızlı Linkler
                            <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"></span>
                        </h3>
                        <ul className="space-y-3">
                            {quickLinks.map((link, index) => (
                                <motion.li
                                    key={index}
                                    whileHover={{ x: 5 }}
                                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                                >
                                    <Link href={link.path} className="text-gray-300 hover:text-white group flex items-center">
                                        <svg
                                            className="h-3 w-3 mr-2 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 5l7 7-7 7"
                                            />
                                        </svg>
                                        {link.name}
                                    </Link>
                                </motion.li>
                            ))}
                        </ul>
                    </motion.div>

                    {/* Kategoriler */}
                    <motion.div variants={fadeInUp} className="col-span-1">
                        <h3 className="text-lg font-semibold mb-6 relative inline-block">
                            Kategoriler
                            <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"></span>
                        </h3>
                        <ul className="space-y-3">
                            {categories.map((category, index) => (
                                <motion.li
                                    key={index}
                                    whileHover={{ x: 5 }}
                                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                                >
                                    <Link href={category.path} className="text-gray-300 hover:text-white group flex items-center">
                                        <svg
                                            className="h-3 w-3 mr-2 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 5l7 7-7 7"
                                            />
                                        </svg>
                                        {category.name}
                                    </Link>
                                </motion.li>
                            ))}
                        </ul>
                    </motion.div>

                    {/* İletişim */}
                    <motion.div variants={fadeInUp} className="col-span-1">
                        <h3 className="text-lg font-semibold mb-6 relative inline-block">
                            İletişim
                            <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400"></span>
                        </h3>
                        <ul className="space-y-4">
                            <motion.li
                                className="flex items-start"
                                whileHover={{ scale: 1.02 }}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6 mr-3 text-purple-400 flex-shrink-0 mt-0.5"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={1.5}
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                    />
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={1.5}
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                </svg>
                                <span className="text-gray-300 text-sm leading-relaxed">
                                    Atatürk Cad. No:123, 34100
                                    <br />
                                    Kadıköy / İstanbul
                                </span>
                            </motion.li>
                            <motion.li
                                className="flex items-center"
                                whileHover={{ scale: 1.02 }}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6 mr-3 text-purple-400 flex-shrink-0"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={1.5}
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                    />
                                </svg>
                                <span className="text-gray-300">0212 123 45 67</span>
                            </motion.li>
                            <motion.li
                                className="flex items-center"
                                whileHover={{ scale: 1.02 }}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6 mr-3 text-purple-400 flex-shrink-0"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={1.5}
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                    />
                                </svg>
                                <span className="text-gray-300"><EMAIL></span>
                            </motion.li>
                            <motion.li whileHover={{ scale: 1.05 }}>
                                <motion.div
                                    className="mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 p-0.5 rounded-lg"
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <div className="bg-gray-900 rounded-md p-4">
                                        <p className="text-sm text-gray-300 mb-3">Bültenimize abone olun</p>
                                        <div className="flex">
                                            <input
                                                type="email"
                                                placeholder="E-posta adresiniz"
                                                className="flex-1 bg-gray-800 border-none rounded-l-md text-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-purple-500 text-gray-100"
                                            />
                                            <button className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-r-md px-3 py-2 text-sm font-medium">
                                                Abone Ol
                                            </button>
                                        </div>
                                    </div>
                                </motion.div>
                            </motion.li>
                        </ul>
                    </motion.div>
                </motion.div>

                {/* Alt Bölüm */}
                <div className="border-t border-gray-700 mt-12 pt-8">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <motion.p
                            className="text-gray-400 text-sm mb-4 md:mb-0"
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                        >
                            &copy; {new Date().getFullYear()} Say Global. Tüm hakları saklıdır.
                        </motion.p>
                        <div className="flex flex-wrap justify-center gap-4">
                            {footerLinks.map((item, index) => (
                                <motion.div
                                    key={index}
                                    whileHover={{ y: -2 }}
                                    whileTap={{ y: 0 }}
                                >
                                    <Link
                                        href={item.path}
                                        className="text-gray-400 hover:text-white text-sm relative overflow-hidden group"
                                    >
                                        {item.name}
                                        <span className="absolute bottom-0 top-6 left-0 w-full h-0.5 bg-purple-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out "></span>
                                    </Link>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
} 