'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useFavorites } from '@/contexts/FavoritesContext';
import AddToCartModal from '@/components/AddToCartModal';
import FavoriteModal from '@/components/FavoriteModal';
import ImageModal from '@/components/ImageModal';
import { useCatalogProductDetail } from '@/hooks/useCatalogProducts';
import { useDiscountRate } from '@/hooks/useDiscountRate';
import { useAddToCart } from '@/hooks/useCart';
import {
    useCatalogProductDetailStore,
    useSelectedVariant,
    useCurrentImage,
    useVariantImages,
    useCatalogProductQuantity,
    useSlideDirection,
    useCurrentImageIndex
} from '@/stores/catalogProductDetailStore';
import { useCustomerPriceStore } from '@/stores/customerPriceStore';

export default function ProductDetailPage() {
    const { id } = useParams();
    const [productId, setProductId] = useState<number | null>(null);
    const [showAddToCartModal, setShowAddToCartModal] = useState(false);
    const [lastAddedProduct, setLastAddedProduct] = useState<any>(null);
    const [showFavoriteModal, setShowFavoriteModal] = useState(false);
    const [favoriteModalIsAdded, setFavoriteModalIsAdded] = useState(true);
    const [showImageModal, setShowImageModal] = useState(false);

    // Hooks
    const { addToCart } = useCart();
    const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites();
    const { data: discountRateData, isLoading: discountRateLoading } = useDiscountRate();
    const addToCartMutation = useAddToCart();

    // Global customer price state
    const { isCustomerPrice } = useCustomerPriceStore();

    // Store actions
    const {
        setSelectedVariant,
        setCurrentImage,
        increaseQuantity,
        decreaseQuantity,
        resetState
    } = useCatalogProductDetailStore();

    // Store selectors
    const selectedVariant = useSelectedVariant();
    const currentImage = useCurrentImage();
    const variantImages = useVariantImages();
    const quantity = useCatalogProductQuantity();
    const slideDirection = useSlideDirection();
    const currentImageIndex = useCurrentImageIndex();



    // API call
    const { data: productData, isLoading, error } = useCatalogProductDetail(productId);

    // Initialize product ID from params
    useEffect(() => {
        if (id) {
            const numericId = parseInt(id as string);
            if (!isNaN(numericId)) {
                setProductId(numericId);
            }
        }
    }, [id]);

    // Reset store when component unmounts
    useEffect(() => {
        return () => {
            resetState();
        };
    }, []); // Empty dependency array to run only on mount/unmount

    // Loading state
    if (isLoading) {
        return (
            <div className="container mx-auto px-4 py-16 text-center">
                <div className="bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <h2 className="text-2xl font-bold mb-2">Ürün Yükleniyor...</h2>
                    <p className="text-gray-600">
                        Ürün detayları getiriliyor, lütfen bekleyin.
                    </p>
                </div>
            </div>
        );
    }

    // Error state
    if (error || !productData) {
        return (
            <div className="container mx-auto px-4 py-16 text-center">
                <div className="bg-white rounded-xl p-8 shadow-md max-w-lg mx-auto">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-16 w-16 mx-auto text-gray-400 mb-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1.5}
                            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                    </svg>
                    <h2 className="text-2xl font-bold mb-2">Ürün Bulunamadı</h2>
                    <p className="text-gray-600 mb-6">
                        {error?.message || 'Aradığınız ürün bulunamadı veya artık mevcut değil.'}
                    </p>
                    <Link
                        href="/products"
                        className="inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300"
                    >
                        Ürünlere Dön
                    </Link>
                </div>
            </div>
        );
    }

    const product = productData.data;

    // Get stock status text based on API enum
    const getStockStatusText = (stockStatus: number) => {
        switch (stockStatus) {
            case 0: return { text: 'Stokta yok', color: 'text-red-600' }; // OutOfStock
            case 1: return { text: 'Az stok', color: 'text-yellow-600' }; // LowStock
            case 2: return { text: 'Stokta var', color: 'text-green-600' }; // InStock
            default: return { text: 'Bilinmiyor', color: 'text-gray-600' };
        }
    };

    // Calculate points from ratios (pv, cv, sp are percentages) based on original price
    const calculatePoints = (ratio: number, originalPrice: number) => {
        return Math.round((ratio / 100) * originalPrice);
    };

    // Calculate discounted price with membership discount and extra discount
    const calculateDiscountedPrice = (originalPrice: number, extraDiscount: number, applyMembershipDiscount: boolean = true) => {
        let finalPrice = originalPrice;
        const discountRate = discountRateData?.discountRate || null;

        // Müşteri fiyatları gösteriliyorsa membership discount uygulama
        const shouldApplyMembershipDiscount = applyMembershipDiscount && !isCustomerPrice;

        // Önce membership discount uygula (eğer varsa ve uygulanacaksa)
        if (shouldApplyMembershipDiscount && discountRate && discountRate > 0) {
            finalPrice = finalPrice * (1 - discountRate / 100);
        }

        // Sonra extra discount uygula
        if (extraDiscount > 0) {
            finalPrice = finalPrice * (1 - extraDiscount / 100);
        }

        return finalPrice;
    };

    // Check if there's any discount applied
    const hasAnyDiscount = (extraDiscount: number, applyMembershipDiscount: boolean = true) => {
        const discountRate = discountRateData?.discountRate || null;
        return (applyMembershipDiscount && discountRate && discountRate > 0) || (extraDiscount > 0);
    };

    // Image navigation functions
    const goToPreviousImage = (e?: React.MouseEvent) => {
        if (e) {
            e.stopPropagation(); // Modal açılmasını engelle
        }
        // Explicitly set direction before calling prevImage
        const { setCurrentImage } = useCatalogProductDetailStore.getState();
        const state = useCatalogProductDetailStore.getState();
        const currentVariant = state.productData?.data.variants[state.selectedVariantIndex];
        const maxIndex = currentVariant?.images.length || 0;
        if (maxIndex > 0) {
            const prevIndex = state.currentImageIndex === 0 ? maxIndex - 1 : state.currentImageIndex - 1;
            // Set direction first, then update image
            useCatalogProductDetailStore.setState({ slideDirection: -1 });
            setTimeout(() => {
                setCurrentImage(prevIndex);
            }, 0);
        }
    };

    const goToNextImage = (e?: React.MouseEvent) => {
        if (e) {
            e.stopPropagation(); // Modal açılmasını engelle
        }
        // Explicitly set direction before calling nextImage
        const { setCurrentImage } = useCatalogProductDetailStore.getState();
        const state = useCatalogProductDetailStore.getState();
        const currentVariant = state.productData?.data.variants[state.selectedVariantIndex];
        const maxIndex = currentVariant?.images.length || 0;
        if (maxIndex > 0) {
            const nextIndex = (state.currentImageIndex + 1) % maxIndex;
            // Set direction first, then update image
            useCatalogProductDetailStore.setState({ slideDirection: 1 });
            setTimeout(() => {
                setCurrentImage(nextIndex);
            }, 0);
        }
    };

    // Modal image navigation - senkron olarak ana sayfadaki fotoğrafı da değiştirir
    const handleModalImageChange = (index: number) => {
        setCurrentImage(index);
    };

    const handleAddToCart = async () => {
        if (product && selectedVariant && currentImage) {
            try {
                // API'ye sepete ekleme isteği gönder (mutation otomatik olarak cache'i yenileyecek)
                await addToCartMutation.mutateAsync({
                    productVariantId: selectedVariant.id,
                    quantity: quantity,
                    isCustomerPrice: isCustomerPrice
                });

                // Başarılı olduğunda modal için gerekli bilgileri hazırla
                const discountRate = discountRateData?.discountRate || null;
                const membershipDiscount = (!isCustomerPrice && discountRate && discountRate > 0) ? discountRate : 0;
                const extraDiscount = selectedVariant.extraDiscount || 0;

                const cartItem = {
                    id: selectedVariant.id,
                    title: product.name,
                    price: selectedVariant.price, // Orijinal fiyat
                    discountedPrice: calculateDiscountedPrice(selectedVariant.price, selectedVariant.extraDiscount, !isCustomerPrice), // İndirimli fiyat
                    thumbnail: currentImage.url,
                    brand: product.brandName,
                    membershipDiscount: membershipDiscount,
                    extraDiscount: extraDiscount,
                    points: calculatePoints(selectedVariant.pv, selectedVariant.price),
                    quantity: quantity
                };

                // Local sepete de ekle (UI için)
                addToCart(cartItem);

                // Modal için son eklenen ürünü ayarla ve modalı göster
                setLastAddedProduct(cartItem);
                setShowAddToCartModal(true);
            } catch (error) {
                console.error('Sepete ekleme sırasında hata:', error);
                // Hata durumunda kullanıcıya bilgi verilebilir
            }
        }
    };

    // Favorileme fonksiyonu
    const handleFavoriteClick = () => {
        if (!product || !selectedVariant || !currentImage) return;

        // Toplam indirim oranını hesapla
        const discountRate = discountRateData?.discountRate || null;
        let totalDiscountPercentage = 0;

        if (discountRate && discountRate > 0) {
            totalDiscountPercentage += discountRate;
        }

        if (selectedVariant.extraDiscount && selectedVariant.extraDiscount > 0) {
            if (totalDiscountPercentage > 0) {
                totalDiscountPercentage = totalDiscountPercentage + selectedVariant.extraDiscount - (totalDiscountPercentage * selectedVariant.extraDiscount / 100);
            } else {
                totalDiscountPercentage = selectedVariant.extraDiscount;
            }
        }

        const favoriteItem = {
            id: product.id,
            title: product.name,
            price: selectedVariant.price, // Orijinal fiyat
            thumbnail: currentImage.url,
            brand: product.brandName,
            discountPercentage: totalDiscountPercentage,
            points: calculatePoints(selectedVariant.pv, selectedVariant.price),
        };

        const isCurrentlyFavorite = isFavorite(product.id);

        if (isCurrentlyFavorite) {
            removeFromFavorites(product.id);
            setFavoriteModalIsAdded(false);
        } else {
            addToFavorites(favoriteItem);
            setFavoriteModalIsAdded(true);
        }

        setShowFavoriteModal(true);
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-6">
                <Link
                    href="/products"
                    className="inline-flex items-center text-gray-600 hover:text-purple-600 transition-colors"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 19l-7-7 7-7"
                        />
                    </svg>
                    Ürünlere Dön
                </Link>
            </div>

            <div className="flex flex-col lg:flex-row gap-10">
                {/* Ürün Resimleri */}
                <motion.div
                    className="lg:w-1/2"
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="bg-white rounded-xl overflow-hidden shadow-md mb-4">
                        <div
                            className="relative h-96 w-full cursor-pointer"
                            onClick={() => setShowImageModal(true)}
                        >
                            <AnimatePresence mode="wait">
                                {currentImage && (
                                    <motion.div
                                        key={currentImageIndex}
                                        initial={slideDirection !== 0 ? { x: slideDirection > 0 ? 300 : -300, opacity: 0 } : { opacity: 1 }}
                                        animate={{ x: 0, opacity: 1 }}
                                        exit={slideDirection !== 0 ? { x: slideDirection > 0 ? -300 : 300, opacity: 0 } : { opacity: 0 }}
                                        transition={{
                                            type: "tween",
                                            ease: "easeOut",
                                            duration: slideDirection !== 0 ? 0.2 : 0,
                                            delay: slideDirection !== 0 ? 0.1 : 0
                                        }}
                                        className="absolute inset-0"
                                    >
                                        <Image
                                            src={currentImage.url}
                                            alt={product.name}
                                            fill
                                            className="object-contain"
                                        />
                                    </motion.div>
                                )}
                            </AnimatePresence>

                            {/* Navigation Arrows - Only show if there are multiple images */}
                            {variantImages.length > 1 && (
                                <>
                                    {/* Left Arrow */}
                                    <motion.button
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                        onClick={(e) => goToPreviousImage(e)}
                                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10"
                                    >
                                        <ChevronLeft className="h-6 w-6" />
                                    </motion.button>

                                    {/* Right Arrow */}
                                    <motion.button
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                        onClick={(e) => goToNextImage(e)}
                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full p-2 shadow-lg transition-all duration-300 z-10"
                                    >
                                        <ChevronRight className="h-6 w-6" />
                                    </motion.button>
                                </>
                            )}

                            {selectedVariant && selectedVariant.extraDiscount > 0 && (
                                <div className="absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                                    %{selectedVariant.extraDiscount} İndirim
                                </div>
                            )}

                            {/* Membership Discount Badge - Extra discount varsa altında, yoksa üstte */}
                            {!isCustomerPrice && discountRateData?.discountRate && discountRateData.discountRate > 0 && (
                                <div className={`absolute ${selectedVariant && selectedVariant.extraDiscount > 0 ? 'top-12' : 'top-4'} right-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg`}>
                                    %{discountRateData.discountRate} Üye İndirimi
                                </div>
                            )}
                            {selectedVariant && (
                                <div className="absolute top-4 left-4 flex flex-col space-y-2">
                                    {/* PV Badge */}
                                    <div className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                        </svg>
                                        <span className="font-bold">PV</span>
                                        <span>{calculatePoints(selectedVariant.pv, selectedVariant.price)}</span>
                                    </div>
                                    {/* CV Badge */}
                                    <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                        </svg>
                                        <span className="font-bold">CV</span>
                                        <span>{calculatePoints(selectedVariant.cv, selectedVariant.price)}</span>
                                    </div>
                                    {/* SP Badge */}
                                    <div className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center gap-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                        </svg>
                                        <span className="font-bold">SP</span>
                                        <span>{calculatePoints(selectedVariant.sp, selectedVariant.price)}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Küçük Resimler */}
                    <div className="grid grid-cols-5 gap-2">
                        {variantImages.map((image, index) => (
                            <motion.div
                                key={image.id}
                                className={`relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${currentImage?.id === image.id
                                    ? "border-purple-500"
                                    : "border-gray-200"
                                    }`}
                                whileHover={{ scale: 1.05 }}
                                onClick={() => {
                                    // Determine slide direction based on thumbnail position
                                    const direction = index > currentImageIndex ? 1 : index < currentImageIndex ? -1 : 0;
                                    useCatalogProductDetailStore.setState({ slideDirection: direction });
                                    setTimeout(() => {
                                        setCurrentImage(index);
                                    }, 0);
                                }}
                            >
                                <div className="relative h-16 w-full">
                                    <Image
                                        src={image.url}
                                        alt={`${product.name} - ${index + 1}`}
                                        fill
                                        className="object-cover"
                                    />
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>

                {/* Ürün Bilgileri */}
                <motion.div
                    className="lg:w-1/2"
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                >
                    <div className="bg-white rounded-xl shadow-md p-8">
                        <div className="flex justify-between items-start mb-4">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                                    {product.name}
                                </h1>
                                <p className="text-gray-600 mb-2">{product.brandName}</p>
                            </div>
                            {product.averageRating && (
                                <div className="flex items-center bg-yellow-50 px-3 py-1 rounded-full">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 text-yellow-400"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <span className="ml-1 font-medium text-yellow-700">
                                        {product.averageRating.toFixed(1)}
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Varyant Seçimi */}
                        <div className="border-b border-gray-100 pb-6 mb-6">
                            {product.variants && product.variants.length > 0 && (
                                <div className="mb-6">
                                    <h3 className="font-semibold mb-3 text-gray-700">Varyant Seçimi</h3>
                                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                                        {product.variants.map((variant, index) => (
                                            <motion.button
                                                key={variant.id}
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                                onClick={() => setSelectedVariant(index)}
                                                className={`p-3 border-2 rounded-lg text-sm transition-all duration-200 ${selectedVariant?.id === variant.id
                                                    ? 'border-purple-500 bg-purple-50 text-purple-700'
                                                    : 'border-gray-200 hover:border-purple-300'
                                                    }`}
                                            >
                                                <div className="text-left">
                                                    {variant.features.map((feature, idx) => (
                                                        <div key={idx} className="text-xs text-gray-600">
                                                            {feature.featureName}: <span className="font-medium">{feature.featureValue}</span>
                                                        </div>
                                                    ))}
                                                    <div className="font-semibold mt-1">
                                                        {hasAnyDiscount(variant.extraDiscount, !isCustomerPrice) ? (
                                                            <>
                                                                <span className={selectedVariant?.id === variant.id ? "text-purple-700" : "text-gray-600"}>{calculateDiscountedPrice(variant.price, variant.extraDiscount).toFixed(2)} ₺</span>
                                                                <span className="text-gray-500 line-through text-xs ml-1">{variant.price.toFixed(2)} ₺</span>
                                                            </>
                                                        ) : (
                                                            <span className={selectedVariant?.id === variant.id ? "text-purple-700" : "text-gray-600"}>{variant.price.toFixed(2)} ₺</span>
                                                        )}
                                                    </div>
                                                    <div className={`text-xs mt-1 ${getStockStatusText(variant.stockStatus).color}`}>
                                                        {getStockStatusText(variant.stockStatus).text}
                                                    </div>
                                                    {/* Puan Bilgileri */}
                                                    <div className="flex space-x-1 mt-2">
                                                        <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium">
                                                            PV {calculatePoints(variant.pv, variant.price)}
                                                        </span>
                                                        <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                                            CV {calculatePoints(variant.cv, variant.price)}
                                                        </span>
                                                        <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">
                                                            SP {calculatePoints(variant.sp, variant.price)}
                                                        </span>
                                                    </div>
                                                </div>
                                            </motion.button>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {selectedVariant && (
                                <>
                                    <div className="flex items-baseline mb-4">
                                        {hasAnyDiscount(selectedVariant.extraDiscount, !isCustomerPrice) ? (
                                            <>
                                                <span className="text-3xl font-bold text-purple-700 mr-2">
                                                    {calculateDiscountedPrice(selectedVariant.price, selectedVariant.extraDiscount).toFixed(2)} ₺
                                                </span>
                                                <span className="text-lg text-gray-500 line-through">
                                                    {selectedVariant.price.toFixed(2)} ₺
                                                </span>
                                                {/* Discount badges */}
                                                <div className="flex flex-col gap-1 ml-3">
                                                    {!isCustomerPrice && discountRateData?.discountRate && discountRateData.discountRate > 0 && (
                                                        <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                            %{discountRateData.discountRate} Üye İndirimi
                                                        </span>
                                                    )}
                                                    {selectedVariant.extraDiscount > 0 && (
                                                        <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                            %{selectedVariant.extraDiscount} İndirim
                                                        </span>
                                                    )}
                                                </div>
                                            </>
                                        ) : (
                                            <span className="text-3xl font-bold text-purple-700 mr-2">
                                                {selectedVariant.price.toFixed(2)} ₺
                                            </span>
                                        )}
                                    </div>

                                    {/* Puan Bilgileri */}
                                    <div className="mb-4">
                                        <h4 className="text-sm font-medium text-gray-700 mb-2">Bu ürünü satın alarak kazanacağınız puanlar:</h4>
                                        <div className="flex flex-wrap gap-2">
                                            <div className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                                </svg>
                                                <span className="font-bold">PV</span>
                                                <span>{calculatePoints(selectedVariant.pv, selectedVariant.price)} Puan</span>
                                            </div>
                                            <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                                </svg>
                                                <span className="font-bold">CV</span>
                                                <span>{calculatePoints(selectedVariant.cv, selectedVariant.price)} Puan</span>
                                            </div>
                                            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg flex items-center gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                                </svg>
                                                <span className="font-bold">SP</span>
                                                <span>{calculatePoints(selectedVariant.sp, selectedVariant.price)} Puan</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-4">
                                        <div className={`text-sm flex items-center ${getStockStatusText(selectedVariant.stockStatus).color}`}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                className="h-5 w-5 mr-1"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M5 13l4 4L19 7"
                                                />
                                            </svg>
                                            {getStockStatusText(selectedVariant.stockStatus).text}
                                        </div>

                                        {/* Kategori Bilgisi */}
                                        <div className="text-sm flex items-center text-gray-600">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                className="h-5 w-5 mr-1"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                                                />
                                            </svg>
                                            Kategori: {product.categoryName}
                                        </div>
                                    </div>
                                </>
                            )}
                        </div>

                        <div className="mb-8">
                            <div className="flex flex-col sm:flex-row items-center gap-4 mb-6">
                                <div className="flex items-center border border-gray-200 rounded-md text-gray-600">
                                    <motion.button
                                        whileTap={{ scale: 0.9 }}
                                        onClick={decreaseQuantity}
                                        className="px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none"
                                    >
                                        -
                                    </motion.button>
                                    <div className="w-12 text-center">{quantity}</div>
                                    <motion.button
                                        whileTap={{ scale: 0.9 }}
                                        onClick={increaseQuantity}
                                        className="px-4 py-2 text-gray-600 hover:text-purple-700 focus:outline-none"
                                    >
                                        +
                                    </motion.button>
                                </div>

                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={handleAddToCart}
                                    className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 mr-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                                        />
                                    </svg>
                                    Sepete Ekle
                                </motion.button>

                                <motion.button
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                    onClick={handleFavoriteClick}
                                    className={`w-12 h-12 flex items-center justify-center border rounded-full transition-colors ${isFavorite(product.id)
                                        ? 'border-red-500 text-red-500 bg-red-50'
                                        : 'border-gray-200 text-gray-400 hover:text-red-500 hover:border-red-500'
                                        }`}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-6 w-6"
                                        fill={isFavorite(product.id) ? 'currentColor' : 'none'}
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                        />
                                    </svg>
                                </motion.button>
                            </div>
                        </div>

                        <div>
                            <h3 className="font-semibold mb-3 text-gray-700">Ürün Açıklaması</h3>
                            <div className="text-gray-700 leading-relaxed">
                                {product.description}
                            </div>
                        </div>
                    </div>
                </motion.div>
            </div>

            {/* Add To Cart Modal */}
            <AddToCartModal
                isOpen={showAddToCartModal}
                onClose={() => setShowAddToCartModal(false)}
                product={lastAddedProduct}
                quantity={quantity}
            />

            {/* Favorite Modal */}
            <FavoriteModal
                isOpen={showFavoriteModal}
                onClose={() => setShowFavoriteModal(false)}
                product={selectedVariant && currentImage ? {
                    id: product.id,
                    title: product.name,
                    price: selectedVariant.price,
                    thumbnail: currentImage.url,
                    brand: product.brandName,
                    discountPercentage: selectedVariant.extraDiscount,
                    points: calculatePoints(selectedVariant.pv, selectedVariant.price),
                } : null}
                isAdded={favoriteModalIsAdded}
            />

            {/* Image Modal */}
            <ImageModal
                isOpen={showImageModal}
                onClose={() => setShowImageModal(false)}
                images={variantImages}
                currentImageIndex={variantImages.findIndex(img => img.id === currentImage?.id)}
                onImageChange={handleModalImageChange}
                productName={product.name}
            />
        </div>
    );
} 