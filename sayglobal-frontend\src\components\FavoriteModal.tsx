'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

interface FavoriteModalProps {
    isOpen: boolean;
    onClose: () => void;
    product: any;
    isAdded: boolean; // true: eklendi, false: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
}

export default function FavoriteModal({ isOpen, onClose, product, isAdded }: FavoriteModalProps) {
    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={onClose}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Heart Icon */}
                        <motion.div
                            className="text-center mb-6"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                        >
                            <div className={`mx-auto w-20 h-20 ${isAdded ? 'bg-red-100' : 'bg-gray-100'} rounded-full flex items-center justify-center mb-4`}>
                                <motion.svg
                                    className={`w-10 h-10 ${isAdded ? 'text-red-600' : 'text-gray-500'}`}
                                    fill={isAdded ? 'currentColor' : 'none'}
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                    />
                                </motion.svg>
                            </div>

                            <motion.h2
                                className="text-2xl font-bold text-gray-800 mb-2"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                {isAdded ? 'Favorilere Eklendi!' : 'Favorilerden Çıkarıldı!'}
                            </motion.h2>

                            <motion.p
                                className="text-gray-600"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.4 }}
                            >
                                {isAdded
                                    ? 'Ürün favori listenize başarıyla eklendi.'
                                    : 'Ürün favori listenizden çıkarıldı.'}
                            </motion.p>
                        </motion.div>

                        {/* Product Info */}
                        {product && (
                            <motion.div
                                className={`${isAdded ? 'bg-red-50 border-red-200' : 'bg-gray-50 border-gray-200'} border rounded-lg p-4 mb-6 flex items-center space-x-3`}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5 }}
                            >
                                <div className="relative w-16 h-16 flex-shrink-0">
                                    <Image
                                        src={product.thumbnail}
                                        alt={product.title}
                                        fill
                                        className="object-cover rounded-lg"
                                    />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h3 className="font-medium text-gray-900 truncate">{product.title}</h3>
                                    <p className="text-sm text-gray-600">{product.brand}</p>
                                    <p className="text-sm font-medium text-purple-600">
                                        {product.discountPercentage ? (
                                            <>
                                                <span className="line-through text-gray-400 mr-2">
                                                    ₺{product.price.toFixed(2)}
                                                </span>
                                                ₺{(product.price * (1 - product.discountPercentage / 100)).toFixed(2)}
                                            </>
                                        ) : (
                                            `₺${product.price.toFixed(2)}`
                                        )}
                                    </p>
                                </div>
                            </motion.div>
                        )}

                        {/* Action Buttons */}
                        <motion.div
                            className="flex flex-col space-y-3"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                        >
                            <Link href="/account?tab=favorites">
                                <motion.button
                                    className={`w-full ${isAdded ? 'bg-gradient-to-r from-red-600 to-pink-600' : 'bg-gradient-to-r from-gray-600 to-gray-700'} text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300`}
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={onClose}
                                >
                                    Favorilerime Git
                                </motion.button>
                            </Link>

                            <motion.button
                                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={onClose}
                            >
                                Alışverişe Devam Et
                            </motion.button>
                        </motion.div>

                        {/* Close Button */}
                        <motion.button
                            onClick={onClose}
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.7 }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </motion.button>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 