'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
// Cart item tipi - product detail sayfasından gelen veri yapısı
interface CartItem {
    id: number;
    title: string;
    price: number; // Orijinal fiyat
    discountedPrice: number; // İndirimli fiyat
    thumbnail: string;
    brand: string;
    membershipDiscount: number; // Üye indirimi yüzdesi
    extraDiscount: number; // Ekstra indirim yüzdesi
    pvPoints: number; // PV puanları (tek ürün için)
    cvPoints: number; // CV puanları (tek ürün için)
    spPoints: number; // SP puanları (tek ürün için)
    quantity: number;
}

interface AddToCartModalProps {
    isOpen: boolean;
    onClose: () => void;
    product?: CartItem | null;
    quantity?: number;
}

export default function AddToCartModal({ isOpen, onClose, product, quantity = 1 }: AddToCartModalProps) {
    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={onClose}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Success Icon */}
                        <motion.div
                            className="text-center mb-6"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                        >
                            <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                <motion.svg
                                    className="w-10 h-10 text-green-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    initial={{ pathLength: 0 }}
                                    animate={{ pathLength: 1 }}
                                    transition={{ delay: 0.4, duration: 0.6 }}
                                >
                                    <motion.path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17"
                                        initial={{ pathLength: 0 }}
                                        animate={{ pathLength: 1 }}
                                        transition={{ delay: 0.4, duration: 0.6 }}
                                    />
                                </motion.svg>
                            </div>

                            <motion.h2
                                className="text-2xl font-bold text-gray-800 mb-2"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                Sepete Eklendi!
                            </motion.h2>

                            <motion.p
                                className="text-gray-600"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.4 }}
                            >
                                Ürün başarıyla sepetinize eklendi.
                            </motion.p>
                        </motion.div>

                        {/* Product Details */}
                        {product && (
                            <motion.div
                                className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-4 mb-6"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5 }}
                            >
                                <div className="flex items-center space-x-4">
                                    <div className="relative w-16 h-16 flex-shrink-0">
                                        <Image
                                            src={product.thumbnail}
                                            alt={product.title}
                                            fill
                                            className="object-cover rounded-lg"
                                        />
                                        {/* Quantity Badge */}
                                        <motion.div
                                            className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full w-7 h-7 flex items-center justify-center text-xs font-bold shadow-lg"
                                            initial={{ scale: 0 }}
                                            animate={{ scale: 1 }}
                                            transition={{ delay: 0.6, type: "spring", stiffness: 200 }}
                                        >
                                            {quantity}
                                        </motion.div>
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-gray-800 text-sm mb-1">
                                            {product.title}
                                        </h3>
                                        <p className="text-xs text-gray-500 mb-2">{product.brand}</p>
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center space-x-2">
                                                {(product.membershipDiscount > 0 || product.extraDiscount > 0) ? (
                                                    <>
                                                        <p className="text-purple-600 font-bold text-lg">
                                                            {product.discountedPrice.toFixed(2)} ₺
                                                        </p>
                                                        <p className="text-gray-500 line-through text-sm">
                                                            {product.price.toFixed(2)} ₺
                                                        </p>
                                                    </>
                                                ) : (
                                                    <p className="text-purple-600 font-bold text-lg">
                                                        {product.discountedPrice.toFixed(2)} ₺
                                                    </p>
                                                )}
                                            </div>
                                            <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium">
                                                {quantity} adet
                                            </div>
                                        </div>
                                        {/* İndirim Badge'leri */}
                                        <div className="flex items-center space-x-2 mt-1">
                                            {/* Üye İndirimi Badge */}
                                            {product.membershipDiscount > 0 && (
                                                <span className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                    %{product.membershipDiscount.toFixed(0)} Üye İndirimi
                                                </span>
                                            )}

                                            {/* Ekstra İndirim Badge */}
                                            {product.extraDiscount > 0 && (
                                                <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                    %{product.extraDiscount.toFixed(0)} İndirim
                                                </span>
                                            )}
                                        </div>

                                        {/* Puan Bilgileri */}
                                        <div className="flex items-center gap-1 mt-2 flex-wrap">
                                            {/* PV Puanları */}
                                            {product.pvPoints > 0 && (
                                                <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 713.138-3.138z" />
                                                    </svg>
                                                    PV {(product.pvPoints * quantity).toFixed(0)} Puan
                                                </div>
                                            )}

                                            {/* CV Puanları */}
                                            {product.cvPoints > 0 && (
                                                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 714.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 713.138-3.138z" />
                                                    </svg>
                                                    CV {(product.cvPoints * quantity).toFixed(0)} Puan
                                                </div>
                                            )}

                                            {/* SP Puanları */}
                                            {product.spPoints > 0 && (
                                                <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 714.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 713.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 713.138-3.138z" />
                                                    </svg>
                                                    SP {(product.spPoints * quantity).toFixed(0)} Puan
                                                </div>
                                            )}
                                        </div>

                                        {/* Toplam Fiyat */}
                                        <div className="mt-3 pt-3 border-t border-purple-200">
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-700">Toplam:</span>
                                                <span className="text-lg font-bold text-purple-700">
                                                    {(product.discountedPrice * quantity).toFixed(2)} ₺
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </motion.div>
                        )}

                        {/* Action Buttons */}
                        <motion.div
                            className="flex flex-col space-y-3"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                        >
                            <Link href="/cart">
                                <motion.button
                                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-4 px-6 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={onClose}
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
                                    </svg>
                                    <span>Sepeti Görüntüle</span>
                                </motion.button>
                            </Link>

                            <motion.button
                                className="w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 transition-all duration-300 flex items-center justify-center space-x-2"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={onClose}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                                </svg>
                                <span>Alışverişe Devam Et</span>
                            </motion.button>
                        </motion.div>

                        {/* Close Button */}
                        <motion.button
                            onClick={onClose}
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.7 }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </motion.button>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 