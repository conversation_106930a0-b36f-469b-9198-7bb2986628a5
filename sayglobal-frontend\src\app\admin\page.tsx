'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { adminStats } from '@/data/mocks/admin';
import {
    Users,
    ShoppingCart,
    TrendingUp,
    Package,
    Eye,
    UserPlus,
    Calendar,
    BarChart3,
    Shield,
    Settings,
    Database,
    Activity
} from 'lucide-react';
import Link from 'next/link';

const AdminPanel = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const [stats] = useState(adminStats);

    useEffect(() => {
        if (!isLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, isLoading, router]);

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                Yönetici Paneli
                            </h1>
                            <p className="text-gray-600">
                                Say Global platformu yönetim merkezi
                            </p>
                        </div>
                        <div className="flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg">
                            <Shield className="h-5 w-5 text-red-600" />
                            <span className="text-red-800 font-medium">Admin Erişimi</span>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

                    {/* Toplam Kullanıcılar */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Kullanıcı</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
                                <p className="text-sm text-blue-600">+{stats.newUsersThisMonth} bu ay</p>
                            </div>
                            <div className="bg-blue-100 p-3 rounded-full">
                                <Users className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </motion.div>

                    {/* Toplam Siparişler */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Sipariş</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalOrders.toLocaleString()}</p>
                                <p className="text-sm text-green-600">+{stats.ordersThisMonth} bu ay</p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-full">
                                <ShoppingCart className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </motion.div>

                    {/* Toplam Gelir */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Gelir</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                                <p className="text-sm text-purple-600">{formatCurrency(stats.revenueThisMonth)} bu ay</p>
                            </div>
                            <div className="bg-purple-100 p-3 rounded-full">
                                <TrendingUp className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </motion.div>

                    {/* Toplam Ürünler */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Ürün</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
                                <p className="text-sm text-orange-600">Aktif ürünler</p>
                            </div>
                            <div className="bg-orange-100 p-3 rounded-full">
                                <Package className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

                    {/* Yönetim İşlemleri */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                    >
                        <div className="flex items-center mb-6">
                            <Settings className="h-6 w-6 text-gray-600 mr-3" />
                            <h3 className="text-xl font-semibold text-gray-900">Yönetim İşlemleri</h3>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                            <Link
                                href="/admin/users"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                            >
                                <Users className="h-8 w-8 text-blue-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">Kullanıcılar</span>
                            </Link>
                            <Link
                                href="/admin/orders"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors group"
                            >
                                <ShoppingCart className="h-8 w-8 text-green-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-green-700">Siparişler</span>
                            </Link>
                            <Link
                                href="/admin/products"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors group"
                            >
                                <Package className="h-8 w-8 text-orange-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-orange-700">Ürünler</span>
                            </Link>
                            <Link
                                href="/admin/pending-products"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-colors group"
                            >
                                <Eye className="h-8 w-8 text-yellow-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-yellow-700">Ürün Onayları</span>
                            </Link>
                            <Link
                                href="/admin/dealership-applications"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors group"
                            >
                                <UserPlus className="h-8 w-8 text-purple-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-purple-700">Satıcı Başvuruları</span>
                            </Link>
                            <Link
                                href="/admin/reports"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                            >
                                <BarChart3 className="h-8 w-8 text-blue-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">Raporlar</span>
                            </Link>
                            <Link
                                href="/admin/category-management"
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group"
                            >
                                <Database className="h-8 w-8 text-indigo-600 mb-2 group-hover:scale-110 transition-transform" />
                                <span className="text-sm font-medium text-gray-700 group-hover:text-indigo-700">Kategori Yönetimi</span>
                            </Link>
                        </div>
                    </motion.div>

                    {/* Son Aktiviteler */}
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 }}
                    >
                        <div className="flex items-center mb-6">
                            <Activity className="h-6 w-6 text-gray-600 mr-3" />
                            <h3 className="text-xl font-semibold text-gray-900">Son Aktiviteler</h3>
                        </div>
                        <div className="space-y-4">
                            <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                                <UserPlus className="h-5 w-5 text-blue-600 mr-3" />
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-700">Yeni üye kaydı</p>
                                    <p className="text-xs text-gray-500"><EMAIL> - 10 dakika önce</p>
                                </div>
                            </div>
                            <div className="flex items-center p-3 bg-green-50 rounded-lg">
                                <ShoppingCart className="h-5 w-5 text-green-600 mr-3" />
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-700">Yeni sipariş</p>
                                    <p className="text-xs text-gray-500">ORD-2024-1001 - 25 dakika önce</p>
                                </div>
                            </div>
                            <div className="flex items-center p-3 bg-orange-50 rounded-lg">
                                <Package className="h-5 w-5 text-orange-600 mr-3" />
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-700">Stok güncellemesi</p>
                                    <p className="text-xs text-gray-500">Premium Collagen Plus - 1 saat önce</p>
                                </div>
                            </div>
                            <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                                <Eye className="h-5 w-5 text-purple-600 mr-3" />
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-700">Admin girişi</p>
                                    <p className="text-xs text-gray-500"><EMAIL> - 2 saat önce</p>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Monthly Performance Chart */}
                <motion.div
                    className="bg-white rounded-xl shadow-lg p-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                >
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h3 className="text-xl font-semibold text-gray-900">Aylık Performans</h3>
                            <p className="text-sm text-gray-600">Son 6 aylık platform performansı</p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Kullanıcılar</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Siparişler</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Gelir (₺)</span>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-4">
                        {stats.monthlyStats.map((month, index) => (
                            <div key={index} className="flex items-center justify-between">
                                <div className="flex items-center space-x-4 w-32">
                                    <span className="text-sm font-medium text-gray-700">{month.month}</span>
                                </div>
                                <div className="flex-1 space-y-2">
                                    <div className="flex items-center space-x-2">
                                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-blue-500 h-2 rounded-full"
                                                style={{ width: `${(month.users / 100) * 100}%` }}
                                            ></div>
                                        </div>
                                        <span className="text-xs text-gray-600 w-12">{month.users}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-green-500 h-2 rounded-full"
                                                style={{ width: `${(month.orders / 250) * 100}%` }}
                                            ></div>
                                        </div>
                                        <span className="text-xs text-gray-600 w-12">{month.orders}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-purple-500 h-2 rounded-full"
                                                style={{ width: `${(month.revenue / 20000) * 100}%` }}
                                            ></div>
                                        </div>
                                        <span className="text-xs text-gray-600 w-12">{formatCurrency(month.revenue).replace('₺', '').trim()}k</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </motion.div>

                {/* System Status */}
                <motion.div
                    className="mt-8 bg-white rounded-xl shadow-lg p-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                >
                    <div className="flex items-center mb-6">
                        <Database className="h-6 w-6 text-gray-600 mr-3" />
                        <h3 className="text-xl font-semibold text-gray-900">Sistem Durumu</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">Platform</h4>
                            <p className="text-sm text-green-600">Aktif</p>
                        </div>
                        <div className="text-center">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">Veritabanı</h4>
                            <p className="text-sm text-green-600">Aktif</p>
                        </div>
                        <div className="text-center">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">API</h4>
                            <p className="text-sm text-green-600">Aktif</p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
};

export default AdminPanel; 