"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"718de0b0a05c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXERlc2t0b3BcXFNheWdsb2JhbFxcc2F5Z2xvYmFsLWZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MThkZTBiMGEwNWNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addressService: () => (/* binding */ addressService),\n/* harmony export */   cartService: () => (/* binding */ cartService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   productService: () => (/* binding */ productService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var axios_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios-retry */ \"(app-pages-browser)/./node_modules/axios-retry/dist/esm/index.js\");\n/* harmony import */ var _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants/apiEndpoints */ \"(app-pages-browser)/./src/constants/apiEndpoints.ts\");\n/* harmony import */ var _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/networkStore */ \"(app-pages-browser)/./src/stores/networkStore.ts\");\n\n\n\n\n// API Base URL - Development'te proxy kullan, production'da direkt API\nconst API_BASE_URL =  true ? '' // Development'te Next.js proxy kullanır\n : 0;\n// Cookie'den belirli bir değeri okuma utility fonksiyonu\nconst getCookieValue = (name)=>{\n    if (typeof document === 'undefined') return null; // SSR kontrolü\n    const value = \"; \".concat(document.cookie);\n    const parts = value.split(\"; \".concat(name, \"=\"));\n    if (parts.length === 2) {\n        var _parts_pop;\n        const cookieValue = (_parts_pop = parts.pop()) === null || _parts_pop === void 0 ? void 0 : _parts_pop.split(';').shift();\n        return cookieValue || null;\n    }\n    return null;\n};\n// Axios instance oluştur - HTTP-only cookies için withCredentials: true\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    },\n    timeout: 30000,\n    withCredentials: true\n});\n// Yeniden deneme mekanizmasını yapılandır\n(0,axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(apiClient, {\n    retries: 3,\n    retryCondition: (error)=>{\n        var _error_response;\n        // Sadece ağ hatalarında veya sunucu tarafı geçici hatalarında yeniden dene.\n        // 401/403 gibi client hatalarında yeniden deneme, çünkü bu auth akışını geciktirir.\n        if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) && error.response.status >= 400 && error.response.status < 500) {\n            return false;\n        }\n        return axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isNetworkError(error) || axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isIdempotentRequestError(error);\n    },\n    retryDelay: (retryCount, error)=>{\n        console.warn(\"[axios-retry] Request failed: \".concat(error.message, \". Retry attempt #\").concat(retryCount, \"...\"));\n        // Her denemede bekleme süresini artır (1s, 2s, 4s)\n        return Math.pow(2, retryCount - 1) * 1000;\n    }\n});\n// Refresh işlemi devam ediyor mu kontrolü\nlet isRefreshing = false;\nlet failedQueue = [];\nconst processQueue = function(error) {\n    let token = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    failedQueue.forEach((param)=>{\n        let { resolve, reject } = param;\n        if (error) {\n            reject(error);\n        } else {\n            resolve(token);\n        }\n    });\n    failedQueue = [];\n};\n// Herkese açık ve engellenmemesi gereken endpoint'ler\nconst publicEndpoints = [\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REGISTER,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGOUT\n];\n// Request interceptor - Cookie'leri otomatik gönder (HttpOnly cookie'ler için)\napiClient.interceptors.request.use((config)=>{\n    var _config_method;\n    console.log('📡 API Request başlıyor:', (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(), config.url);\n    console.log('🍪 withCredentials:', config.withCredentials);\n    // HttpOnly cookie'ler JavaScript ile okunamaz, bu normal bir durum\n    // withCredentials: true olduğu için browser cookie'leri otomatik gönderir\n    // Backend HttpOnly cookie'yi kontrol edecek\n    // Eğer cookie JavaScript ile okunabiliyorsa Authorization header'ına da ekle\n    const accessToken = getCookieValue('AccessToken');\n    if (accessToken) {\n        config.headers.Authorization = \"Bearer \".concat(accessToken);\n        console.log('🔑 Authorization header eklendi (JS readable cookie)');\n    } else {\n        console.log('🔑 Cookie HttpOnly olabilir - browser otomatik gönderecek');\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor - Token yenileme ve hata yönetimi\napiClient.interceptors.response.use((response)=>{\n    // Başarılı response'larda network status'u online'a çek\n    const currentStatus = _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().status;\n    if (currentStatus !== 'online') {\n        console.log('✅ API başarılı - Network status online\\'a çekiliyor');\n        _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('online');\n    }\n    return response;\n}, async (error)=>{\n    var _originalRequest_url, _originalRequest_url1, _error_response, _error_response1;\n    const originalRequest = error.config;\n    // Login endpoint'inden gelen 401 hatasını token refresh döngüsünden çıkar\n    // User/Me endpoint'ini bu kontrolden kaldırıyoruz ki token süresi dolduğunda yenileyebilsin.\n    // REFRESH endpoint'ini de döngüden çıkarıyoruz ki sonsuz döngüye girmesin.\n    if ((((_originalRequest_url = originalRequest.url) === null || _originalRequest_url === void 0 ? void 0 : _originalRequest_url.includes(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN)) || ((_originalRequest_url1 = originalRequest.url) === null || _originalRequest_url1 === void 0 ? void 0 : _originalRequest_url1.includes(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN))) && ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        return Promise.reject(error);\n    }\n    // HİBRİT ÇÖZÜM - HttpOnly Cookie'ler için Güncellenmiş Yaklaşım:\n    // HttpOnly cookie'ler JavaScript ile okunamaz, bu yüzden cookie varlığını kontrol edemeyiz.\n    // Bunun yerine, token yenileme isteğini gönderip sonucuna göre karar vereceğiz.\n    // Eğer refresh token yoksa, backend 401 döndürecek ve biz bunu yakalayacağız.\n    // 1. ÖNCELİK: Token yenileme mantığı\n    // 401 durumunda token yenileme - retry flag kontrolü ile döngüyü engelle\n    if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true; // Bu request'in retry edildiğini işaretle\n        if (isRefreshing) {\n            // Eğer refresh işlemi devam ediyorsa, kuyruğa ekle\n            return new Promise((resolve, reject)=>{\n                failedQueue.push({\n                    resolve,\n                    reject\n                });\n            }).then(()=>{\n                // Refresh tamamlandıktan sonra orijinal isteği tekrar gönder\n                return apiClient(originalRequest);\n            }).catch((err)=>{\n                return Promise.reject(err);\n            });\n        }\n        isRefreshing = true;\n        // Refresh token için özel retry mekanizması\n        const attemptRefresh = async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n            try {\n                const refreshResponse = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN);\n                return refreshResponse;\n            } catch (refreshError) {\n                var _refreshError_response;\n                // Retry koşulları:\n                // 1. Ağ hatası (timeout, connection error vb.)\n                // 2. 401 hatası ama henüz max retry'a ulaşmadıysak (timeout nedeniyle 401 olabilir)\n                const isNetworkError = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && !refreshError.response;\n                const is401Error = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && ((_refreshError_response = refreshError.response) === null || _refreshError_response === void 0 ? void 0 : _refreshError_response.status) === 401;\n                const shouldRetry = (isNetworkError || is401Error) && retryCount < 2;\n                if (shouldRetry) {\n                    console.log(\"\\uD83D\\uDD04 Refresh token denemesi \".concat(retryCount + 1, \"/3 başarısız (\").concat(isNetworkError ? 'Ağ hatası' : '401 - Timeout olabilir', \"). \").concat(retryCount < 1 ? 'Tekrar deneniyor...' : 'Son deneme yapılıyor...'));\n                    // Exponential backoff: 1s, 2s, 4s\n                    const delay = Math.pow(2, retryCount) * 1000;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return attemptRefresh(retryCount + 1);\n                }\n                // Max retry'a ulaştık veya kesin bir hata aldık\n                throw refreshError;\n            }\n        };\n        try {\n            // Refresh token çağrısı - retry mekanizması ile\n            const refreshResponse = await attemptRefresh();\n            // Tüm bekleyen istekleri başarılı olarak işaretle\n            processQueue(null);\n            isRefreshing = false;\n            // Başarılı refresh sonrası orijinal isteği tekrar gönder\n            return apiClient(originalRequest);\n        } catch (refreshError) {\n            var _refreshError_response;\n            // ÖNEMLİ: isRefreshing bayrağını mutlaka sıfırla\n            isRefreshing = false;\n            // 3 deneme sonrasında da 401 alıyorsak, bu gerçekten kullanıcının giriş yapmadığı anlamına gelir\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && ((_refreshError_response = refreshError.response) === null || _refreshError_response === void 0 ? void 0 : _refreshError_response.status) === 401) {\n                console.log('🚪 3 deneme sonrasında da 401 hatası. Kullanıcı gerçekten giriş yapmamış.');\n                // Tüm bekleyen istekleri orijinal hata ile reddet\n                processQueue(error, null);\n                // Orijinal hatayı döndür ki uygulama \"giriş yapmamış\" durumuna geçsin\n                return Promise.reject(error);\n            }\n            // Diğer hatalar için normal işlem\n            processQueue(refreshError, null);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && !refreshError.response) {\n                // 3 deneme sonrasında hala ağ hatası alıyorsak, bu ciddi bir bağlantı sorunu\n                console.log('🔌 3 deneme sonrasında refresh token yenilenemedi. Ağ bağlantısı sorunlu.');\n                _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('reconnecting');\n            } else {\n                console.log('💥 Beklenmedik hata sonrası auth:force-logout event gönderiliyor');\n                window.dispatchEvent(new CustomEvent('auth:force-logout'));\n            }\n            return Promise.reject(refreshError);\n        }\n    }\n    // 2. ÖNCELİK: Genel ağ hatalarını yakalama\n    // Eğer hata 401 değilse ve bir ağ hatasıysa (sunucudan yanıt yoksa),\n    // bu genel bir internet kesintisidir.\n    if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error) && !error.response) {\n        console.log('🔌 Genel ağ hatası algılandı. Yeniden bağlanma moduna geçiliyor.');\n        _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('reconnecting');\n        // Burada hatayı yutup banner'ın çalışmasına izin veriyoruz.\n        // Component'in tekrar denemesi için hatayı reject etmiyoruz ki sürekli error state'i göstermesin.\n        // Bunun yerine, bir daha asla çözülmeyecek bir promise döndürerek isteği askıda bırakıyoruz.\n        return new Promise(()=>{});\n    }\n    // Diğer tüm hatalar (500, 404, 403 vb.) normal şekilde componente geri dönsün.\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n// ===========================================\n// ADDRESS SERVICES\n// ===========================================\nconst addressService = {\n    // Kullanıcının adreslerini listele\n    async getAddresses () {\n        try {\n            console.log('📍 Adresler alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.USER_ADDRESSES);\n            console.log('📍 API Response:', response);\n            console.log('📍 Response data:', response.data);\n            console.log('📍 Response status:', response.status);\n            console.log('📍 Response data type:', typeof response.data);\n            console.log('📍 Response data is array:', Array.isArray(response.data));\n            // Eğer response.data bir object ise, içindeki property'leri kontrol et\n            if (typeof response.data === 'object' && response.data !== null) {\n                console.log('📍 Response data keys:', Object.keys(response.data));\n                console.log('📍 Response data values:', Object.values(response.data));\n                // Muhtemel nested yapıları kontrol et\n                if (response.data.data) {\n                    console.log('📍 Nested data found:', response.data.data);\n                    console.log('📍 Nested data is array:', Array.isArray(response.data.data));\n                }\n                if (response.data.addresses) {\n                    console.log('📍 Addresses property found:', response.data.addresses);\n                    console.log('📍 Addresses is array:', Array.isArray(response.data.addresses));\n                }\n                if (response.data.result) {\n                    console.log('📍 Result property found:', response.data.result);\n                    console.log('📍 Result is array:', Array.isArray(response.data.result));\n                }\n            }\n            // Farklı response yapılarını dene\n            let addressData = response.data;\n            // Eğer response.data.data varsa ve array ise onu kullan\n            if (response.data.data && Array.isArray(response.data.data)) {\n                addressData = response.data.data;\n                console.log('📍 Using nested data array');\n            } else if (response.data.addresses && Array.isArray(response.data.addresses)) {\n                addressData = response.data.addresses;\n                console.log('📍 Using addresses property');\n            } else if (response.data.result && Array.isArray(response.data.result)) {\n                addressData = response.data.result;\n                console.log('📍 Using result property');\n            } else if (Array.isArray(response.data)) {\n                addressData = response.data;\n                console.log('📍 Using direct response data');\n            } else {\n                console.warn('📍 No valid array found in response, using empty array');\n                addressData = [];\n            }\n            console.log('📍 Final address data:', addressData);\n            console.log('📍 Final address data type:', typeof addressData);\n            console.log('📍 Final address data is array:', Array.isArray(addressData));\n            console.log('📍 Final address count:', addressData.length);\n            return {\n                success: true,\n                data: addressData\n            };\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error('❌ Adresler alınırken hata:', error);\n            console.error('❌ Error response:', error.response);\n            console.error('❌ Error data:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            return {\n                success: false,\n                error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Adresler alınırken bir hata oluştu',\n                data: [] // Hata durumunda boş array döndür\n            };\n        }\n    },\n    // Yeni adres ekle\n    async createAddress (addressData) {\n        try {\n            console.log('➕ Yeni adres oluşturuluyor:', addressData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_ADDRESS, addressData);\n            console.log('✅ Adres oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Adres eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Adres eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Adres sil\n    async deleteAddress (addressId, userId) {\n        try {\n            console.log('🗑️ Adres siliniyor:', {\n                addressId,\n                userId\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_ADDRESS, {\n                addressId,\n                userId\n            });\n            console.log('✅ Adres silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Adres silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Adres silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Varsayılan adres ayarla\n    async setDefaultAddress (addressId) {\n        try {\n            console.log('⭐ Varsayılan adres ayarlanıyor:', {\n                addressId\n            });\n            const response = await apiClient.post(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.SET_DEFAULT_ADDRESS, \"?addressId=\").concat(addressId));\n            console.log('✅ Varsayılan adres ayarlama başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Varsayılan adres ayarlanırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Varsayılan adres ayarlanırken bir hata oluştu'\n            };\n        }\n    }\n};\n// ===========================================\n// USER SERVICES\n// ===========================================\nconst userService = {\n    // Kullanıcı profilini güncelle\n    async updateProfile (profileData) {\n        try {\n            console.log('👤 Profil güncelleniyor:', profileData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PROFILE, profileData);\n            console.log('✅ Profil güncelleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Profil güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Profil güncellenirken bir hata oluştu'\n            };\n        }\n    },\n    // Admin kullanıcıları getir (filtreleme ve sayfalama ile)\n    async getUsers (params) {\n        try {\n            // Request body oluştur\n            const requestBody = {\n                page: params.page || 1,\n                pageSize: params.pageSize || 10,\n                search: params.search || \"\"\n            };\n            // Sadece roleId 0'dan farklıysa ekle\n            if (params.roleId && params.roleId > 0) {\n                requestBody.roleId = params.roleId;\n            }\n            // Sadece isActive parametresi gönderildiyse ekle\n            if (params.isActive !== undefined) {\n                requestBody.isActive = params.isActive;\n            }\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_USERS, requestBody);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcılar alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcılar alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kullanıcı rol sayılarını getir\n    async getUserRoleCounts () {\n        try {\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_USER_ROLE_COUNTS);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı rol sayıları alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcı rol sayıları alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Kullanıcı indirim oranını getir\n    async getDiscountRate () {\n        try {\n            console.log('💰 Kullanıcı indirim oranı alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_DISCOUNT_RATE);\n            console.log('✅ Kullanıcı indirim oranı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı indirim oranı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcı indirim oranı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Sepet tipini güncelle (customer price toggle)\n    async updateCartType () {\n        try {\n            console.log('🛒 Sepet tipi güncelleniyor...');\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_CART_TYPE);\n            console.log('✅ Sepet tipi başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet tipi güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Sepet tipi güncellenirken bir hata oluştu',\n                data: null\n            };\n        }\n    }\n};\n// ===========================================\n// PRODUCT SERVICES\n// ===========================================\nconst productService = {\n    // Markaları getir\n    async getBrands () {\n        try {\n            console.log('🏷️ Markalar alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_BRANDS);\n            console.log('✅ Markalar başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Markalar alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Markalar alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Markaya göre kategorileri getir\n    async getCategoriesByBrand (brandId) {\n        try {\n            console.log('📂 Kategoriler alınıyor, brandId:', brandId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATEGORIES_BY_BRAND, \"/\").concat(brandId));\n            console.log('✅ Kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kategoriye göre alt kategorileri getir\n    async getSubCategories (categoryId) {\n        try {\n            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORIES, \"/\").concat(categoryId, \"/subcategories\"));\n            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Alt kategori özelliklerini getir\n    async getSubCategoryFeatures (subCategoryId) {\n        try {\n            console.log('🔧 Alt kategori özellikleri alınıyor, subCategoryId:', subCategoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORY_FEATURES, \"/\").concat(subCategoryId));\n            console.log('✅ Alt kategori özellikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategori özellikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategori özellikleri alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Özellik değerlerini getir\n    async getFeatureValues (definitionId) {\n        try {\n            console.log('🏷️ Özellik değerleri alınıyor, definitionId:', definitionId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_FEATURE_VALUES, \"/\").concat(definitionId));\n            console.log('✅ Özellik değerleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Özellik değerleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Özellik değerleri alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Tüm özellik tanımlarını getir\n    async getAllFeatureDefinitions () {\n        try {\n            console.log('🔍 Tüm özellik tanımları alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ALL_FEATURE_DEFINITIONS);\n            console.log('✅ Tüm özellik tanımları başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Tüm özellik tanımları alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Tüm özellik tanımları alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Yeni ürün ekle (tam ürün - varyantlarla birlikte)\n    async createFullProduct (productData) {\n        try {\n            console.log('➕ Tam ürün oluşturuluyor:', productData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_FULL_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Tam ürün oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Tam ürün oluşturulurken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün oluşturulurken bir hata oluştu'\n            };\n        }\n    },\n    // Satıcı ürünü ekle (dealership product - PV/CV/SP olmadan)\n    async createDealershipProduct (productData) {\n        try {\n            console.log('➕ Satıcı ürünü oluşturuluyor:', productData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_DEALERSHIP_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Satıcı ürünü oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Satıcı ürünü oluşturulurken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün oluşturulurken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görseli ekle\n    async addProductImage (imageData) {\n        try {\n            console.log('🖼️ Ürün görseli ekleniyor:', imageData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_PRODUCT_IMAGE, imageData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Ürün görseli ekleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görselini sil\n    async deleteProductImage (imageId) {\n        try {\n            console.log('🗑️ Ürün görseli siliniyor, imageId:', imageId);\n            const response = await apiClient.delete(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PRODUCT_IMAGE, \"/\").concat(imageId));\n            console.log('✅ Ürün görseli silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görselini değiştir\n    async replaceProductImage (imageId, newImageFile) {\n        try {\n            console.log('🔄 Ürün görseli değiştiriliyor, imageId:', imageId);\n            const formData = new FormData();\n            formData.append('file', newImageFile);\n            const response = await apiClient.put(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REPLACE_PRODUCT_IMAGE, \"/\").concat(imageId), formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Ürün görseli değiştirme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli değiştirilirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli değiştirilirken bir hata oluştu'\n            };\n        }\n    },\n    // Admin ürünlerini getir\n    async getAdminProducts (params) {\n        try {\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ADMIN_PRODUCTS, {\n                params\n            });\n            return Array.isArray(response.data) ? response.data : [];\n        } catch (error) {\n            console.error('❌ Admin ürünleri alınırken hata:', error);\n            return [];\n        }\n    },\n    // Kullanıcıya ait ürünleri getir\n    async getMyProducts (params) {\n        try {\n            console.log('📦 Kullanıcıya ait ürünler alınıyor:', params);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_MY_PRODUCTS, params);\n            console.log('✅ Kullanıcıya ait ürünler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcıya ait ürünler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürünler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Dealership ürün detayını getir\n    async getDealershipProductDetail (productId) {\n        try {\n            console.log('📦 Dealership ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_DEALERSHIP_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Dealership ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Dealership ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Ürünü sil\n    async deleteProduct (productId) {\n        try {\n            console.log('🗑️ Ürün siliniyor, productId:', productId);\n            // API GET metodu ve query parametresi beklediği için ona uygun istek atıyoruz.\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PRODUCT, {\n                params: {\n                    productId\n                }\n            });\n            console.log('✅ Ürün silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürünü güncelle (tam ürün - varyantlarla birlikte)\n    async updateFullProduct (productData) {\n        try {\n            console.log('🔄 Tam ürün güncelleniyor:', productData);\n            // FormData içeriğini detaylı logla\n            console.log('📋 FormData içeriği:');\n            for (let [key, value] of productData.entries()){\n                console.log(\"\".concat(key, \":\"), value);\n            }\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_FULL_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Tam ürün güncelleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2, _error_response_data1, _error_response3;\n            console.error('❌ Tam ürün güncellenirken hata:', error);\n            console.error('❌ Hata detayları:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error('❌ HTTP Status:', (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            // Hata mesajını daha detaylı döndür\n            const errorMessage = ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data1 = _error_response3.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.title) || error.message || 'Ürün güncellenirken bir hata oluştu';\n            throw new Error(errorMessage);\n        }\n    },\n    // Admin ürün istatistiklerini getir\n    async getAdminProductStatistics () {\n        try {\n            console.log('📊 Admin ürün istatistikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ADMIN_PRODUCT_STATISTICS);\n            console.log('✅ Admin ürün istatistikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Admin ürün istatistikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'İstatistikler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Kullanıcı ürün istatistiklerini getir\n    async getMyProductStatistics () {\n        try {\n            console.log('📊 Kullanıcı ürün istatistikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_MY_PRODUCT_STATISTICS);\n            console.log('✅ Kullanıcı ürün istatistikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data.data // API response'u data wrapper'ı içinde geliyor\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı ürün istatistikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'İstatistikler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Basit ürün güncelleme (dealership için)\n    async updateSimpleProduct (productFormData) {\n        console.log('🔄 API Service: Basit ürün güncelleniyor...');\n        console.log('🔗 Endpoint:', _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT);\n        // FormData içeriğini logla\n        console.log('📋 API Service: FormData contents:');\n        for (let [key, value] of productFormData.entries()){\n            if (value instanceof File) {\n                console.log(\"  \".concat(key, \": File(\").concat(value.name, \", \").concat(value.size, \" bytes, \").concat(value.type, \")\"));\n            } else {\n                console.log(\"  \".concat(key, \": \").concat(value));\n            }\n        }\n        const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT, productFormData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        console.log('✅ API Service: Basit ürün başarıyla güncellendi');\n        console.log('📄 Response status:', response.status);\n        console.log('📄 Response data:', response.data);\n        return response.data;\n    },\n    // Ürün detayını getir\n    async getProductDetail (productId) {\n        try {\n            console.log('📦 Ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Catalog ürün detayını getir (public)\n    async getCatalogProductDetail (productId) {\n        try {\n            console.log('📦 Catalog ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATALOG_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Catalog ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Catalog ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Ürün durumunu güncelle (onay/red)\n    async updateProductStatus (productId, isApproved, message) {\n        try {\n            console.log('🔄 Ürün durumu güncelleniyor:', {\n                productId,\n                isApproved,\n                message\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PRODUCT_STATUS, {\n                productId,\n                isApproved,\n                message\n            });\n            console.log('✅ Ürün durumu başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün durumu güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün durumu güncellenirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün admin notunu getir\n    async getProductMessage (productId) {\n        try {\n            console.log('📝 Ürün admin notu alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_PRODUCT_MESSAGE, \"/\").concat(productId));\n            console.log('✅ Ürün admin notu başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error('❌ Ürün admin notu alınırken hata:', error);\n            // 404 hatası ise kayıt yok demektir\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                return {\n                    success: true,\n                    data: null\n                };\n            }\n            // Diğer hatalar için false döndür\n            return {\n                success: false,\n                error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün admin notu alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Tüm kategorileri getir (public)\n    async getCategories () {\n        try {\n            console.log('📂 Kategoriler alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATEGORIES);\n            console.log('✅ Kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kategoriye göre alt kategorileri getir (public)\n    async getSubCategoriesByCategory (categoryId) {\n        try {\n            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORIES_BY_CATEGORY.replace('{categoryId}', categoryId.toString())));\n            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Ürünleri filtrele (public)\n    async filterProducts (filterRequest) {\n        try {\n            console.log('🔍 Ürünler filtreleniyor:', filterRequest);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.FILTER_PRODUCTS, filterRequest);\n            console.log('✅ Ürünler başarıyla filtrelendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürünler filtrelenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürünler filtrelenirken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Reference data getir (public)\n    async getReferenceData () {\n        try {\n            console.log('📋 Reference data alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_REFERENCE_DATA);\n            console.log('✅ Reference data başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Reference data alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Reference data alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    }\n};\nconst cartService = {\n    // Sepete ürün ekle\n    async addToCart (productVariantId, quantity, isCustomerPrice) {\n        try {\n            console.log('🛒 Sepete ürün ekleniyor:', {\n                productVariantId,\n                quantity,\n                isCustomerPrice\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_TO_CART, {\n                productVariantId,\n                quantity,\n                isCustomerPrice\n            });\n            console.log('✅ Ürün sepete başarıyla eklendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepete ürün eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün sepete eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Sepet içeriklerini getir\n    async getCartItems () {\n        try {\n            console.log('🛒 Sepet içerikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CART_ITEMS);\n            console.log('✅ Sepet içerikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet içerikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Sepet içerikleri alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Sepetten ürün çıkar\n    async removeFromCart (productVariantId) {\n        try {\n            console.log('🗑️ Sepetten ürün çıkarılıyor:', {\n                productVariantId\n            });\n            const url = \"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REMOVE_FROM_CART, \"/\").concat(productVariantId);\n            console.log('🔍 API URL:', url);\n            console.log('🔍 API_ENDPOINTS.REMOVE_FROM_CART:', _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REMOVE_FROM_CART);\n            const response = await apiClient.delete(url);\n            console.log('✅ Ürün sepetten başarıyla çıkarıldı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2;\n            console.error('❌ Sepetten ürün çıkarılırken hata:', error);\n            console.error('❌ Error response:', error.response);\n            console.error('❌ Error status:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status);\n            console.error('❌ Error data:', (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data);\n            return {\n                success: false,\n                error: ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün sepetten çıkarılırken bir hata oluştu'\n            };\n        }\n    },\n    // Sepet ürün miktarını güncelle\n    async updateCartQuantity (productVariantId, quantity) {\n        try {\n            console.log('🔄 Sepet ürün miktarı güncelleniyor:', {\n                productVariantId,\n                quantity\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_CART_QUANTITY, {\n                productVariantId,\n                quantity\n            });\n            console.log('✅ Sepet ürün miktarı başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet ürün miktarı güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün miktarı güncellenirken bir hata oluştu'\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9hcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ1c7QUFDb0I7QUFDRDtBQUd4RCx1RUFBdUU7QUFDdkUsTUFBTUksZUFBZUMsS0FBc0MsR0FDckQsR0FBRyx3Q0FBd0M7R0FDMUNBLENBQWlFO0FBRXhFLHlEQUF5RDtBQUN6RCxNQUFNRyxpQkFBaUIsQ0FBQ0M7SUFDcEIsSUFBSSxPQUFPQyxhQUFhLGFBQWEsT0FBTyxNQUFNLGVBQWU7SUFFakUsTUFBTUMsUUFBUSxLQUFxQixPQUFoQkQsU0FBU0UsTUFBTTtJQUNsQyxNQUFNQyxRQUFRRixNQUFNRyxLQUFLLENBQUMsS0FBVSxPQUFMTCxNQUFLO0lBQ3BDLElBQUlJLE1BQU1FLE1BQU0sS0FBSyxHQUFHO1lBQ0FGO1FBQXBCLE1BQU1HLGVBQWNILGFBQUFBLE1BQU1JLEdBQUcsZ0JBQVRKLGlDQUFBQSxXQUFhQyxLQUFLLENBQUMsS0FBS0ksS0FBSztRQUNqRCxPQUFPRixlQUFlO0lBQzFCO0lBQ0EsT0FBTztBQUNYO0FBRUEsd0VBQXdFO0FBQ3hFLE1BQU1HLFlBQVluQiw2Q0FBS0EsQ0FBQ29CLE1BQU0sQ0FBQztJQUMzQkMsU0FBU2pCO0lBQ1RrQixTQUFTO1FBQ0wsZ0JBQWdCO1FBQ2hCLFVBQVU7SUFDZDtJQUNBQyxTQUFTO0lBQ1RDLGlCQUFpQjtBQUNyQjtBQUVBLDBDQUEwQztBQUMxQ3ZCLHVEQUFVQSxDQUFDa0IsV0FBVztJQUNsQk0sU0FBUztJQUNUQyxnQkFBZ0IsQ0FBQ0M7WUFHVEE7UUFGSiw0RUFBNEU7UUFDNUUsb0ZBQW9GO1FBQ3BGLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHNDQUFBQSxnQkFBZ0JFLE1BQU0sS0FBSUYsTUFBTUMsUUFBUSxDQUFDQyxNQUFNLElBQUksT0FBT0YsTUFBTUMsUUFBUSxDQUFDQyxNQUFNLEdBQUcsS0FBSztZQUN2RixPQUFPO1FBQ1g7UUFDQSxPQUFPNUIsa0VBQXlCLENBQUMwQixVQUFVMUIsNEVBQW1DLENBQUMwQjtJQUNuRjtJQUNBSyxZQUFZLENBQUNDLFlBQVlOO1FBQ3JCTyxRQUFRQyxJQUFJLENBQUMsaUNBQWtFRixPQUFqQ04sTUFBTVMsT0FBTyxFQUFDLHFCQUE4QixPQUFYSCxZQUFXO1FBQzFGLG1EQUFtRDtRQUNuRCxPQUFPSSxLQUFLQyxHQUFHLENBQUMsR0FBR0wsYUFBYSxLQUFLO0lBQ3pDO0FBQ0o7QUFFQSwwQ0FBMEM7QUFDMUMsSUFBSU0sZUFBZTtBQUNuQixJQUFJQyxjQUdDLEVBQUU7QUFFUCxNQUFNQyxlQUFlLFNBQUNkO1FBQVllLHlFQUF1QjtJQUNyREYsWUFBWUcsT0FBTyxDQUFDO1lBQUMsRUFBRUMsT0FBTyxFQUFFQyxNQUFNLEVBQUU7UUFDcEMsSUFBSWxCLE9BQU87WUFDUGtCLE9BQU9sQjtRQUNYLE9BQU87WUFDSGlCLFFBQVFGO1FBQ1o7SUFDSjtJQUVBRixjQUFjLEVBQUU7QUFDcEI7QUFFQSxzREFBc0Q7QUFDdEQsTUFBTU0sa0JBQWtCO0lBQ3BCNUMsa0VBQWFBLENBQUM2QyxLQUFLO0lBQ25CN0Msa0VBQWFBLENBQUM4QyxRQUFRO0lBQ3RCOUMsa0VBQWFBLENBQUMrQyxhQUFhO0lBQzNCL0Msa0VBQWFBLENBQUNnRCxNQUFNO0NBR3ZCO0FBRUQsK0VBQStFO0FBQy9FL0IsVUFBVWdDLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQzlCLENBQUNDO1FBQzJDQTtJQUF4Q3BCLFFBQVFxQixHQUFHLENBQUMsNkJBQTRCRCxpQkFBQUEsT0FBT0UsTUFBTSxjQUFiRixxQ0FBQUEsZUFBZUcsV0FBVyxJQUFJSCxPQUFPSSxHQUFHO0lBQ2hGeEIsUUFBUXFCLEdBQUcsQ0FBQyx1QkFBdUJELE9BQU85QixlQUFlO0lBRXpELG1FQUFtRTtJQUNuRSwwRUFBMEU7SUFDMUUsNENBQTRDO0lBRTVDLDZFQUE2RTtJQUM3RSxNQUFNbUMsY0FBY25ELGVBQWU7SUFDbkMsSUFBSW1ELGFBQWE7UUFDYkwsT0FBT2hDLE9BQU8sQ0FBQ3NDLGFBQWEsR0FBRyxVQUFzQixPQUFaRDtRQUN6Q3pCLFFBQVFxQixHQUFHLENBQUM7SUFDaEIsT0FBTztRQUNIckIsUUFBUXFCLEdBQUcsQ0FBQztJQUNoQjtJQUVBLE9BQU9EO0FBQ1gsR0FDQSxDQUFDM0I7SUFDRyxPQUFPa0MsUUFBUWhCLE1BQU0sQ0FBQ2xCO0FBQzFCO0FBR0oseURBQXlEO0FBQ3pEUixVQUFVZ0MsWUFBWSxDQUFDdkIsUUFBUSxDQUFDeUIsR0FBRyxDQUMvQixDQUFDekI7SUFDRyx3REFBd0Q7SUFDeEQsTUFBTWtDLGdCQUFnQjNELGlFQUFlQSxDQUFDNEQsUUFBUSxHQUFHbEMsTUFBTTtJQUN2RCxJQUFJaUMsa0JBQWtCLFVBQVU7UUFDNUI1QixRQUFRcUIsR0FBRyxDQUFDO1FBQ1pwRCxpRUFBZUEsQ0FBQzRELFFBQVEsR0FBR0MsU0FBUyxDQUFDO0lBQ3pDO0lBQ0EsT0FBT3BDO0FBQ1gsR0FDQSxPQUFPRDtRQU1Fc0Msc0JBQ0RBLHVCQUNBdEMsaUJBV0FBO0lBbEJKLE1BQU1zQyxrQkFBa0J0QyxNQUFNMkIsTUFBTTtJQUVwQywwRUFBMEU7SUFDMUUsNkZBQTZGO0lBQzdGLDJFQUEyRTtJQUMzRSxJQUFJLENBQUNXLEVBQUFBLHVCQUFBQSxnQkFBZ0JQLEdBQUcsY0FBbkJPLDJDQUFBQSxxQkFBcUJDLFFBQVEsQ0FBQ2hFLGtFQUFhQSxDQUFDNkMsS0FBSyxRQUNsRGtCLHdCQUFBQSxnQkFBZ0JQLEdBQUcsY0FBbkJPLDRDQUFBQSxzQkFBcUJDLFFBQVEsQ0FBQ2hFLGtFQUFhQSxDQUFDK0MsYUFBYSxPQUN6RHRCLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHNDQUFBQSxnQkFBZ0JFLE1BQU0sTUFBSyxLQUFLO1FBQ2hDLE9BQU9nQyxRQUFRaEIsTUFBTSxDQUFDbEI7SUFDMUI7SUFFQSxpRUFBaUU7SUFDakUsNEZBQTRGO0lBQzVGLGdGQUFnRjtJQUNoRiw4RUFBOEU7SUFFOUUscUNBQXFDO0lBQ3JDLHlFQUF5RTtJQUN6RSxJQUFJQSxFQUFBQSxtQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsaUJBQWdCRSxNQUFNLE1BQUssT0FBTyxDQUFDb0MsZ0JBQWdCRSxNQUFNLEVBQUU7UUFDM0RGLGdCQUFnQkUsTUFBTSxHQUFHLE1BQU0sMENBQTBDO1FBRXpFLElBQUk1QixjQUFjO1lBQ2QsbURBQW1EO1lBQ25ELE9BQU8sSUFBSXNCLFFBQVEsQ0FBQ2pCLFNBQVNDO2dCQUN6QkwsWUFBWTRCLElBQUksQ0FBQztvQkFBRXhCO29CQUFTQztnQkFBTztZQUN2QyxHQUFHd0IsSUFBSSxDQUFDO2dCQUNKLDZEQUE2RDtnQkFDN0QsT0FBT2xELFVBQVU4QztZQUNyQixHQUFHSyxLQUFLLENBQUNDLENBQUFBO2dCQUNMLE9BQU9WLFFBQVFoQixNQUFNLENBQUMwQjtZQUMxQjtRQUNKO1FBRUFoQyxlQUFlO1FBRWYsNENBQTRDO1FBQzVDLE1BQU1pQyxpQkFBaUI7Z0JBQU92Qyw4RUFBYTtZQUN2QyxJQUFJO2dCQUNBLE1BQU13QyxrQkFBa0IsTUFBTXRELFVBQVV1RCxHQUFHLENBQUN4RSxrRUFBYUEsQ0FBQytDLGFBQWE7Z0JBQ3ZFLE9BQU93QjtZQUNYLEVBQUUsT0FBT0UsY0FBbUI7b0JBSytCQTtnQkFKdkQsbUJBQW1CO2dCQUNuQiwrQ0FBK0M7Z0JBQy9DLG9GQUFvRjtnQkFDcEYsTUFBTTdDLGlCQUFpQjlCLDZDQUFLQSxDQUFDNEUsWUFBWSxDQUFDRCxpQkFBaUIsQ0FBQ0EsYUFBYS9DLFFBQVE7Z0JBQ2pGLE1BQU1pRCxhQUFhN0UsNkNBQUtBLENBQUM0RSxZQUFZLENBQUNELGlCQUFpQkEsRUFBQUEseUJBQUFBLGFBQWEvQyxRQUFRLGNBQXJCK0MsNkNBQUFBLHVCQUF1QjlDLE1BQU0sTUFBSztnQkFDekYsTUFBTWlELGNBQWMsQ0FBQ2hELGtCQUFrQitDLFVBQVMsS0FBTTVDLGFBQWE7Z0JBRW5FLElBQUk2QyxhQUFhO29CQUNiNUMsUUFBUXFCLEdBQUcsQ0FBQyx1Q0FBNER6QixPQUEvQkcsYUFBYSxHQUFFLGtCQUE2RUEsT0FBN0RILGlCQUFpQixjQUFjLDBCQUF5QixPQUF3RSxPQUFuRUcsYUFBYSxJQUFJLHdCQUF3QjtvQkFFOUssa0NBQWtDO29CQUNsQyxNQUFNOEMsUUFBUTFDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHTCxjQUFjO29CQUN4QyxNQUFNLElBQUk0QixRQUFRakIsQ0FBQUEsVUFBV29DLFdBQVdwQyxTQUFTbUM7b0JBRWpELE9BQU9QLGVBQWV2QyxhQUFhO2dCQUN2QztnQkFFQSxnREFBZ0Q7Z0JBQ2hELE1BQU0wQztZQUNWO1FBQ0o7UUFFQSxJQUFJO1lBQ0EsZ0RBQWdEO1lBQ2hELE1BQU1GLGtCQUFrQixNQUFNRDtZQUU5QixrREFBa0Q7WUFDbEQvQixhQUFhO1lBQ2JGLGVBQWU7WUFFZix5REFBeUQ7WUFDekQsT0FBT3BCLFVBQVU4QztRQUVyQixFQUFFLE9BQU9VLGNBQW1CO2dCQUtnQkE7WUFKeEMsaURBQWlEO1lBQ2pEcEMsZUFBZTtZQUVmLGlHQUFpRztZQUNqRyxJQUFJdkMsNkNBQUtBLENBQUM0RSxZQUFZLENBQUNELGlCQUFpQkEsRUFBQUEseUJBQUFBLGFBQWEvQyxRQUFRLGNBQXJCK0MsNkNBQUFBLHVCQUF1QjlDLE1BQU0sTUFBSyxLQUFLO2dCQUMzRUssUUFBUXFCLEdBQUcsQ0FBQztnQkFFWixrREFBa0Q7Z0JBQ2xEZCxhQUFhZCxPQUFPO2dCQUVwQixzRUFBc0U7Z0JBQ3RFLE9BQU9rQyxRQUFRaEIsTUFBTSxDQUFDbEI7WUFDMUI7WUFFQSxrQ0FBa0M7WUFDbENjLGFBQWFrQyxjQUFjO1lBRTNCLElBQUkzRSw2Q0FBS0EsQ0FBQzRFLFlBQVksQ0FBQ0QsaUJBQWlCLENBQUNBLGFBQWEvQyxRQUFRLEVBQUU7Z0JBQzVELDZFQUE2RTtnQkFDN0VNLFFBQVFxQixHQUFHLENBQUM7Z0JBQ1pwRCxpRUFBZUEsQ0FBQzRELFFBQVEsR0FBR0MsU0FBUyxDQUFDO1lBQ3pDLE9BQU87Z0JBQ0g5QixRQUFRcUIsR0FBRyxDQUFDO2dCQUNaMEIsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVk7WUFDekM7WUFFQSxPQUFPdEIsUUFBUWhCLE1BQU0sQ0FBQzhCO1FBQzFCO0lBQ0o7SUFFQSwyQ0FBMkM7SUFDM0MscUVBQXFFO0lBQ3JFLHNDQUFzQztJQUN0QyxJQUFJM0UsNkNBQUtBLENBQUM0RSxZQUFZLENBQUNqRCxVQUFVLENBQUNBLE1BQU1DLFFBQVEsRUFBRTtRQUM5Q00sUUFBUXFCLEdBQUcsQ0FBQztRQUNacEQsaUVBQWVBLENBQUM0RCxRQUFRLEdBQUdDLFNBQVMsQ0FBQztRQUNyQyw0REFBNEQ7UUFDNUQsa0dBQWtHO1FBQ2xHLDZGQUE2RjtRQUM3RixPQUFPLElBQUlILFFBQVEsS0FBUTtJQUMvQjtJQUVBLCtFQUErRTtJQUMvRSxPQUFPQSxRQUFRaEIsTUFBTSxDQUFDbEI7QUFDMUI7QUFHSixpRUFBZVIsU0FBU0EsRUFBQztBQUV6Qiw4Q0FBOEM7QUFDOUMsbUJBQW1CO0FBQ25CLDhDQUE4QztBQUV2QyxNQUFNaUUsaUJBQWlCO0lBQzFCLG1DQUFtQztJQUNuQyxNQUFNQztRQUNGLElBQUk7WUFDQW5ELFFBQVFxQixHQUFHLENBQUM7WUFDWixNQUFNM0IsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDeEUsa0VBQWFBLENBQUNvRixjQUFjO1lBQ2pFcEQsUUFBUXFCLEdBQUcsQ0FBQyxvQkFBb0IzQjtZQUNoQ00sUUFBUXFCLEdBQUcsQ0FBQyxxQkFBcUIzQixTQUFTMkQsSUFBSTtZQUM5Q3JELFFBQVFxQixHQUFHLENBQUMsdUJBQXVCM0IsU0FBU0MsTUFBTTtZQUNsREssUUFBUXFCLEdBQUcsQ0FBQywwQkFBMEIsT0FBTzNCLFNBQVMyRCxJQUFJO1lBQzFEckQsUUFBUXFCLEdBQUcsQ0FBQyw4QkFBOEJpQyxNQUFNQyxPQUFPLENBQUM3RCxTQUFTMkQsSUFBSTtZQUVyRSx1RUFBdUU7WUFDdkUsSUFBSSxPQUFPM0QsU0FBUzJELElBQUksS0FBSyxZQUFZM0QsU0FBUzJELElBQUksS0FBSyxNQUFNO2dCQUM3RHJELFFBQVFxQixHQUFHLENBQUMsMEJBQTBCbUMsT0FBT0MsSUFBSSxDQUFDL0QsU0FBUzJELElBQUk7Z0JBQy9EckQsUUFBUXFCLEdBQUcsQ0FBQyw0QkFBNEJtQyxPQUFPRSxNQUFNLENBQUNoRSxTQUFTMkQsSUFBSTtnQkFFbkUsc0NBQXNDO2dCQUN0QyxJQUFJM0QsU0FBUzJELElBQUksQ0FBQ0EsSUFBSSxFQUFFO29CQUNwQnJELFFBQVFxQixHQUFHLENBQUMseUJBQXlCM0IsU0FBUzJELElBQUksQ0FBQ0EsSUFBSTtvQkFDdkRyRCxRQUFRcUIsR0FBRyxDQUFDLDRCQUE0QmlDLE1BQU1DLE9BQU8sQ0FBQzdELFNBQVMyRCxJQUFJLENBQUNBLElBQUk7Z0JBQzVFO2dCQUNBLElBQUkzRCxTQUFTMkQsSUFBSSxDQUFDTSxTQUFTLEVBQUU7b0JBQ3pCM0QsUUFBUXFCLEdBQUcsQ0FBQyxnQ0FBZ0MzQixTQUFTMkQsSUFBSSxDQUFDTSxTQUFTO29CQUNuRTNELFFBQVFxQixHQUFHLENBQUMsMEJBQTBCaUMsTUFBTUMsT0FBTyxDQUFDN0QsU0FBUzJELElBQUksQ0FBQ00sU0FBUztnQkFDL0U7Z0JBQ0EsSUFBSWpFLFNBQVMyRCxJQUFJLENBQUNPLE1BQU0sRUFBRTtvQkFDdEI1RCxRQUFRcUIsR0FBRyxDQUFDLDZCQUE2QjNCLFNBQVMyRCxJQUFJLENBQUNPLE1BQU07b0JBQzdENUQsUUFBUXFCLEdBQUcsQ0FBQyx1QkFBdUJpQyxNQUFNQyxPQUFPLENBQUM3RCxTQUFTMkQsSUFBSSxDQUFDTyxNQUFNO2dCQUN6RTtZQUNKO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUlDLGNBQWNuRSxTQUFTMkQsSUFBSTtZQUUvQix3REFBd0Q7WUFDeEQsSUFBSTNELFNBQVMyRCxJQUFJLENBQUNBLElBQUksSUFBSUMsTUFBTUMsT0FBTyxDQUFDN0QsU0FBUzJELElBQUksQ0FBQ0EsSUFBSSxHQUFHO2dCQUN6RFEsY0FBY25FLFNBQVMyRCxJQUFJLENBQUNBLElBQUk7Z0JBQ2hDckQsUUFBUXFCLEdBQUcsQ0FBQztZQUNoQixPQUVLLElBQUkzQixTQUFTMkQsSUFBSSxDQUFDTSxTQUFTLElBQUlMLE1BQU1DLE9BQU8sQ0FBQzdELFNBQVMyRCxJQUFJLENBQUNNLFNBQVMsR0FBRztnQkFDeEVFLGNBQWNuRSxTQUFTMkQsSUFBSSxDQUFDTSxTQUFTO2dCQUNyQzNELFFBQVFxQixHQUFHLENBQUM7WUFDaEIsT0FFSyxJQUFJM0IsU0FBUzJELElBQUksQ0FBQ08sTUFBTSxJQUFJTixNQUFNQyxPQUFPLENBQUM3RCxTQUFTMkQsSUFBSSxDQUFDTyxNQUFNLEdBQUc7Z0JBQ2xFQyxjQUFjbkUsU0FBUzJELElBQUksQ0FBQ08sTUFBTTtnQkFDbEM1RCxRQUFRcUIsR0FBRyxDQUFDO1lBQ2hCLE9BRUssSUFBSWlDLE1BQU1DLE9BQU8sQ0FBQzdELFNBQVMyRCxJQUFJLEdBQUc7Z0JBQ25DUSxjQUFjbkUsU0FBUzJELElBQUk7Z0JBQzNCckQsUUFBUXFCLEdBQUcsQ0FBQztZQUNoQixPQUVLO2dCQUNEckIsUUFBUUMsSUFBSSxDQUFDO2dCQUNiNEQsY0FBYyxFQUFFO1lBQ3BCO1lBRUE3RCxRQUFRcUIsR0FBRyxDQUFDLDBCQUEwQndDO1lBQ3RDN0QsUUFBUXFCLEdBQUcsQ0FBQywrQkFBK0IsT0FBT3dDO1lBQ2xEN0QsUUFBUXFCLEdBQUcsQ0FBQyxtQ0FBbUNpQyxNQUFNQyxPQUFPLENBQUNNO1lBQzdEN0QsUUFBUXFCLEdBQUcsQ0FBQywyQkFBMkJ3QyxZQUFZaEYsTUFBTTtZQUV6RCxPQUFPO2dCQUNIaUYsU0FBUztnQkFDVFQsTUFBTVE7WUFDVjtRQUNKLEVBQUUsT0FBT3BFLE9BQVk7Z0JBR2NBLGlCQUlwQkEsc0JBQUFBO1lBTlhPLFFBQVFQLEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDTyxRQUFRUCxLQUFLLENBQUMscUJBQXFCQSxNQUFNQyxRQUFRO1lBQ2pETSxRQUFRUCxLQUFLLENBQUMsa0JBQWlCQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCxzQ0FBQUEsZ0JBQWdCNEQsSUFBSTtZQUVuRCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsbUJBQUFBLE1BQU1DLFFBQVEsY0FBZEQsd0NBQUFBLHVCQUFBQSxpQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJVCxNQUFNUyxPQUFPLElBQUk7Z0JBQ3pEbUQsTUFBTSxFQUFFLENBQUMsa0NBQWtDO1lBQy9DO1FBQ0o7SUFDSjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNVSxlQUFjRixXQUFnQjtRQUNoQyxJQUFJO1lBQ0E3RCxRQUFRcUIsR0FBRyxDQUFDLCtCQUErQndDO1lBQzNDLE1BQU1uRSxXQUFXLE1BQU1ULFVBQVUrRSxJQUFJLENBQUNoRyxrRUFBYUEsQ0FBQ2lHLGNBQWMsRUFBRUo7WUFDcEU3RCxRQUFRcUIsR0FBRyxDQUFDLCtCQUErQjNCLFNBQVMyRCxJQUFJO1lBQ3hELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQyxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtZQUM1QztRQUNKO0lBQ0o7SUFFQSxZQUFZO0lBQ1osTUFBTWdFLGVBQWNDLFNBQWlCLEVBQUVDLE1BQWM7UUFDakQsSUFBSTtZQUNBcEUsUUFBUXFCLEdBQUcsQ0FBQyx3QkFBd0I7Z0JBQUU4QztnQkFBV0M7WUFBTztZQUN4RCxNQUFNMUUsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDaEcsa0VBQWFBLENBQUNxRyxjQUFjLEVBQUU7Z0JBQUVGO2dCQUFXQztZQUFPO1lBQ3hGcEUsUUFBUXFCLEdBQUcsQ0FBQywyQkFBMkIzQixTQUFTMkQsSUFBSTtZQUNwRCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7WUFDNUM7UUFDSjtJQUNKO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1vRSxtQkFBa0JILFNBQWlCO1FBQ3JDLElBQUk7WUFDQW5FLFFBQVFxQixHQUFHLENBQUMsbUNBQW1DO2dCQUFFOEM7WUFBVTtZQUMzRCxNQUFNekUsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDLEdBQWtERyxPQUEvQ25HLGtFQUFhQSxDQUFDdUcsbUJBQW1CLEVBQUMsZUFBdUIsT0FBVko7WUFDeEZuRSxRQUFRcUIsR0FBRyxDQUFDLHlDQUF5QzNCLFNBQVMyRCxJQUFJO1lBQ2xFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMseUNBQXlDQTtZQUN2RCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtZQUM1QztRQUNKO0lBQ0o7QUFDSixFQUFFO0FBRUYsOENBQThDO0FBQzlDLGdCQUFnQjtBQUNoQiw4Q0FBOEM7QUFFdkMsTUFBTXNFLGNBQWM7SUFDdkIsK0JBQStCO0lBQy9CLE1BQU1DLGVBQWNDLFdBT25CO1FBQ0csSUFBSTtZQUNBMUUsUUFBUXFCLEdBQUcsQ0FBQyw0QkFBNEJxRDtZQUN4QyxNQUFNaEYsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDaEcsa0VBQWFBLENBQUMyRyxjQUFjLEVBQUVEO1lBQ3BFMUUsUUFBUXFCLEdBQUcsQ0FBQyxpQ0FBaUMzQixTQUFTMkQsSUFBSTtZQUMxRCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7WUFDNUM7UUFDSjtJQUNKO0lBRUEsMERBQTBEO0lBQzFELE1BQU0wRSxVQUFTQyxNQU1kO1FBQ0csSUFBSTtZQUNBLHVCQUF1QjtZQUN2QixNQUFNQyxjQUFtQjtnQkFDckJDLE1BQU1GLE9BQU9FLElBQUksSUFBSTtnQkFDckJDLFVBQVVILE9BQU9HLFFBQVEsSUFBSTtnQkFDN0JDLFFBQVFKLE9BQU9JLE1BQU0sSUFBSTtZQUM3QjtZQUVBLHFDQUFxQztZQUNyQyxJQUFJSixPQUFPSyxNQUFNLElBQUlMLE9BQU9LLE1BQU0sR0FBRyxHQUFHO2dCQUNwQ0osWUFBWUksTUFBTSxHQUFHTCxPQUFPSyxNQUFNO1lBQ3RDO1lBRUEsaURBQWlEO1lBQ2pELElBQUlMLE9BQU9NLFFBQVEsS0FBS0MsV0FBVztnQkFDL0JOLFlBQVlLLFFBQVEsR0FBR04sT0FBT00sUUFBUTtZQUMxQztZQUVBLE1BQU16RixXQUFXLE1BQU1ULFVBQVUrRSxJQUFJLENBQUNoRyxrRUFBYUEsQ0FBQ3FILFNBQVMsRUFBRVA7WUFFL0QsT0FBTztnQkFDSGhCLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSVQsTUFBTVMsT0FBTyxJQUFJO2dCQUN6RG1ELE1BQU0sRUFBRTtZQUNaO1FBQ0o7SUFDSjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNaUM7UUFDRixJQUFJO1lBQ0EsTUFBTTVGLFdBQVcsTUFBTVQsVUFBVXVELEdBQUcsQ0FBQ3hFLGtFQUFhQSxDQUFDdUgsb0JBQW9CO1lBQ3ZFLE9BQU87Z0JBQ0h6QixTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLDRDQUE0Q0E7WUFDMUQsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUlULE1BQU1TLE9BQU8sSUFBSTtnQkFDekRtRCxNQUFNO1lBQ1Y7UUFDSjtJQUNKO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU1tQztRQUNGLElBQUk7WUFDQXhGLFFBQVFxQixHQUFHLENBQUM7WUFDWixNQUFNM0IsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDeEUsa0VBQWFBLENBQUN5SCxpQkFBaUI7WUFDcEV6RixRQUFRcUIsR0FBRyxDQUFDLCtDQUErQzNCLFNBQVMyRCxJQUFJO1lBQ3hFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsNkNBQTZDQTtZQUMzRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSVQsTUFBTVMsT0FBTyxJQUFJO2dCQUN6RG1ELE1BQU07WUFDVjtRQUNKO0lBQ0o7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTXFDO1FBQ0YsSUFBSTtZQUNBMUYsUUFBUXFCLEdBQUcsQ0FBQztZQUNaLE1BQU0zQixXQUFXLE1BQU1ULFVBQVUrRSxJQUFJLENBQUNoRyxrRUFBYUEsQ0FBQzJILGdCQUFnQjtZQUNwRTNGLFFBQVFxQixHQUFHLENBQUMsdUNBQXVDM0IsU0FBUzJELElBQUk7WUFDaEUsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQyxxQ0FBcUNBO1lBQ25ELE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJVCxNQUFNUyxPQUFPLElBQUk7Z0JBQ3pEbUQsTUFBTTtZQUNWO1FBQ0o7SUFDSjtBQUNKLEVBQUU7QUFFRiw4Q0FBOEM7QUFDOUMsbUJBQW1CO0FBQ25CLDhDQUE4QztBQUV2QyxNQUFNdUMsaUJBQWlCO0lBQzFCLGtCQUFrQjtJQUNsQixNQUFNQztRQUNGLElBQUk7WUFDQTdGLFFBQVFxQixHQUFHLENBQUM7WUFDWixNQUFNM0IsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDeEUsa0VBQWFBLENBQUM4SCxVQUFVO1lBQzdEOUYsUUFBUXFCLEdBQUcsQ0FBQyxnQ0FBZ0MzQixTQUFTMkQsSUFBSTtZQUN6RCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTSxFQUFFO1lBQ1o7UUFDSjtJQUNKO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU0wQyxzQkFBcUJDLE9BQWU7UUFDdEMsSUFBSTtZQUNBaEcsUUFBUXFCLEdBQUcsQ0FBQyxxQ0FBcUMyRTtZQUNqRCxNQUFNdEcsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDLEdBQTRDd0QsT0FBekNoSSxrRUFBYUEsQ0FBQ2lJLHVCQUF1QixFQUFDLEtBQVcsT0FBUkQ7WUFDakZoRyxRQUFRcUIsR0FBRyxDQUFDLG1DQUFtQzNCLFNBQVMyRCxJQUFJO1lBQzVELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtnQkFDeENtRCxNQUFNLEVBQUU7WUFDWjtRQUNKO0lBQ0o7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTTZDLGtCQUFpQkMsVUFBa0I7UUFDckMsSUFBSTtZQUNBbkcsUUFBUXFCLEdBQUcsQ0FBQyw0Q0FBNEM4RTtZQUN4RCxNQUFNekcsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDLEdBQXNDMkQsT0FBbkNuSSxrRUFBYUEsQ0FBQ29JLGlCQUFpQixFQUFDLEtBQWMsT0FBWEQsWUFBVztZQUN0Rm5HLFFBQVFxQixHQUFHLENBQUMsdUNBQXVDM0IsU0FBUzJELElBQUk7WUFDaEUsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQyxxQ0FBcUNBO1lBQ25ELE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO2dCQUN4Q21ELE1BQU0sRUFBRTtZQUNaO1FBQ0o7SUFDSjtJQUVBLG1DQUFtQztJQUNuQyxNQUFNZ0Qsd0JBQXVCQyxhQUFxQjtRQUM5QyxJQUFJO1lBQ0F0RyxRQUFRcUIsR0FBRyxDQUFDLHdEQUF3RGlGO1lBQ3BFLE1BQU01RyxXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUMsR0FBNkM4RCxPQUExQ3RJLGtFQUFhQSxDQUFDdUksd0JBQXdCLEVBQUMsS0FBaUIsT0FBZEQ7WUFDbEZ0RyxRQUFRcUIsR0FBRyxDQUFDLGdEQUFnRDNCLFNBQVMyRCxJQUFJO1lBQ3pFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsOENBQThDQTtZQUM1RCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtnQkFDeENtRCxNQUFNLEVBQUU7WUFDWjtRQUNKO0lBQ0o7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTW1ELGtCQUFpQkMsWUFBb0I7UUFDdkMsSUFBSTtZQUNBekcsUUFBUXFCLEdBQUcsQ0FBQyxpREFBaURvRjtZQUM3RCxNQUFNL0csV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDLEdBQXVDaUUsT0FBcEN6SSxrRUFBYUEsQ0FBQzBJLGtCQUFrQixFQUFDLEtBQWdCLE9BQWJEO1lBQzVFekcsUUFBUXFCLEdBQUcsQ0FBQyx5Q0FBeUMzQixTQUFTMkQsSUFBSTtZQUNsRSxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckQsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTSxFQUFFO1lBQ1o7UUFDSjtJQUNKO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1zRDtRQUNGLElBQUk7WUFDQTNHLFFBQVFxQixHQUFHLENBQUM7WUFDWixNQUFNM0IsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDeEUsa0VBQWFBLENBQUM0SSwyQkFBMkI7WUFDOUU1RyxRQUFRcUIsR0FBRyxDQUFDLDZDQUE2QzNCLFNBQVMyRCxJQUFJO1lBQ3RFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsMkNBQTJDQTtZQUN6RCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtnQkFDeENtRCxNQUFNLEVBQUU7WUFDWjtRQUNKO0lBQ0o7SUFFQSxvREFBb0Q7SUFDcEQsTUFBTXdELG1CQUFrQkMsV0FBZ0I7UUFDcEMsSUFBSTtZQUNBOUcsUUFBUXFCLEdBQUcsQ0FBQyw2QkFBNkJ5RjtZQUN6QyxNQUFNcEgsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDaEcsa0VBQWFBLENBQUMrSSxtQkFBbUIsRUFBRUQsYUFBYTtnQkFDbEYxSCxTQUFTO29CQUNMLGdCQUFnQjtnQkFDcEI7WUFDSjtZQUNBWSxRQUFRcUIsR0FBRyxDQUFDLGtDQUFrQzNCLFNBQVMyRCxJQUFJO1lBQzNELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsbUNBQW1DQTtZQUNqRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtZQUM1QztRQUNKO0lBQ0o7SUFFQSw0REFBNEQ7SUFDNUQsTUFBTThHLHlCQUF3QkYsV0FBZ0I7UUFDMUMsSUFBSTtZQUNBOUcsUUFBUXFCLEdBQUcsQ0FBQyxpQ0FBaUN5RjtZQUM3QyxNQUFNcEgsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDaEcsa0VBQWFBLENBQUNpSix5QkFBeUIsRUFBRUgsYUFBYTtnQkFDeEYxSCxTQUFTO29CQUNMLGdCQUFnQjtnQkFDcEI7WUFDSjtZQUNBWSxRQUFRcUIsR0FBRyxDQUFDLHNDQUFzQzNCLFNBQVMyRCxJQUFJO1lBQy9ELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsdUNBQXVDQTtZQUNyRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtZQUM1QztRQUNKO0lBQ0o7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTWdILGlCQUFnQkMsU0FBYztRQUNoQyxJQUFJO1lBQ0FuSCxRQUFRcUIsR0FBRyxDQUFDLCtCQUErQjhGO1lBQzNDLE1BQU16SCxXQUFXLE1BQU1ULFVBQVUrRSxJQUFJLENBQUNoRyxrRUFBYUEsQ0FBQ29KLGlCQUFpQixFQUFFRCxXQUFXO2dCQUM5RS9ILFNBQVM7b0JBQ0wsZ0JBQWdCO2dCQUNwQjtZQUNKO1lBQ0FZLFFBQVFxQixHQUFHLENBQUMsbUNBQW1DM0IsU0FBUzJELElBQUk7WUFDNUQsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO1lBQzVDO1FBQ0o7SUFDSjtJQUVBLHFCQUFxQjtJQUNyQixNQUFNbUgsb0JBQW1CQyxPQUFlO1FBQ3BDLElBQUk7WUFDQXRILFFBQVFxQixHQUFHLENBQUMsd0NBQXdDaUc7WUFDcEQsTUFBTTVILFdBQVcsTUFBTVQsVUFBVXNJLE1BQU0sQ0FBQyxHQUF5Q0QsT0FBdEN0SixrRUFBYUEsQ0FBQ3dKLG9CQUFvQixFQUFDLEtBQVcsT0FBUkY7WUFDakZ0SCxRQUFRcUIsR0FBRyxDQUFDLGtDQUFrQzNCLFNBQVMyRCxJQUFJO1lBQzNELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsbUNBQW1DQTtZQUNqRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtZQUM1QztRQUNKO0lBQ0o7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTXVILHFCQUFvQkgsT0FBZSxFQUFFSSxZQUFrQjtRQUN6RCxJQUFJO1lBQ0ExSCxRQUFRcUIsR0FBRyxDQUFDLDRDQUE0Q2lHO1lBQ3hELE1BQU1LLFdBQVcsSUFBSUM7WUFDckJELFNBQVNFLE1BQU0sQ0FBQyxRQUFRSDtZQUV4QixNQUFNaEksV0FBVyxNQUFNVCxVQUFVNkksR0FBRyxDQUFDLEdBQTBDUixPQUF2Q3RKLGtFQUFhQSxDQUFDK0oscUJBQXFCLEVBQUMsS0FBVyxPQUFSVCxVQUFXSyxVQUFVO2dCQUNoR3ZJLFNBQVM7b0JBQ0wsZ0JBQWdCO2dCQUNwQjtZQUNKO1lBQ0FZLFFBQVFxQixHQUFHLENBQUMsdUNBQXVDM0IsU0FBUzJELElBQUk7WUFDaEUsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQyx3Q0FBd0NBO1lBQ3RELE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO1lBQzVDO1FBQ0o7SUFDSjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNOEgsa0JBQWlCbkQsTUFBd0U7UUFDM0YsSUFBSTtZQUNBLE1BQU1uRixXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUN4RSxrRUFBYUEsQ0FBQ2lLLGtCQUFrQixFQUFFO2dCQUFFcEQ7WUFBTztZQUNoRixPQUFPdkIsTUFBTUMsT0FBTyxDQUFDN0QsU0FBUzJELElBQUksSUFBSTNELFNBQVMyRCxJQUFJLEdBQUcsRUFBRTtRQUM1RCxFQUFFLE9BQU81RCxPQUFPO1lBQ1pPLFFBQVFQLEtBQUssQ0FBQyxvQ0FBb0NBO1lBQ2xELE9BQU8sRUFBRTtRQUNiO0lBQ0o7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTXlJLGVBQWNyRCxNQUEwRTtRQUMxRixJQUFJO1lBQ0E3RSxRQUFRcUIsR0FBRyxDQUFDLHdDQUF3Q3dEO1lBQ3BELE1BQU1uRixXQUFXLE1BQU1ULFVBQVUrRSxJQUFJLENBQUNoRyxrRUFBYUEsQ0FBQ21LLGVBQWUsRUFBRXREO1lBQ3JFN0UsUUFBUXFCLEdBQUcsQ0FBQywrQ0FBK0MzQixTQUFTMkQsSUFBSTtZQUN4RSxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLDZDQUE2Q0E7WUFDM0QsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTTtZQUNWO1FBQ0o7SUFDSjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNK0UsNEJBQTJCQyxTQUFpQjtRQUM5QyxJQUFJO1lBQ0FySSxRQUFRcUIsR0FBRyxDQUFDLGtEQUFrRGdIO1lBQzlELE1BQU0zSSxXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUMsR0FBa0Q2RixPQUEvQ3JLLGtFQUFhQSxDQUFDc0ssNkJBQTZCLEVBQUMsS0FBYSxPQUFWRDtZQUN2RnJJLFFBQVFxQixHQUFHLENBQUMsOENBQThDM0IsU0FBUzJELElBQUk7WUFDdkUsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQyw0Q0FBNENBO1lBQzFELE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO2dCQUN4Q21ELE1BQU07WUFDVjtRQUNKO0lBQ0o7SUFFQSxZQUFZO0lBQ1osTUFBTWtGLGVBQWNGLFNBQWlCO1FBQ2pDLElBQUk7WUFDQXJJLFFBQVFxQixHQUFHLENBQUMsa0NBQWtDZ0g7WUFDOUMsK0VBQStFO1lBQy9FLE1BQU0zSSxXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUN4RSxrRUFBYUEsQ0FBQ3dLLGNBQWMsRUFBRTtnQkFDL0QzRCxRQUFRO29CQUFFd0Q7Z0JBQVU7WUFDeEI7WUFDQXJJLFFBQVFxQixHQUFHLENBQUMsMEJBQTBCM0IsU0FBUzJELElBQUk7WUFDbkQsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO1lBQzVDO1FBQ0o7SUFDSjtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNdUksbUJBQWtCM0IsV0FBZ0I7UUFDcEMsSUFBSTtZQUNBOUcsUUFBUXFCLEdBQUcsQ0FBQyw4QkFBOEJ5RjtZQUUxQyxtQ0FBbUM7WUFDbkM5RyxRQUFRcUIsR0FBRyxDQUFDO1lBQ1osS0FBSyxJQUFJLENBQUNxSCxLQUFLakssTUFBTSxJQUFJcUksWUFBWTZCLE9BQU8sR0FBSTtnQkFDNUMzSSxRQUFRcUIsR0FBRyxDQUFDLEdBQU8sT0FBSnFILEtBQUksTUFBSWpLO1lBQzNCO1lBRUEsTUFBTWlCLFdBQVcsTUFBTVQsVUFBVStFLElBQUksQ0FBQ2hHLGtFQUFhQSxDQUFDNEssbUJBQW1CLEVBQUU5QixhQUFhO2dCQUNsRjFILFNBQVM7b0JBQ0wsZ0JBQWdCO2dCQUNwQjtZQUNKO1lBQ0FZLFFBQVFxQixHQUFHLENBQUMsbUNBQW1DM0IsU0FBUzJELElBQUk7WUFDNUQsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFFa0JBLGlCQUNIQSxrQkFHWEEsc0JBQUFBLGtCQUNqQkEsdUJBQUFBO1lBTkpPLFFBQVFQLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pETyxRQUFRUCxLQUFLLENBQUMsc0JBQXFCQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCxzQ0FBQUEsZ0JBQWdCNEQsSUFBSTtZQUN2RHJELFFBQVFQLEtBQUssQ0FBQyxtQkFBa0JBLG1CQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSxpQkFBZ0JFLE1BQU07WUFFdEQsb0NBQW9DO1lBQ3BDLE1BQU1rSixlQUFlcEosRUFBQUEsbUJBQUFBLE1BQU1DLFFBQVEsY0FBZEQsd0NBQUFBLHVCQUFBQSxpQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxPQUM5Q1QsbUJBQUFBLE1BQU1DLFFBQVEsY0FBZEQsd0NBQUFBLHdCQUFBQSxpQkFBZ0I0RCxJQUFJLGNBQXBCNUQsNENBQUFBLHNCQUFzQnFKLEtBQUssS0FDM0JySixNQUFNUyxPQUFPLElBQ2I7WUFFSixNQUFNLElBQUk2SSxNQUFNRjtRQUNwQjtJQUNKO0lBRUEsb0NBQW9DO0lBQ3BDLE1BQU1HO1FBQ0YsSUFBSTtZQUNBaEosUUFBUXFCLEdBQUcsQ0FBQztZQUNaLE1BQU0zQixXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUN4RSxrRUFBYUEsQ0FBQ2lMLDRCQUE0QjtZQUMvRWpKLFFBQVFxQixHQUFHLENBQUMsaURBQWlEM0IsU0FBUzJELElBQUk7WUFDMUUsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQywrQ0FBK0NBO1lBQzdELE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO2dCQUN4Q21ELE1BQU07WUFDVjtRQUNKO0lBQ0o7SUFFQSx3Q0FBd0M7SUFDeEMsTUFBTTZGO1FBQ0YsSUFBSTtZQUNBbEosUUFBUXFCLEdBQUcsQ0FBQztZQUNaLE1BQU0zQixXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUN4RSxrRUFBYUEsQ0FBQ21MLHlCQUF5QjtZQUM1RW5KLFFBQVFxQixHQUFHLENBQUMscURBQXFEM0IsU0FBUzJELElBQUk7WUFDOUUsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJLENBQUNBLElBQUksQ0FBQywrQ0FBK0M7WUFDNUU7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLG1EQUFtREE7WUFDakUsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTTtZQUNWO1FBQ0o7SUFDSjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNK0YscUJBQW9CQyxlQUF5QjtRQUMvQ3JKLFFBQVFxQixHQUFHLENBQUM7UUFDWnJCLFFBQVFxQixHQUFHLENBQUMsZ0JBQWdCckQsa0VBQWFBLENBQUNzTCxxQkFBcUI7UUFFL0QsMkJBQTJCO1FBQzNCdEosUUFBUXFCLEdBQUcsQ0FBQztRQUNaLEtBQUssSUFBSSxDQUFDcUgsS0FBS2pLLE1BQU0sSUFBSTRLLGdCQUFnQlYsT0FBTyxHQUFJO1lBQ2hELElBQUlsSyxpQkFBaUI4SyxNQUFNO2dCQUN2QnZKLFFBQVFxQixHQUFHLENBQUMsS0FBa0I1QyxPQUFiaUssS0FBSSxXQUF3QmpLLE9BQWZBLE1BQU1GLElBQUksRUFBQyxNQUF5QkUsT0FBckJBLE1BQU0rSyxJQUFJLEVBQUMsWUFBcUIsT0FBWC9LLE1BQU1nTCxJQUFJLEVBQUM7WUFDakYsT0FBTztnQkFDSHpKLFFBQVFxQixHQUFHLENBQUMsS0FBYTVDLE9BQVJpSyxLQUFJLE1BQVUsT0FBTmpLO1lBQzdCO1FBQ0o7UUFFQSxNQUFNaUIsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDaEcsa0VBQWFBLENBQUNzTCxxQkFBcUIsRUFBRUQsaUJBQWlCO1lBQ3hGakssU0FBUztnQkFDTCxnQkFBZ0I7WUFDcEI7UUFDSjtRQUVBWSxRQUFRcUIsR0FBRyxDQUFDO1FBQ1pyQixRQUFRcUIsR0FBRyxDQUFDLHVCQUF1QjNCLFNBQVNDLE1BQU07UUFDbERLLFFBQVFxQixHQUFHLENBQUMscUJBQXFCM0IsU0FBUzJELElBQUk7UUFFOUMsT0FBTzNELFNBQVMyRCxJQUFJO0lBQ3hCO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1xRyxrQkFBaUJyQixTQUFpQjtRQUNwQyxJQUFJO1lBQ0FySSxRQUFRcUIsR0FBRyxDQUFDLHVDQUF1Q2dIO1lBQ25ELE1BQU0zSSxXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUMsR0FBdUM2RixPQUFwQ3JLLGtFQUFhQSxDQUFDMkwsa0JBQWtCLEVBQUMsS0FBYSxPQUFWdEI7WUFDNUVySSxRQUFRcUIsR0FBRyxDQUFDLG1DQUFtQzNCLFNBQVMyRCxJQUFJO1lBQzVELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtnQkFDeENtRCxNQUFNO1lBQ1Y7UUFDSjtJQUNKO0lBRUEsdUNBQXVDO0lBQ3ZDLE1BQU11Ryx5QkFBd0J2QixTQUFpQjtRQUMzQyxJQUFJO1lBQ0FySSxRQUFRcUIsR0FBRyxDQUFDLCtDQUErQ2dIO1lBQzNELE1BQU0zSSxXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUMsR0FBK0M2RixPQUE1Q3JLLGtFQUFhQSxDQUFDNkwsMEJBQTBCLEVBQUMsS0FBYSxPQUFWeEI7WUFDcEZySSxRQUFRcUIsR0FBRyxDQUFDLDJDQUEyQzNCLFNBQVMyRCxJQUFJO1lBQ3BFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMseUNBQXlDQTtZQUN2RCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtnQkFDeENtRCxNQUFNO1lBQ1Y7UUFDSjtJQUNKO0lBRUEsb0NBQW9DO0lBQ3BDLE1BQU15RyxxQkFBb0J6QixTQUFpQixFQUFFMEIsVUFBbUIsRUFBRTdKLE9BQWU7UUFDN0UsSUFBSTtZQUNBRixRQUFRcUIsR0FBRyxDQUFDLGlDQUFpQztnQkFBRWdIO2dCQUFXMEI7Z0JBQVk3SjtZQUFRO1lBQzlFLE1BQU1SLFdBQVcsTUFBTVQsVUFBVStFLElBQUksQ0FBQ2hHLGtFQUFhQSxDQUFDZ00scUJBQXFCLEVBQUU7Z0JBQ3ZFM0I7Z0JBQ0EwQjtnQkFDQTdKO1lBQ0o7WUFDQUYsUUFBUXFCLEdBQUcsQ0FBQyx3Q0FBd0MzQixTQUFTMkQsSUFBSTtZQUNqRSxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLHNDQUFzQ0E7WUFDcEQsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7WUFDNUM7UUFDSjtJQUNKO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU0rSixtQkFBa0I1QixTQUFpQjtRQUNyQyxJQUFJO1lBQ0FySSxRQUFRcUIsR0FBRyxDQUFDLDJDQUEyQ2dIO1lBQ3ZELE1BQU0zSSxXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUMsR0FBd0M2RixPQUFyQ3JLLGtFQUFhQSxDQUFDa00sbUJBQW1CLEVBQUMsS0FBYSxPQUFWN0I7WUFDN0VySSxRQUFRcUIsR0FBRyxDQUFDLHVDQUF1QzNCLFNBQVMyRCxJQUFJO1lBQ2hFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSWJBLGlCQVVPQSxzQkFBQUE7WUFiWE8sUUFBUVAsS0FBSyxDQUFDLHFDQUFxQ0E7WUFFbkQsb0NBQW9DO1lBQ3BDLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHNDQUFBQSxnQkFBZ0JFLE1BQU0sTUFBSyxLQUFLO2dCQUNoQyxPQUFPO29CQUNIbUUsU0FBUztvQkFDVFQsTUFBTTtnQkFDVjtZQUNKO1lBRUEsa0NBQWtDO1lBQ2xDLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxtQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx3Q0FBQUEsdUJBQUFBLGlCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTTtZQUNWO1FBQ0o7SUFDSjtJQUVBLGtDQUFrQztJQUNsQyxNQUFNOEc7UUFDRixJQUFJO1lBQ0FuSyxRQUFRcUIsR0FBRyxDQUFDO1lBQ1osTUFBTTNCLFdBQVcsTUFBTVQsVUFBVXVELEdBQUcsQ0FBQ3hFLGtFQUFhQSxDQUFDb00sY0FBYztZQUNqRXBLLFFBQVFxQixHQUFHLENBQUMsbUNBQW1DM0IsU0FBUzJELElBQUk7WUFDNUQsT0FBTztnQkFDSFMsU0FBUztnQkFDVFQsTUFBTTNELFNBQVMyRCxJQUFJO1lBQ3ZCO1FBQ0osRUFBRSxPQUFPNUQsT0FBWTtnQkFJTkEsc0JBQUFBO1lBSFhPLFFBQVFQLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLE9BQU87Z0JBQ0hxRSxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsa0JBQUFBLE1BQU1DLFFBQVEsY0FBZEQsdUNBQUFBLHVCQUFBQSxnQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO2dCQUN4Q21ELE1BQU0sRUFBRTtZQUNaO1FBQ0o7SUFDSjtJQUVBLGtEQUFrRDtJQUNsRCxNQUFNZ0gsNEJBQTJCbEUsVUFBa0I7UUFDL0MsSUFBSTtZQUNBbkcsUUFBUXFCLEdBQUcsQ0FBQyw0Q0FBNEM4RTtZQUN4RCxNQUFNekcsV0FBVyxNQUFNVCxVQUFVdUQsR0FBRyxDQUFDLEdBQThGLE9BQTNGeEUsa0VBQWFBLENBQUNzTSw2QkFBNkIsQ0FBQ0MsT0FBTyxDQUFDLGdCQUFnQnBFLFdBQVdxRSxRQUFRO1lBQy9IeEssUUFBUXFCLEdBQUcsQ0FBQyx1Q0FBdUMzQixTQUFTMkQsSUFBSTtZQUNoRSxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTSxFQUFFO1lBQ1o7UUFDSjtJQUNKO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1vSCxnQkFBZUMsYUFBa0I7UUFDbkMsSUFBSTtZQUNBMUssUUFBUXFCLEdBQUcsQ0FBQyw2QkFBNkJxSjtZQUN6QyxNQUFNaEwsV0FBVyxNQUFNVCxVQUFVK0UsSUFBSSxDQUFDaEcsa0VBQWFBLENBQUMyTSxlQUFlLEVBQUVEO1lBQ3JFMUssUUFBUXFCLEdBQUcsQ0FBQyxvQ0FBb0MzQixTQUFTMkQsSUFBSTtZQUM3RCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaEQsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTTtZQUNWO1FBQ0o7SUFDSjtJQUVBLGdDQUFnQztJQUNoQyxNQUFNdUg7UUFDRixJQUFJO1lBQ0E1SyxRQUFRcUIsR0FBRyxDQUFDO1lBQ1osTUFBTTNCLFdBQVcsTUFBTVQsVUFBVXVELEdBQUcsQ0FBQ3hFLGtFQUFhQSxDQUFDNk0sa0JBQWtCO1lBQ3JFN0ssUUFBUXFCLEdBQUcsQ0FBQyxzQ0FBc0MzQixTQUFTMkQsSUFBSTtZQUMvRCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLG9DQUFvQ0E7WUFDbEQsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7Z0JBQ3hDbUQsTUFBTTtZQUNWO1FBQ0o7SUFDSjtBQUNKLEVBQUU7QUFFSyxNQUFNeUgsY0FBYztJQUN2QixtQkFBbUI7SUFDbkIsTUFBTUMsV0FBVUMsZ0JBQXdCLEVBQUVDLFFBQWdCLEVBQUVDLGVBQXdCO1FBQ2hGLElBQUk7WUFDQWxMLFFBQVFxQixHQUFHLENBQUMsNkJBQTZCO2dCQUFFMko7Z0JBQWtCQztnQkFBVUM7WUFBZ0I7WUFDdkYsTUFBTXhMLFdBQVcsTUFBTVQsVUFBVStFLElBQUksQ0FBQ2hHLGtFQUFhQSxDQUFDbU4sV0FBVyxFQUFFO2dCQUM3REg7Z0JBQ0FDO2dCQUNBQztZQUNKO1lBQ0FsTCxRQUFRcUIsR0FBRyxDQUFDLG9DQUFvQzNCLFNBQVMyRCxJQUFJO1lBQzdELE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtZQUM1QztRQUNKO0lBQ0o7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTWtMO1FBQ0YsSUFBSTtZQUNBcEwsUUFBUXFCLEdBQUcsQ0FBQztZQUNaLE1BQU0zQixXQUFXLE1BQU1ULFVBQVV1RCxHQUFHLENBQUN4RSxrRUFBYUEsQ0FBQ3FOLGNBQWM7WUFDakVyTCxRQUFRcUIsR0FBRyxDQUFDLHdDQUF3QzNCLFNBQVMyRCxJQUFJO1lBQ2pFLE9BQU87Z0JBQ0hTLFNBQVM7Z0JBQ1RULE1BQU0zRCxTQUFTMkQsSUFBSTtZQUN2QjtRQUNKLEVBQUUsT0FBTzVELE9BQVk7Z0JBSU5BLHNCQUFBQTtZQUhYTyxRQUFRUCxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRCxPQUFPO2dCQUNIcUUsU0FBUztnQkFDVHJFLE9BQU9BLEVBQUFBLGtCQUFBQSxNQUFNQyxRQUFRLGNBQWRELHVDQUFBQSx1QkFBQUEsZ0JBQWdCNEQsSUFBSSxjQUFwQjVELDJDQUFBQSxxQkFBc0JTLE9BQU8sS0FBSTtnQkFDeENtRCxNQUFNO1lBQ1Y7UUFDSjtJQUNKO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1pSSxnQkFBZU4sZ0JBQXdCO1FBQ3pDLElBQUk7WUFDQWhMLFFBQVFxQixHQUFHLENBQUMsa0NBQWtDO2dCQUFFMko7WUFBaUI7WUFDakUsTUFBTXhKLE1BQU0sR0FBcUN3SixPQUFsQ2hOLGtFQUFhQSxDQUFDdU4sZ0JBQWdCLEVBQUMsS0FBb0IsT0FBakJQO1lBQ2pEaEwsUUFBUXFCLEdBQUcsQ0FBQyxlQUFlRztZQUMzQnhCLFFBQVFxQixHQUFHLENBQUMsc0NBQXNDckQsa0VBQWFBLENBQUN1TixnQkFBZ0I7WUFDaEYsTUFBTTdMLFdBQVcsTUFBTVQsVUFBVXNJLE1BQU0sQ0FBQy9GO1lBQ3hDeEIsUUFBUXFCLEdBQUcsQ0FBQyx3Q0FBd0MzQixTQUFTMkQsSUFBSTtZQUNqRSxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUdnQkEsaUJBQ0ZBLGtCQUdwQkEsc0JBQUFBO1lBTlhPLFFBQVFQLEtBQUssQ0FBQyxzQ0FBc0NBO1lBQ3BETyxRQUFRUCxLQUFLLENBQUMscUJBQXFCQSxNQUFNQyxRQUFRO1lBQ2pETSxRQUFRUCxLQUFLLENBQUMsb0JBQW1CQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCxzQ0FBQUEsZ0JBQWdCRSxNQUFNO1lBQ3ZESyxRQUFRUCxLQUFLLENBQUMsa0JBQWlCQSxtQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsaUJBQWdCNEQsSUFBSTtZQUNuRCxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUckUsT0FBT0EsRUFBQUEsbUJBQUFBLE1BQU1DLFFBQVEsY0FBZEQsd0NBQUFBLHVCQUFBQSxpQkFBZ0I0RCxJQUFJLGNBQXBCNUQsMkNBQUFBLHFCQUFzQlMsT0FBTyxLQUFJO1lBQzVDO1FBQ0o7SUFDSjtJQUVBLGdDQUFnQztJQUNoQyxNQUFNc0wsb0JBQW1CUixnQkFBd0IsRUFBRUMsUUFBZ0I7UUFDL0QsSUFBSTtZQUNBakwsUUFBUXFCLEdBQUcsQ0FBQyx3Q0FBd0M7Z0JBQUUySjtnQkFBa0JDO1lBQVM7WUFDakYsTUFBTXZMLFdBQVcsTUFBTVQsVUFBVStFLElBQUksQ0FBQ2hHLGtFQUFhQSxDQUFDeU4sb0JBQW9CLEVBQUU7Z0JBQ3RFVDtnQkFDQUM7WUFDSjtZQUNBakwsUUFBUXFCLEdBQUcsQ0FBQywrQ0FBK0MzQixTQUFTMkQsSUFBSTtZQUN4RSxPQUFPO2dCQUNIUyxTQUFTO2dCQUNUVCxNQUFNM0QsU0FBUzJELElBQUk7WUFDdkI7UUFDSixFQUFFLE9BQU81RCxPQUFZO2dCQUlOQSxzQkFBQUE7WUFIWE8sUUFBUVAsS0FBSyxDQUFDLDZDQUE2Q0E7WUFDM0QsT0FBTztnQkFDSHFFLFNBQVM7Z0JBQ1RyRSxPQUFPQSxFQUFBQSxrQkFBQUEsTUFBTUMsUUFBUSxjQUFkRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQjRELElBQUksY0FBcEI1RCwyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUk7WUFDNUM7UUFDSjtJQUNKO0FBQ0osRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxEZXNrdG9wXFxTYXlnbG9iYWxcXHNheWdsb2JhbC1mcm9udGVuZFxcc3JjXFxzZXJ2aWNlc1xcYXBpLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5pbXBvcnQgYXhpb3NSZXRyeSBmcm9tICdheGlvcy1yZXRyeSc7XG5pbXBvcnQgeyBBUElfRU5EUE9JTlRTIH0gZnJvbSAnQC9jb25zdGFudHMvYXBpRW5kcG9pbnRzJztcbmltcG9ydCB7IHVzZU5ldHdvcmtTdG9yZSB9IGZyb20gJ0Avc3RvcmVzL25ldHdvcmtTdG9yZSc7XG5pbXBvcnQgeyBBZG1pblByb2R1Y3QgfSBmcm9tICdAL3R5cGVzJztcblxuLy8gQVBJIEJhc2UgVVJMIC0gRGV2ZWxvcG1lbnQndGUgcHJveHkga3VsbGFuLCBwcm9kdWN0aW9uJ2RhIGRpcmVrdCBBUElcbmNvbnN0IEFQSV9CQVNFX1VSTCA9IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnXG4gICAgPyAnJyAvLyBEZXZlbG9wbWVudCd0ZSBOZXh0LmpzIHByb3h5IGt1bGxhbsSxclxuICAgIDogKHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHBzOi8vYXBpLnNheWdsb2JhbHdlYi5jb20nKTtcblxuLy8gQ29va2llJ2RlbiBiZWxpcmxpIGJpciBkZcSfZXJpIG9rdW1hIHV0aWxpdHkgZm9ua3NpeW9udVxuY29uc3QgZ2V0Q29va2llVmFsdWUgPSAobmFtZTogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCA9PiB7XG4gICAgaWYgKHR5cGVvZiBkb2N1bWVudCA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsOyAvLyBTU1Iga29udHJvbMO8XG5cbiAgICBjb25zdCB2YWx1ZSA9IGA7ICR7ZG9jdW1lbnQuY29va2llfWA7XG4gICAgY29uc3QgcGFydHMgPSB2YWx1ZS5zcGxpdChgOyAke25hbWV9PWApO1xuICAgIGlmIChwYXJ0cy5sZW5ndGggPT09IDIpIHtcbiAgICAgICAgY29uc3QgY29va2llVmFsdWUgPSBwYXJ0cy5wb3AoKT8uc3BsaXQoJzsnKS5zaGlmdCgpO1xuICAgICAgICByZXR1cm4gY29va2llVmFsdWUgfHwgbnVsbDtcbiAgICB9XG4gICAgcmV0dXJuIG51bGw7XG59O1xuXG4vLyBBeGlvcyBpbnN0YW5jZSBvbHXFn3R1ciAtIEhUVFAtb25seSBjb29raWVzIGnDp2luIHdpdGhDcmVkZW50aWFsczogdHJ1ZVxuY29uc3QgYXBpQ2xpZW50ID0gYXhpb3MuY3JlYXRlKHtcbiAgICBiYXNlVVJMOiBBUElfQkFTRV9VUkwsXG4gICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgIH0sXG4gICAgdGltZW91dDogMzAwMDAsIC8vIDMwIHNhbml5ZSB0aW1lb3V0XG4gICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLCAvLyBIVFRQLW9ubHkgY29va2llcyBpw6dpblxufSk7XG5cbi8vIFllbmlkZW4gZGVuZW1lIG1la2FuaXptYXPEsW7EsSB5YXDEsWxhbmTEsXJcbmF4aW9zUmV0cnkoYXBpQ2xpZW50LCB7XG4gICAgcmV0cmllczogMywgLy8gMyBrZXogeWVuaWRlbiBkZW5lXG4gICAgcmV0cnlDb25kaXRpb246IChlcnJvcikgPT4ge1xuICAgICAgICAvLyBTYWRlY2UgYcSfIGhhdGFsYXLEsW5kYSB2ZXlhIHN1bnVjdSB0YXJhZsSxIGdlw6dpY2kgaGF0YWxhcsSxbmRhIHllbmlkZW4gZGVuZS5cbiAgICAgICAgLy8gNDAxLzQwMyBnaWJpIGNsaWVudCBoYXRhbGFyxLFuZGEgeWVuaWRlbiBkZW5lbWUsIMOnw7xua8O8IGJ1IGF1dGggYWvEscWfxLFuxLEgZ2VjaWt0aXJpci5cbiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgJiYgZXJyb3IucmVzcG9uc2Uuc3RhdHVzID49IDQwMCAmJiBlcnJvci5yZXNwb25zZS5zdGF0dXMgPCA1MDApIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYXhpb3NSZXRyeS5pc05ldHdvcmtFcnJvcihlcnJvcikgfHwgYXhpb3NSZXRyeS5pc0lkZW1wb3RlbnRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgIH0sXG4gICAgcmV0cnlEZWxheTogKHJldHJ5Q291bnQsIGVycm9yKSA9PiB7XG4gICAgICAgIGNvbnNvbGUud2FybihgW2F4aW9zLXJldHJ5XSBSZXF1ZXN0IGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfS4gUmV0cnkgYXR0ZW1wdCAjJHtyZXRyeUNvdW50fS4uLmApO1xuICAgICAgICAvLyBIZXIgZGVuZW1lZGUgYmVrbGVtZSBzw7xyZXNpbmkgYXJ0xLFyICgxcywgMnMsIDRzKVxuICAgICAgICByZXR1cm4gTWF0aC5wb3coMiwgcmV0cnlDb3VudCAtIDEpICogMTAwMDtcbiAgICB9LFxufSk7XG5cbi8vIFJlZnJlc2ggacWfbGVtaSBkZXZhbSBlZGl5b3IgbXUga29udHJvbMO8XG5sZXQgaXNSZWZyZXNoaW5nID0gZmFsc2U7XG5sZXQgZmFpbGVkUXVldWU6IEFycmF5PHtcbiAgICByZXNvbHZlOiAodmFsdWU/OiBhbnkpID0+IHZvaWQ7XG4gICAgcmVqZWN0OiAoZXJyb3I/OiBhbnkpID0+IHZvaWQ7XG59PiA9IFtdO1xuXG5jb25zdCBwcm9jZXNzUXVldWUgPSAoZXJyb3I6IGFueSwgdG9rZW46IHN0cmluZyB8IG51bGwgPSBudWxsKSA9PiB7XG4gICAgZmFpbGVkUXVldWUuZm9yRWFjaCgoeyByZXNvbHZlLCByZWplY3QgfSkgPT4ge1xuICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXNvbHZlKHRva2VuKTtcbiAgICAgICAgfVxuICAgIH0pO1xuXG4gICAgZmFpbGVkUXVldWUgPSBbXTtcbn07XG5cbi8vIEhlcmtlc2UgYcOnxLFrIHZlIGVuZ2VsbGVubWVtZXNpIGdlcmVrZW4gZW5kcG9pbnQnbGVyXG5jb25zdCBwdWJsaWNFbmRwb2ludHMgPSBbXG4gICAgQVBJX0VORFBPSU5UUy5MT0dJTixcbiAgICBBUElfRU5EUE9JTlRTLlJFR0lTVEVSLFxuICAgIEFQSV9FTkRQT0lOVFMuUkVGUkVTSF9UT0tFTixcbiAgICBBUElfRU5EUE9JTlRTLkxPR09VVCxcbiAgICAvL0FQSV9FTkRQT0lOVFMuRk9SR09UX1BBU1NXT1JELFxuICAgIC8vIEVrbGVuZWJpbGVjZWsgZGnEn2VyIHB1YmxpYyBlbmRwb2ludCdsZXIuLi5cbl07XG5cbi8vIFJlcXVlc3QgaW50ZXJjZXB0b3IgLSBDb29raWUnbGVyaSBvdG9tYXRpayBnw7ZuZGVyIChIdHRwT25seSBjb29raWUnbGVyIGnDp2luKVxuYXBpQ2xpZW50LmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShcbiAgICAoY29uZmlnKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OhIEFQSSBSZXF1ZXN0IGJhxZ9sxLF5b3I6JywgY29uZmlnLm1ldGhvZD8udG9VcHBlckNhc2UoKSwgY29uZmlnLnVybCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn42qIHdpdGhDcmVkZW50aWFsczonLCBjb25maWcud2l0aENyZWRlbnRpYWxzKTtcblxuICAgICAgICAvLyBIdHRwT25seSBjb29raWUnbGVyIEphdmFTY3JpcHQgaWxlIG9rdW5hbWF6LCBidSBub3JtYWwgYmlyIGR1cnVtXG4gICAgICAgIC8vIHdpdGhDcmVkZW50aWFsczogdHJ1ZSBvbGR1xJ91IGnDp2luIGJyb3dzZXIgY29va2llJ2xlcmkgb3RvbWF0aWsgZ8O2bmRlcmlyXG4gICAgICAgIC8vIEJhY2tlbmQgSHR0cE9ubHkgY29va2llJ3lpIGtvbnRyb2wgZWRlY2VrXG5cbiAgICAgICAgLy8gRcSfZXIgY29va2llIEphdmFTY3JpcHQgaWxlIG9rdW5hYmlsaXlvcnNhIEF1dGhvcml6YXRpb24gaGVhZGVyJ8SxbmEgZGEgZWtsZVxuICAgICAgICBjb25zdCBhY2Nlc3NUb2tlbiA9IGdldENvb2tpZVZhbHVlKCdBY2Nlc3NUb2tlbicpO1xuICAgICAgICBpZiAoYWNjZXNzVG9rZW4pIHtcbiAgICAgICAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7YWNjZXNzVG9rZW59YDtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SRIEF1dGhvcml6YXRpb24gaGVhZGVyIGVrbGVuZGkgKEpTIHJlYWRhYmxlIGNvb2tpZSknKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SRIENvb2tpZSBIdHRwT25seSBvbGFiaWxpciAtIGJyb3dzZXIgb3RvbWF0aWsgZ8O2bmRlcmVjZWsnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBjb25maWc7XG4gICAgfSxcbiAgICAoZXJyb3IpID0+IHtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgICB9XG4pO1xuXG4vLyBSZXNwb25zZSBpbnRlcmNlcHRvciAtIFRva2VuIHllbmlsZW1lIHZlIGhhdGEgecO2bmV0aW1pXG5hcGlDbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgICAocmVzcG9uc2UpID0+IHtcbiAgICAgICAgLy8gQmHFn2FyxLFsxLEgcmVzcG9uc2UnbGFyZGEgbmV0d29yayBzdGF0dXMndSBvbmxpbmUnYSDDp2VrXG4gICAgICAgIGNvbnN0IGN1cnJlbnRTdGF0dXMgPSB1c2VOZXR3b3JrU3RvcmUuZ2V0U3RhdGUoKS5zdGF0dXM7XG4gICAgICAgIGlmIChjdXJyZW50U3RhdHVzICE9PSAnb25saW5lJykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBBUEkgYmHFn2FyxLFsxLEgLSBOZXR3b3JrIHN0YXR1cyBvbmxpbmVcXCdhIMOnZWtpbGl5b3InKTtcbiAgICAgICAgICAgIHVzZU5ldHdvcmtTdG9yZS5nZXRTdGF0ZSgpLnNldFN0YXR1cygnb25saW5lJyk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgIH0sXG4gICAgYXN5bmMgKGVycm9yKSA9PiB7XG4gICAgICAgIGNvbnN0IG9yaWdpbmFsUmVxdWVzdCA9IGVycm9yLmNvbmZpZztcblxuICAgICAgICAvLyBMb2dpbiBlbmRwb2ludCdpbmRlbiBnZWxlbiA0MDEgaGF0YXPEsW7EsSB0b2tlbiByZWZyZXNoIGTDtm5nw7xzw7xuZGVuIMOnxLFrYXJcbiAgICAgICAgLy8gVXNlci9NZSBlbmRwb2ludCdpbmkgYnUga29udHJvbGRlbiBrYWxkxLFyxLF5b3J1eiBraSB0b2tlbiBzw7xyZXNpIGRvbGR1xJ91bmRhIHllbmlsZXllYmlsc2luLlxuICAgICAgICAvLyBSRUZSRVNIIGVuZHBvaW50J2luaSBkZSBkw7ZuZ8O8ZGVuIMOnxLFrYXLEsXlvcnV6IGtpIHNvbnN1eiBkw7ZuZ8O8eWUgZ2lybWVzaW4uXG4gICAgICAgIGlmICgob3JpZ2luYWxSZXF1ZXN0LnVybD8uaW5jbHVkZXMoQVBJX0VORFBPSU5UUy5MT0dJTikgfHxcbiAgICAgICAgICAgIG9yaWdpbmFsUmVxdWVzdC51cmw/LmluY2x1ZGVzKEFQSV9FTkRQT0lOVFMuUkVGUkVTSF9UT0tFTikpICYmXG4gICAgICAgICAgICBlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBIxLBCUsSwVCDDh8OWWsOcTSAtIEh0dHBPbmx5IENvb2tpZSdsZXIgacOnaW4gR8O8bmNlbGxlbm1pxZ8gWWFrbGHFn8SxbTpcbiAgICAgICAgLy8gSHR0cE9ubHkgY29va2llJ2xlciBKYXZhU2NyaXB0IGlsZSBva3VuYW1heiwgYnUgecO8emRlbiBjb29raWUgdmFybMSxxJ/EsW7EsSBrb250cm9sIGVkZW1leWl6LlxuICAgICAgICAvLyBCdW51biB5ZXJpbmUsIHRva2VuIHllbmlsZW1lIGlzdGXEn2luaSBnw7ZuZGVyaXAgc29udWN1bmEgZ8O2cmUga2FyYXIgdmVyZWNlxJ9pei5cbiAgICAgICAgLy8gRcSfZXIgcmVmcmVzaCB0b2tlbiB5b2tzYSwgYmFja2VuZCA0MDEgZMO2bmTDvHJlY2VrIHZlIGJpeiBidW51IHlha2FsYXlhY2HEn8Sxei5cblxuICAgICAgICAvLyAxLiDDlk5DRUzEsEs6IFRva2VuIHllbmlsZW1lIG1hbnTEscSfxLFcbiAgICAgICAgLy8gNDAxIGR1cnVtdW5kYSB0b2tlbiB5ZW5pbGVtZSAtIHJldHJ5IGZsYWcga29udHJvbMO8IGlsZSBkw7ZuZ8O8ecO8IGVuZ2VsbGVcbiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSAmJiAhb3JpZ2luYWxSZXF1ZXN0Ll9yZXRyeSkge1xuICAgICAgICAgICAgb3JpZ2luYWxSZXF1ZXN0Ll9yZXRyeSA9IHRydWU7IC8vIEJ1IHJlcXVlc3QnaW4gcmV0cnkgZWRpbGRpxJ9pbmkgacWfYXJldGxlXG5cbiAgICAgICAgICAgIGlmIChpc1JlZnJlc2hpbmcpIHtcbiAgICAgICAgICAgICAgICAvLyBFxJ9lciByZWZyZXNoIGnFn2xlbWkgZGV2YW0gZWRpeW9yc2EsIGt1eXJ1xJ9hIGVrbGVcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBmYWlsZWRRdWV1ZS5wdXNoKHsgcmVzb2x2ZSwgcmVqZWN0IH0pO1xuICAgICAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAvLyBSZWZyZXNoIHRhbWFtbGFuZMSxa3RhbiBzb25yYSBvcmlqaW5hbCBpc3RlxJ9pIHRla3JhciBnw7ZuZGVyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBhcGlDbGllbnQob3JpZ2luYWxSZXF1ZXN0KTtcbiAgICAgICAgICAgICAgICB9KS5jYXRjaChlcnIgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgaXNSZWZyZXNoaW5nID0gdHJ1ZTtcblxuICAgICAgICAgICAgLy8gUmVmcmVzaCB0b2tlbiBpw6dpbiDDtnplbCByZXRyeSBtZWthbml6bWFzxLFcbiAgICAgICAgICAgIGNvbnN0IGF0dGVtcHRSZWZyZXNoID0gYXN5bmMgKHJldHJ5Q291bnQgPSAwKTogUHJvbWlzZTxhbnk+ID0+IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZWZyZXNoUmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuUkVGUkVTSF9UT0tFTik7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZWZyZXNoUmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAocmVmcmVzaEVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gUmV0cnkga2/Fn3VsbGFyxLE6XG4gICAgICAgICAgICAgICAgICAgIC8vIDEuIEHEnyBoYXRhc8SxICh0aW1lb3V0LCBjb25uZWN0aW9uIGVycm9yIHZiLilcbiAgICAgICAgICAgICAgICAgICAgLy8gMi4gNDAxIGhhdGFzxLEgYW1hIGhlbsO8eiBtYXggcmV0cnknYSB1bGHFn21hZMSxeXNhayAodGltZW91dCBuZWRlbml5bGUgNDAxIG9sYWJpbGlyKVxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpc05ldHdvcmtFcnJvciA9IGF4aW9zLmlzQXhpb3NFcnJvcihyZWZyZXNoRXJyb3IpICYmICFyZWZyZXNoRXJyb3IucmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzNDAxRXJyb3IgPSBheGlvcy5pc0F4aW9zRXJyb3IocmVmcmVzaEVycm9yKSAmJiByZWZyZXNoRXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzaG91bGRSZXRyeSA9IChpc05ldHdvcmtFcnJvciB8fCBpczQwMUVycm9yKSAmJiByZXRyeUNvdW50IDwgMjtcblxuICAgICAgICAgICAgICAgICAgICBpZiAoc2hvdWxkUmV0cnkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFJlZnJlc2ggdG9rZW4gZGVuZW1lc2kgJHtyZXRyeUNvdW50ICsgMX0vMyBiYcWfYXLEsXPEsXogKCR7aXNOZXR3b3JrRXJyb3IgPyAnQcSfIGhhdGFzxLEnIDogJzQwMSAtIFRpbWVvdXQgb2xhYmlsaXInfSkuICR7cmV0cnlDb3VudCA8IDEgPyAnVGVrcmFyIGRlbmVuaXlvci4uLicgOiAnU29uIGRlbmVtZSB5YXDEsWzEsXlvci4uLid9YCk7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIEV4cG9uZW50aWFsIGJhY2tvZmY6IDFzLCAycywgNHNcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlbGF5ID0gTWF0aC5wb3coMiwgcmV0cnlDb3VudCkgKiAxMDAwO1xuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIGRlbGF5KSk7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBhdHRlbXB0UmVmcmVzaChyZXRyeUNvdW50ICsgMSk7XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICAvLyBNYXggcmV0cnknYSB1bGHFn3TEsWsgdmV5YSBrZXNpbiBiaXIgaGF0YSBhbGTEsWtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgcmVmcmVzaEVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8gUmVmcmVzaCB0b2tlbiDDp2HEn3LEsXPEsSAtIHJldHJ5IG1la2FuaXptYXPEsSBpbGVcbiAgICAgICAgICAgICAgICBjb25zdCByZWZyZXNoUmVzcG9uc2UgPSBhd2FpdCBhdHRlbXB0UmVmcmVzaCgpO1xuXG4gICAgICAgICAgICAgICAgLy8gVMO8bSBiZWtsZXllbiBpc3Rla2xlcmkgYmHFn2FyxLFsxLEgb2xhcmFrIGnFn2FyZXRsZVxuICAgICAgICAgICAgICAgIHByb2Nlc3NRdWV1ZShudWxsKTtcbiAgICAgICAgICAgICAgICBpc1JlZnJlc2hpbmcgPSBmYWxzZTtcblxuICAgICAgICAgICAgICAgIC8vIEJhxZ9hcsSxbMSxIHJlZnJlc2ggc29ucmFzxLEgb3JpamluYWwgaXN0ZcSfaSB0ZWtyYXIgZ8O2bmRlclxuICAgICAgICAgICAgICAgIHJldHVybiBhcGlDbGllbnQob3JpZ2luYWxSZXF1ZXN0KTtcblxuICAgICAgICAgICAgfSBjYXRjaCAocmVmcmVzaEVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgICAgICAvLyDDlk5FTUzEsDogaXNSZWZyZXNoaW5nIGJheXJhxJ/EsW7EsSBtdXRsYWthIHPEsWbEsXJsYVxuICAgICAgICAgICAgICAgIGlzUmVmcmVzaGluZyA9IGZhbHNlO1xuXG4gICAgICAgICAgICAgICAgLy8gMyBkZW5lbWUgc29ucmFzxLFuZGEgZGEgNDAxIGFsxLF5b3JzYWssIGJ1IGdlcsOnZWt0ZW4ga3VsbGFuxLFjxLFuxLFuIGdpcmnFnyB5YXBtYWTEscSfxLEgYW5sYW3EsW5hIGdlbGlyXG4gICAgICAgICAgICAgICAgaWYgKGF4aW9zLmlzQXhpb3NFcnJvcihyZWZyZXNoRXJyb3IpICYmIHJlZnJlc2hFcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CfmqogMyBkZW5lbWUgc29ucmFzxLFuZGEgZGEgNDAxIGhhdGFzxLEuIEt1bGxhbsSxY8SxIGdlcsOnZWt0ZW4gZ2lyacWfIHlhcG1hbcSxxZ8uJyk7XG5cbiAgICAgICAgICAgICAgICAgICAgLy8gVMO8bSBiZWtsZXllbiBpc3Rla2xlcmkgb3JpamluYWwgaGF0YSBpbGUgcmVkZGV0XG4gICAgICAgICAgICAgICAgICAgIHByb2Nlc3NRdWV1ZShlcnJvciwgbnVsbCk7XG5cbiAgICAgICAgICAgICAgICAgICAgLy8gT3JpamluYWwgaGF0YXnEsSBkw7ZuZMO8ciBraSB1eWd1bGFtYSBcImdpcmnFnyB5YXBtYW3EscWfXCIgZHVydW11bmEgZ2XDp3NpblxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIERpxJ9lciBoYXRhbGFyIGnDp2luIG5vcm1hbCBpxZ9sZW1cbiAgICAgICAgICAgICAgICBwcm9jZXNzUXVldWUocmVmcmVzaEVycm9yLCBudWxsKTtcblxuICAgICAgICAgICAgICAgIGlmIChheGlvcy5pc0F4aW9zRXJyb3IocmVmcmVzaEVycm9yKSAmJiAhcmVmcmVzaEVycm9yLnJlc3BvbnNlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIDMgZGVuZW1lIHNvbnJhc8SxbmRhIGhhbGEgYcSfIGhhdGFzxLEgYWzEsXlvcnNhaywgYnUgY2lkZGkgYmlyIGJhxJ9sYW50xLEgc29ydW51XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SMIDMgZGVuZW1lIHNvbnJhc8SxbmRhIHJlZnJlc2ggdG9rZW4geWVuaWxlbmVtZWRpLiBBxJ8gYmHEn2xhbnTEsXPEsSBzb3J1bmx1LicpO1xuICAgICAgICAgICAgICAgICAgICB1c2VOZXR3b3JrU3RvcmUuZ2V0U3RhdGUoKS5zZXRTdGF0dXMoJ3JlY29ubmVjdGluZycpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5KlIEJla2xlbm1lZGlrIGhhdGEgc29ucmFzxLEgYXV0aDpmb3JjZS1sb2dvdXQgZXZlbnQgZ8O2bmRlcmlsaXlvcicpO1xuICAgICAgICAgICAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2F1dGg6Zm9yY2UtbG9nb3V0JykpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChyZWZyZXNoRXJyb3IpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gMi4gw5ZOQ0VMxLBLOiBHZW5lbCBhxJ8gaGF0YWxhcsSxbsSxIHlha2FsYW1hXG4gICAgICAgIC8vIEXEn2VyIGhhdGEgNDAxIGRlxJ9pbHNlIHZlIGJpciBhxJ8gaGF0YXPEsXlzYSAoc3VudWN1ZGFuIHlhbsSxdCB5b2tzYSksXG4gICAgICAgIC8vIGJ1IGdlbmVsIGJpciBpbnRlcm5ldCBrZXNpbnRpc2lkaXIuXG4gICAgICAgIGlmIChheGlvcy5pc0F4aW9zRXJyb3IoZXJyb3IpICYmICFlcnJvci5yZXNwb25zZSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIwgR2VuZWwgYcSfIGhhdGFzxLEgYWxnxLFsYW5kxLEuIFllbmlkZW4gYmHEn2xhbm1hIG1vZHVuYSBnZcOnaWxpeW9yLicpO1xuICAgICAgICAgICAgdXNlTmV0d29ya1N0b3JlLmdldFN0YXRlKCkuc2V0U3RhdHVzKCdyZWNvbm5lY3RpbmcnKTtcbiAgICAgICAgICAgIC8vIEJ1cmFkYSBoYXRhecSxIHl1dHVwIGJhbm5lcifEsW4gw6dhbMSxxZ9tYXPEsW5hIGl6aW4gdmVyaXlvcnV6LlxuICAgICAgICAgICAgLy8gQ29tcG9uZW50J2luIHRla3JhciBkZW5lbWVzaSBpw6dpbiBoYXRhecSxIHJlamVjdCBldG1peW9ydXoga2kgc8O8cmVrbGkgZXJyb3Igc3RhdGUnaSBnw7ZzdGVybWVzaW4uXG4gICAgICAgICAgICAvLyBCdW51biB5ZXJpbmUsIGJpciBkYWhhIGFzbGEgw6fDtnrDvGxtZXllY2VrIGJpciBwcm9taXNlIGTDtm5kw7xyZXJlayBpc3RlxJ9pIGFza8SxZGEgYsSxcmFrxLF5b3J1ei5cbiAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgoKSA9PiB7IH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gRGnEn2VyIHTDvG0gaGF0YWxhciAoNTAwLCA0MDQsIDQwMyB2Yi4pIG5vcm1hbCDFn2VraWxkZSBjb21wb25lbnRlIGdlcmkgZMO2bnPDvG4uXG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gICAgfVxuKTtcblxuZXhwb3J0IGRlZmF1bHQgYXBpQ2xpZW50O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBBRERSRVNTIFNFUlZJQ0VTXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBjb25zdCBhZGRyZXNzU2VydmljZSA9IHtcbiAgICAvLyBLdWxsYW7EsWPEsW7EsW4gYWRyZXNsZXJpbmkgbGlzdGVsZVxuICAgIGFzeW5jIGdldEFkZHJlc3NlcygpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk40gQWRyZXNsZXIgYWzEsW7EsXlvci4uLicpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuVVNFUl9BRERSRVNTRVMpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk40gQVBJIFJlc3BvbnNlOicsIHJlc3BvbnNlKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIFJlc3BvbnNlIGRhdGE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBSZXNwb25zZSBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIFJlc3BvbnNlIGRhdGEgdHlwZTonLCB0eXBlb2YgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBSZXNwb25zZSBkYXRhIGlzIGFycmF5OicsIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpO1xuXG4gICAgICAgICAgICAvLyBFxJ9lciByZXNwb25zZS5kYXRhIGJpciBvYmplY3QgaXNlLCBpw6dpbmRla2kgcHJvcGVydHknbGVyaSBrb250cm9sIGV0XG4gICAgICAgICAgICBpZiAodHlwZW9mIHJlc3BvbnNlLmRhdGEgPT09ICdvYmplY3QnICYmIHJlc3BvbnNlLmRhdGEgIT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBSZXNwb25zZSBkYXRhIGtleXM6JywgT2JqZWN0LmtleXMocmVzcG9uc2UuZGF0YSkpO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIFJlc3BvbnNlIGRhdGEgdmFsdWVzOicsIE9iamVjdC52YWx1ZXMocmVzcG9uc2UuZGF0YSkpO1xuXG4gICAgICAgICAgICAgICAgLy8gTXVodGVtZWwgbmVzdGVkIHlhcMSxbGFyxLEga29udHJvbCBldFxuICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmRhdGEpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk40gTmVzdGVkIGRhdGEgZm91bmQ6JywgcmVzcG9uc2UuZGF0YS5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk40gTmVzdGVkIGRhdGEgaXMgYXJyYXk6JywgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmRhdGEpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuYWRkcmVzc2VzKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIEFkZHJlc3NlcyBwcm9wZXJ0eSBmb3VuZDonLCByZXNwb25zZS5kYXRhLmFkZHJlc3Nlcyk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIEFkZHJlc3NlcyBpcyBhcnJheTonLCBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuYWRkcmVzc2VzKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnJlc3VsdCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBSZXN1bHQgcHJvcGVydHkgZm91bmQ6JywgcmVzcG9uc2UuZGF0YS5yZXN1bHQpO1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBSZXN1bHQgaXMgYXJyYXk6JywgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLnJlc3VsdCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gRmFya2zEsSByZXNwb25zZSB5YXDEsWxhcsSxbsSxIGRlbmVcbiAgICAgICAgICAgIGxldCBhZGRyZXNzRGF0YSA9IHJlc3BvbnNlLmRhdGE7XG5cbiAgICAgICAgICAgIC8vIEXEn2VyIHJlc3BvbnNlLmRhdGEuZGF0YSB2YXJzYSB2ZSBhcnJheSBpc2Ugb251IGt1bGxhblxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YSkpIHtcbiAgICAgICAgICAgICAgICBhZGRyZXNzRGF0YSA9IHJlc3BvbnNlLmRhdGEuZGF0YTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBVc2luZyBuZXN0ZWQgZGF0YSBhcnJheScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRcSfZXIgcmVzcG9uc2UuZGF0YS5hZGRyZXNzZXMgdmFyc2EgdmUgYXJyYXkgaXNlIG9udSBrdWxsYW5cbiAgICAgICAgICAgIGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEuYWRkcmVzc2VzICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5hZGRyZXNzZXMpKSB7XG4gICAgICAgICAgICAgICAgYWRkcmVzc0RhdGEgPSByZXNwb25zZS5kYXRhLmFkZHJlc3NlcztcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBVc2luZyBhZGRyZXNzZXMgcHJvcGVydHknKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEXEn2VyIHJlc3BvbnNlLmRhdGEucmVzdWx0IHZhcnNhIHZlIGFycmF5IGlzZSBvbnUga3VsbGFuXG4gICAgICAgICAgICBlbHNlIGlmIChyZXNwb25zZS5kYXRhLnJlc3VsdCAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEucmVzdWx0KSkge1xuICAgICAgICAgICAgICAgIGFkZHJlc3NEYXRhID0gcmVzcG9uc2UuZGF0YS5yZXN1bHQ7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk40gVXNpbmcgcmVzdWx0IHByb3BlcnR5Jyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBFxJ9lciByZXNwb25zZS5kYXRhIGRpcmVrdCBhcnJheSBpc2Ugb251IGt1bGxhblxuICAgICAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xuICAgICAgICAgICAgICAgIGFkZHJlc3NEYXRhID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBVc2luZyBkaXJlY3QgcmVzcG9uc2UgZGF0YScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRcSfZXIgaGnDp2JpcmkgZGXEn2lsc2UgYm/FnyBhcnJheVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfwn5ONIE5vIHZhbGlkIGFycmF5IGZvdW5kIGluIHJlc3BvbnNlLCB1c2luZyBlbXB0eSBhcnJheScpO1xuICAgICAgICAgICAgICAgIGFkZHJlc3NEYXRhID0gW107XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIEZpbmFsIGFkZHJlc3MgZGF0YTonLCBhZGRyZXNzRGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjSBGaW5hbCBhZGRyZXNzIGRhdGEgdHlwZTonLCB0eXBlb2YgYWRkcmVzc0RhdGEpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk40gRmluYWwgYWRkcmVzcyBkYXRhIGlzIGFycmF5OicsIEFycmF5LmlzQXJyYXkoYWRkcmVzc0RhdGEpKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5ONIEZpbmFsIGFkZHJlc3MgY291bnQ6JywgYWRkcmVzc0RhdGEubGVuZ3RoKTtcblxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IGFkZHJlc3NEYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQWRyZXNsZXIgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgcmVzcG9uc2U6JywgZXJyb3IucmVzcG9uc2UpO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGRhdGE6JywgZXJyb3IucmVzcG9uc2U/LmRhdGEpO1xuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlIHx8ICdBZHJlc2xlciBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBbXSAvLyBIYXRhIGR1cnVtdW5kYSBib8WfIGFycmF5IGTDtm5kw7xyXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIFllbmkgYWRyZXMgZWtsZVxuICAgIGFzeW5jIGNyZWF0ZUFkZHJlc3MoYWRkcmVzc0RhdGE6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4p6VIFllbmkgYWRyZXMgb2x1xZ90dXJ1bHV5b3I6JywgYWRkcmVzc0RhdGEpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLkNSRUFURV9BRERSRVNTLCBhZGRyZXNzRGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEFkcmVzIG9sdcWfdHVybWEgYmHFn2FyxLFsxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFkcmVzIGVrbGVuaXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnQWRyZXMgZWtsZW5pcmtlbiBiaXIgaGF0YSBvbHXFn3R1J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBBZHJlcyBzaWxcbiAgICBhc3luYyBkZWxldGVBZGRyZXNzKGFkZHJlc3NJZDogbnVtYmVyLCB1c2VySWQ6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+Xke+4jyBBZHJlcyBzaWxpbml5b3I6JywgeyBhZGRyZXNzSWQsIHVzZXJJZCB9KTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoQVBJX0VORFBPSU5UUy5ERUxFVEVfQUREUkVTUywgeyBhZGRyZXNzSWQsIHVzZXJJZCB9KTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgQWRyZXMgc2lsbWUgYmHFn2FyxLFsxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFkcmVzIHNpbGluaXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnQWRyZXMgc2lsaW5pcmtlbiBiaXIgaGF0YSBvbHXFn3R1J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBWYXJzYXnEsWxhbiBhZHJlcyBheWFybGFcbiAgICBhc3luYyBzZXREZWZhdWx0QWRkcmVzcyhhZGRyZXNzSWQ6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4q2QIFZhcnNhecSxbGFuIGFkcmVzIGF5YXJsYW7EsXlvcjonLCB7IGFkZHJlc3NJZCB9KTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoYCR7QVBJX0VORFBPSU5UUy5TRVRfREVGQVVMVF9BRERSRVNTfT9hZGRyZXNzSWQ9JHthZGRyZXNzSWR9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFZhcnNhecSxbGFuIGFkcmVzIGF5YXJsYW1hIGJhxZ9hcsSxbMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBWYXJzYXnEsWxhbiBhZHJlcyBheWFybGFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ1ZhcnNhecSxbGFuIGFkcmVzIGF5YXJsYW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dSdcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG59O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBVU0VSIFNFUlZJQ0VTXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBjb25zdCB1c2VyU2VydmljZSA9IHtcbiAgICAvLyBLdWxsYW7EsWPEsSBwcm9maWxpbmkgZ8O8bmNlbGxlXG4gICAgYXN5bmMgdXBkYXRlUHJvZmlsZShwcm9maWxlRGF0YToge1xuICAgICAgICBmaXJzdE5hbWU/OiBzdHJpbmc7XG4gICAgICAgIGxhc3ROYW1lPzogc3RyaW5nO1xuICAgICAgICBwaG9uZU51bWJlcj86IHN0cmluZztcbiAgICAgICAgZGF0ZU9mQmlydGg/OiBzdHJpbmc7XG4gICAgICAgIGdlbmRlcj86IG51bWJlcjtcbiAgICAgICAgbG9jYXRpb24/OiBzdHJpbmc7XG4gICAgfSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+RpCBQcm9maWwgZ8O8bmNlbGxlbml5b3I6JywgcHJvZmlsZURhdGEpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLlVQREFURV9QUk9GSUxFLCBwcm9maWxlRGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFByb2ZpbCBnw7xuY2VsbGVtZSBiYcWfYXLEsWzEsTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgUHJvZmlsIGfDvG5jZWxsZW5pcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdQcm9maWwgZ8O8bmNlbGxlbmlya2VuIGJpciBoYXRhIG9sdcWfdHUnXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIEFkbWluIGt1bGxhbsSxY8SxbGFyxLEgZ2V0aXIgKGZpbHRyZWxlbWUgdmUgc2F5ZmFsYW1hIGlsZSlcbiAgICBhc3luYyBnZXRVc2VycyhwYXJhbXM6IHtcbiAgICAgICAgcGFnZT86IG51bWJlcjtcbiAgICAgICAgcGFnZVNpemU/OiBudW1iZXI7XG4gICAgICAgIHNlYXJjaD86IHN0cmluZztcbiAgICAgICAgcm9sZUlkPzogbnVtYmVyO1xuICAgICAgICBpc0FjdGl2ZT86IGJvb2xlYW47XG4gICAgfSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBSZXF1ZXN0IGJvZHkgb2x1xZ90dXJcbiAgICAgICAgICAgIGNvbnN0IHJlcXVlc3RCb2R5OiBhbnkgPSB7XG4gICAgICAgICAgICAgICAgcGFnZTogcGFyYW1zLnBhZ2UgfHwgMSwgLy8gQmFja2VuZCAxLWJhc2VkIGluZGV4aW5nIGt1bGxhbsSxeW9yXG4gICAgICAgICAgICAgICAgcGFnZVNpemU6IHBhcmFtcy5wYWdlU2l6ZSB8fCAxMCxcbiAgICAgICAgICAgICAgICBzZWFyY2g6IHBhcmFtcy5zZWFyY2ggfHwgXCJcIlxuICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgLy8gU2FkZWNlIHJvbGVJZCAwJ2RhbiBmYXJrbMSxeXNhIGVrbGVcbiAgICAgICAgICAgIGlmIChwYXJhbXMucm9sZUlkICYmIHBhcmFtcy5yb2xlSWQgPiAwKSB7XG4gICAgICAgICAgICAgICAgcmVxdWVzdEJvZHkucm9sZUlkID0gcGFyYW1zLnJvbGVJZDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gU2FkZWNlIGlzQWN0aXZlIHBhcmFtZXRyZXNpIGfDtm5kZXJpbGRpeXNlIGVrbGVcbiAgICAgICAgICAgIGlmIChwYXJhbXMuaXNBY3RpdmUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHJlcXVlc3RCb2R5LmlzQWN0aXZlID0gcGFyYW1zLmlzQWN0aXZlO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuR0VUX1VTRVJTLCByZXF1ZXN0Qm9keSk7XG5cbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgS3VsbGFuxLFjxLFsYXIgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlIHx8ICdLdWxsYW7EsWPEsWxhciBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBbXVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBLdWxsYW7EsWPEsSByb2wgc2F5xLFsYXLEsW7EsSBnZXRpclxuICAgIGFzeW5jIGdldFVzZXJSb2xlQ291bnRzKCk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5HRVRfVVNFUl9ST0xFX0NPVU5UUyk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEt1bGxhbsSxY8SxIHJvbCBzYXnEsWxhcsSxIGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgZXJyb3IubWVzc2FnZSB8fCAnS3VsbGFuxLFjxLEgcm9sIHNhecSxbGFyxLEgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogbnVsbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBLdWxsYW7EsWPEsSBpbmRpcmltIG9yYW7EsW7EsSBnZXRpclxuICAgIGFzeW5jIGdldERpc2NvdW50UmF0ZSgpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CfkrAgS3VsbGFuxLFjxLEgaW5kaXJpbSBvcmFuxLEgYWzEsW7EsXlvci4uLicpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuR0VUX0RJU0NPVU5UX1JBVEUpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBLdWxsYW7EsWPEsSBpbmRpcmltIG9yYW7EsSBiYcWfYXLEsXlsYSBhbMSxbmTEsTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgS3VsbGFuxLFjxLEgaW5kaXJpbSBvcmFuxLEgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlIHx8ICdLdWxsYW7EsWPEsSBpbmRpcmltIG9yYW7EsSBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBudWxsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIFNlcGV0IHRpcGluaSBnw7xuY2VsbGUgKGN1c3RvbWVyIHByaWNlIHRvZ2dsZSlcbiAgICBhc3luYyB1cGRhdGVDYXJ0VHlwZSgpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfm5IgU2VwZXQgdGlwaSBnw7xuY2VsbGVuaXlvci4uLicpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLlVQREFURV9DQVJUX1RZUEUpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXBldCB0aXBpIGJhxZ9hcsSxeWxhIGfDvG5jZWxsZW5kaTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgU2VwZXQgdGlwaSBnw7xuY2VsbGVuaXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlIHx8ICdTZXBldCB0aXBpIGfDvG5jZWxsZW5pcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBudWxsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxufTtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gUFJPRFVDVCBTRVJWSUNFU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQgY29uc3QgcHJvZHVjdFNlcnZpY2UgPSB7XG4gICAgLy8gTWFya2FsYXLEsSBnZXRpclxuICAgIGFzeW5jIGdldEJyYW5kcygpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8gTWFya2FsYXIgYWzEsW7EsXlvci4uLicpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuR0VUX0JSQU5EUyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIE1hcmthbGFyIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBNYXJrYWxhciBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdNYXJrYWxhciBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBbXVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBNYXJrYXlhIGfDtnJlIGthdGVnb3JpbGVyaSBnZXRpclxuICAgIGFzeW5jIGdldENhdGVnb3JpZXNCeUJyYW5kKGJyYW5kSWQ6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TgiBLYXRlZ29yaWxlciBhbMSxbsSxeW9yLCBicmFuZElkOicsIGJyYW5kSWQpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAke0FQSV9FTkRQT0lOVFMuR0VUX0NBVEVHT1JJRVNfQllfQlJBTkR9LyR7YnJhbmRJZH1gKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgS2F0ZWdvcmlsZXIgYmHFn2FyxLF5bGEgYWzEsW5kxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEthdGVnb3JpbGVyIGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ0thdGVnb3JpbGVyIGFsxLFuxLFya2VuIGJpciBoYXRhIG9sdcWfdHUnLFxuICAgICAgICAgICAgICAgIGRhdGE6IFtdXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIEthdGVnb3JpeWUgZ8O2cmUgYWx0IGthdGVnb3JpbGVyaSBnZXRpclxuICAgIGFzeW5jIGdldFN1YkNhdGVnb3JpZXMoY2F0ZWdvcnlJZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OBIEFsdCBrYXRlZ29yaWxlciBhbMSxbsSxeW9yLCBjYXRlZ29yeUlkOicsIGNhdGVnb3J5SWQpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAke0FQSV9FTkRQT0lOVFMuR0VUX1NVQkNBVEVHT1JJRVN9LyR7Y2F0ZWdvcnlJZH0vc3ViY2F0ZWdvcmllc2ApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBBbHQga2F0ZWdvcmlsZXIgYmHFn2FyxLF5bGEgYWzEsW5kxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFsdCBrYXRlZ29yaWxlciBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdBbHQga2F0ZWdvcmlsZXIgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogW11cbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gQWx0IGthdGVnb3JpIMO2emVsbGlrbGVyaW5pIGdldGlyXG4gICAgYXN5bmMgZ2V0U3ViQ2F0ZWdvcnlGZWF0dXJlcyhzdWJDYXRlZ29yeUlkOiBudW1iZXIpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcgQWx0IGthdGVnb3JpIMO2emVsbGlrbGVyaSBhbMSxbsSxeW9yLCBzdWJDYXRlZ29yeUlkOicsIHN1YkNhdGVnb3J5SWQpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAke0FQSV9FTkRQT0lOVFMuR0VUX1NVQkNBVEVHT1JZX0ZFQVRVUkVTfS8ke3N1YkNhdGVnb3J5SWR9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEFsdCBrYXRlZ29yaSDDtnplbGxpa2xlcmkgYmHFn2FyxLF5bGEgYWzEsW5kxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFsdCBrYXRlZ29yaSDDtnplbGxpa2xlcmkgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnQWx0IGthdGVnb3JpIMO2emVsbGlrbGVyaSBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBbXVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyDDlnplbGxpayBkZcSfZXJsZXJpbmkgZ2V0aXJcbiAgICBhc3luYyBnZXRGZWF0dXJlVmFsdWVzKGRlZmluaXRpb25JZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn4+377iPIMOWemVsbGlrIGRlxJ9lcmxlcmkgYWzEsW7EsXlvciwgZGVmaW5pdGlvbklkOicsIGRlZmluaXRpb25JZCk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYCR7QVBJX0VORFBPSU5UUy5HRVRfRkVBVFVSRV9WQUxVRVN9LyR7ZGVmaW5pdGlvbklkfWApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDlnplbGxpayBkZcSfZXJsZXJpIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDDlnplbGxpayBkZcSfZXJsZXJpIGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8OWemVsbGlrIGRlxJ9lcmxlcmkgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogW11cbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gVMO8bSDDtnplbGxpayB0YW7EsW1sYXLEsW7EsSBnZXRpclxuICAgIGFzeW5jIGdldEFsbEZlYXR1cmVEZWZpbml0aW9ucygpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gVMO8bSDDtnplbGxpayB0YW7EsW1sYXLEsSBhbMSxbsSxeW9yLi4uJyk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5HRVRfQUxMX0ZFQVRVUkVfREVGSU5JVElPTlMpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBUw7xtIMO2emVsbGlrIHRhbsSxbWxhcsSxIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBUw7xtIMO2emVsbGlrIHRhbsSxbWxhcsSxIGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ1TDvG0gw7Z6ZWxsaWsgdGFuxLFtbGFyxLEgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogW11cbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gWWVuaSDDvHLDvG4gZWtsZSAodGFtIMO8csO8biAtIHZhcnlhbnRsYXJsYSBiaXJsaWt0ZSlcbiAgICBhc3luYyBjcmVhdGVGdWxsUHJvZHVjdChwcm9kdWN0RGF0YTogYW55KTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinpUgVGFtIMO8csO8biBvbHXFn3R1cnVsdXlvcjonLCBwcm9kdWN0RGF0YSk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuQ1JFQVRFX0ZVTExfUFJPRFVDVCwgcHJvZHVjdERhdGEsIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YSdcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgVGFtIMO8csO8biBvbHXFn3R1cm1hIGJhxZ9hcsSxbMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBUYW0gw7xyw7xuIG9sdcWfdHVydWx1cmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gb2x1xZ90dXJ1bHVya2VuIGJpciBoYXRhIG9sdcWfdHUnXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIFNhdMSxY8SxIMO8csO8bsO8IGVrbGUgKGRlYWxlcnNoaXAgcHJvZHVjdCAtIFBWL0NWL1NQIG9sbWFkYW4pXG4gICAgYXN5bmMgY3JlYXRlRGVhbGVyc2hpcFByb2R1Y3QocHJvZHVjdERhdGE6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4p6VIFNhdMSxY8SxIMO8csO8bsO8IG9sdcWfdHVydWx1eW9yOicsIHByb2R1Y3REYXRhKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoQVBJX0VORFBPSU5UUy5DUkVBVEVfREVBTEVSU0hJUF9QUk9EVUNULCBwcm9kdWN0RGF0YSwge1xuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTYXTEsWPEsSDDvHLDvG7DvCBvbHXFn3R1cm1hIGJhxZ9hcsSxbMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTYXTEsWPEsSDDvHLDvG7DvCBvbHXFn3R1cnVsdXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnw5xyw7xuIG9sdcWfdHVydWx1cmtlbiBiaXIgaGF0YSBvbHXFn3R1J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyDDnHLDvG4gZ8O2cnNlbGkgZWtsZVxuICAgIGFzeW5jIGFkZFByb2R1Y3RJbWFnZShpbWFnZURhdGE6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+WvO+4jyDDnHLDvG4gZ8O2cnNlbGkgZWtsZW5peW9yOicsIGltYWdlRGF0YSk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuQUREX1BST0RVQ1RfSU1BR0UsIGltYWdlRGF0YSwge1xuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDnHLDvG4gZ8O2cnNlbGkgZWtsZW1lIGJhxZ9hcsSxbMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDDnHLDvG4gZ8O2cnNlbGkgZWtsZW5pcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gZ8O2cnNlbGkgZWtsZW5pcmtlbiBiaXIgaGF0YSBvbHXFn3R1J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyDDnHLDvG4gZ8O2cnNlbGluaSBzaWxcbiAgICBhc3luYyBkZWxldGVQcm9kdWN0SW1hZ2UoaW1hZ2VJZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIMOccsO8biBnw7Zyc2VsaSBzaWxpbml5b3IsIGltYWdlSWQ6JywgaW1hZ2VJZCk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5kZWxldGUoYCR7QVBJX0VORFBPSU5UUy5ERUxFVEVfUFJPRFVDVF9JTUFHRX0vJHtpbWFnZUlkfWApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDnHLDvG4gZ8O2cnNlbGkgc2lsbWUgYmHFn2FyxLFsxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIMOccsO8biBnw7Zyc2VsaSBzaWxpbmlya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8OccsO8biBnw7Zyc2VsaSBzaWxpbmlya2VuIGJpciBoYXRhIG9sdcWfdHUnXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIMOccsO8biBnw7Zyc2VsaW5pIGRlxJ9pxZ90aXJcbiAgICBhc3luYyByZXBsYWNlUHJvZHVjdEltYWdlKGltYWdlSWQ6IG51bWJlciwgbmV3SW1hZ2VGaWxlOiBGaWxlKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIMOccsO8biBnw7Zyc2VsaSBkZcSfacWfdGlyaWxpeW9yLCBpbWFnZUlkOicsIGltYWdlSWQpO1xuICAgICAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcbiAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIG5ld0ltYWdlRmlsZSk7XG5cbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnB1dChgJHtBUElfRU5EUE9JTlRTLlJFUExBQ0VfUFJPRFVDVF9JTUFHRX0vJHtpbWFnZUlkfWAsIGZvcm1EYXRhLCB7XG4gICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIMOccsO8biBnw7Zyc2VsaSBkZcSfacWfdGlybWUgYmHFn2FyxLFsxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIMOccsO8biBnw7Zyc2VsaSBkZcSfacWfdGlyaWxpcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gZ8O2cnNlbGkgZGXEn2nFn3RpcmlsaXJrZW4gYmlyIGhhdGEgb2x1xZ90dSdcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gQWRtaW4gw7xyw7xubGVyaW5pIGdldGlyXG4gICAgYXN5bmMgZ2V0QWRtaW5Qcm9kdWN0cyhwYXJhbXM6IHsgcGFnZU51bWJlcj86IG51bWJlcjsgcGFnZVNpemU/OiBudW1iZXI7IHByb2R1Y3ROYW1lPzogc3RyaW5nIH0pOiBQcm9taXNlPEFkbWluUHJvZHVjdFtdPiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5HRVRfQURNSU5fUFJPRFVDVFMsIHsgcGFyYW1zIH0pO1xuICAgICAgICAgICAgcmV0dXJuIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkgPyByZXNwb25zZS5kYXRhIDogW107XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQWRtaW4gw7xyw7xubGVyaSBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiBbXTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBLdWxsYW7EsWPEsXlhIGFpdCDDvHLDvG5sZXJpIGdldGlyXG4gICAgYXN5bmMgZ2V0TXlQcm9kdWN0cyhwYXJhbXM6IHsgcGFnZTogbnVtYmVyOyBwYWdlU2l6ZTogbnVtYmVyOyBzdGF0dXM/OiBudW1iZXI7IG5hbWU/OiBzdHJpbmcgfSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TpiBLdWxsYW7EsWPEsXlhIGFpdCDDvHLDvG5sZXIgYWzEsW7EsXlvcjonLCBwYXJhbXMpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLkdFVF9NWV9QUk9EVUNUUywgcGFyYW1zKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgS3VsbGFuxLFjxLF5YSBhaXQgw7xyw7xubGVyIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBLdWxsYW7EsWPEsXlhIGFpdCDDvHLDvG5sZXIgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnw5xyw7xubGVyIGFsxLFuxLFya2VuIGJpciBoYXRhIG9sdcWfdHUnLFxuICAgICAgICAgICAgICAgIGRhdGE6IG51bGxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gRGVhbGVyc2hpcCDDvHLDvG4gZGV0YXnEsW7EsSBnZXRpclxuICAgIGFzeW5jIGdldERlYWxlcnNoaXBQcm9kdWN0RGV0YWlsKHByb2R1Y3RJZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OmIERlYWxlcnNoaXAgw7xyw7xuIGRldGF5xLEgYWzEsW7EsXlvciwgcHJvZHVjdElkOicsIHByb2R1Y3RJZCk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYCR7QVBJX0VORFBPSU5UUy5HRVRfREVBTEVSU0hJUF9QUk9EVUNUX0RFVEFJTH0vJHtwcm9kdWN0SWR9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIERlYWxlcnNoaXAgw7xyw7xuIGRldGF5xLEgYmHFn2FyxLF5bGEgYWzEsW5kxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIERlYWxlcnNoaXAgw7xyw7xuIGRldGF5xLEgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnw5xyw7xuIGRldGF5xLEgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogbnVsbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyDDnHLDvG7DvCBzaWxcbiAgICBhc3luYyBkZWxldGVQcm9kdWN0KHByb2R1Y3RJZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIMOccsO8biBzaWxpbml5b3IsIHByb2R1Y3RJZDonLCBwcm9kdWN0SWQpO1xuICAgICAgICAgICAgLy8gQVBJIEdFVCBtZXRvZHUgdmUgcXVlcnkgcGFyYW1ldHJlc2kgYmVrbGVkacSfaSBpw6dpbiBvbmEgdXlndW4gaXN0ZWsgYXTEsXlvcnV6LlxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuREVMRVRFX1BST0RVQ1QsIHtcbiAgICAgICAgICAgICAgICBwYXJhbXM6IHsgcHJvZHVjdElkIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDnHLDvG4gc2lsbWUgYmHFn2FyxLFsxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIMOccsO8biBzaWxpbmlya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8OccsO8biBzaWxpbmlya2VuIGJpciBoYXRhIG9sdcWfdHUnXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIMOccsO8bsO8IGfDvG5jZWxsZSAodGFtIMO8csO8biAtIHZhcnlhbnRsYXJsYSBiaXJsaWt0ZSlcbiAgICBhc3luYyB1cGRhdGVGdWxsUHJvZHVjdChwcm9kdWN0RGF0YTogYW55KTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFRhbSDDvHLDvG4gZ8O8bmNlbGxlbml5b3I6JywgcHJvZHVjdERhdGEpO1xuXG4gICAgICAgICAgICAvLyBGb3JtRGF0YSBpw6dlcmnEn2luaSBkZXRheWzEsSBsb2dsYVxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4sgRm9ybURhdGEgacOnZXJpxJ9pOicpO1xuICAgICAgICAgICAgZm9yIChsZXQgW2tleSwgdmFsdWVdIG9mIHByb2R1Y3REYXRhLmVudHJpZXMoKSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGAke2tleX06YCwgdmFsdWUpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuVVBEQVRFX0ZVTExfUFJPRFVDVCwgcHJvZHVjdERhdGEsIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YSdcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgVGFtIMO8csO8biBnw7xuY2VsbGVtZSBiYcWfYXLEsWzEsTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgVGFtIMO8csO8biBnw7xuY2VsbGVuaXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgSGF0YSBkZXRheWxhcsSxOicsIGVycm9yLnJlc3BvbnNlPy5kYXRhKTtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBIVFRQIFN0YXR1czonLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzKTtcblxuICAgICAgICAgICAgLy8gSGF0YSBtZXNhasSxbsSxIGRhaGEgZGV0YXlsxLEgZMO2bmTDvHJcbiAgICAgICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8XG4gICAgICAgICAgICAgICAgZXJyb3IucmVzcG9uc2U/LmRhdGE/LnRpdGxlIHx8XG4gICAgICAgICAgICAgICAgZXJyb3IubWVzc2FnZSB8fFxuICAgICAgICAgICAgICAgICfDnHLDvG4gZ8O8bmNlbGxlbmlya2VuIGJpciBoYXRhIG9sdcWfdHUnO1xuXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBBZG1pbiDDvHLDvG4gaXN0YXRpc3Rpa2xlcmluaSBnZXRpclxuICAgIGFzeW5jIGdldEFkbWluUHJvZHVjdFN0YXRpc3RpY3MoKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEFkbWluIMO8csO8biBpc3RhdGlzdGlrbGVyaSBhbMSxbsSxeW9yLi4uJyk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5HRVRfQURNSU5fUFJPRFVDVF9TVEFUSVNUSUNTKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgQWRtaW4gw7xyw7xuIGlzdGF0aXN0aWtsZXJpIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBBZG1pbiDDvHLDvG4gaXN0YXRpc3Rpa2xlcmkgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnxLBzdGF0aXN0aWtsZXIgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogbnVsbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBLdWxsYW7EsWPEsSDDvHLDvG4gaXN0YXRpc3Rpa2xlcmluaSBnZXRpclxuICAgIGFzeW5jIGdldE15UHJvZHVjdFN0YXRpc3RpY3MoKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEt1bGxhbsSxY8SxIMO8csO8biBpc3RhdGlzdGlrbGVyaSBhbMSxbsSxeW9yLi4uJyk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5HRVRfTVlfUFJPRFVDVF9TVEFUSVNUSUNTKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgS3VsbGFuxLFjxLEgw7xyw7xuIGlzdGF0aXN0aWtsZXJpIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEuZGF0YSAvLyBBUEkgcmVzcG9uc2UndSBkYXRhIHdyYXBwZXInxLEgacOnaW5kZSBnZWxpeW9yXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgS3VsbGFuxLFjxLEgw7xyw7xuIGlzdGF0aXN0aWtsZXJpIGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8Swc3RhdGlzdGlrbGVyIGFsxLFuxLFya2VuIGJpciBoYXRhIG9sdcWfdHUnLFxuICAgICAgICAgICAgICAgIGRhdGE6IG51bGxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gQmFzaXQgw7xyw7xuIGfDvG5jZWxsZW1lIChkZWFsZXJzaGlwIGnDp2luKVxuICAgIGFzeW5jIHVwZGF0ZVNpbXBsZVByb2R1Y3QocHJvZHVjdEZvcm1EYXRhOiBGb3JtRGF0YSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEFQSSBTZXJ2aWNlOiBCYXNpdCDDvHLDvG4gZ8O8bmNlbGxlbml5b3IuLi4nKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflJcgRW5kcG9pbnQ6JywgQVBJX0VORFBPSU5UUy5VUERBVEVfU0lNUExFX1BST0RVQ1QpO1xuXG4gICAgICAgIC8vIEZvcm1EYXRhIGnDp2VyacSfaW5pIGxvZ2xhXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OLIEFQSSBTZXJ2aWNlOiBGb3JtRGF0YSBjb250ZW50czonKTtcbiAgICAgICAgZm9yIChsZXQgW2tleSwgdmFsdWVdIG9mIHByb2R1Y3RGb3JtRGF0YS5lbnRyaWVzKCkpIHtcbiAgICAgICAgICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIEZpbGUpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgICAke2tleX06IEZpbGUoJHt2YWx1ZS5uYW1lfSwgJHt2YWx1ZS5zaXplfSBieXRlcywgJHt2YWx1ZS50eXBlfSlgKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coYCAgJHtrZXl9OiAke3ZhbHVlfWApO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLlVQREFURV9TSU1QTEVfUFJPRFVDVCwgcHJvZHVjdEZvcm1EYXRhLCB7XG4gICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgQVBJIFNlcnZpY2U6IEJhc2l0IMO8csO8biBiYcWfYXLEsXlsYSBnw7xuY2VsbGVuZGknKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4QgUmVzcG9uc2Ugc3RhdHVzOicsIHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OEIFJlc3BvbnNlIGRhdGE6JywgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgfSxcblxuICAgIC8vIMOccsO8biBkZXRhecSxbsSxIGdldGlyXG4gICAgYXN5bmMgZ2V0UHJvZHVjdERldGFpbChwcm9kdWN0SWQ6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TpiDDnHLDvG4gZGV0YXnEsSBhbMSxbsSxeW9yLCBwcm9kdWN0SWQ6JywgcHJvZHVjdElkKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgJHtBUElfRU5EUE9JTlRTLkdFVF9QUk9EVUNUX0RFVEFJTH0vJHtwcm9kdWN0SWR9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIMOccsO8biBkZXRhecSxIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDDnHLDvG4gZGV0YXnEsSBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gZGV0YXnEsSBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBudWxsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcblxuICAgIC8vIENhdGFsb2cgw7xyw7xuIGRldGF5xLFuxLEgZ2V0aXIgKHB1YmxpYylcbiAgICBhc3luYyBnZXRDYXRhbG9nUHJvZHVjdERldGFpbChwcm9kdWN0SWQ6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TpiBDYXRhbG9nIMO8csO8biBkZXRhecSxIGFsxLFuxLF5b3IsIHByb2R1Y3RJZDonLCBwcm9kdWN0SWQpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAke0FQSV9FTkRQT0lOVFMuR0VUX0NBVEFMT0dfUFJPRFVDVF9ERVRBSUx9LyR7cHJvZHVjdElkfWApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBDYXRhbG9nIMO8csO8biBkZXRhecSxIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBDYXRhbG9nIMO8csO8biBkZXRhecSxIGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8OccsO8biBkZXRhecSxIGFsxLFuxLFya2VuIGJpciBoYXRhIG9sdcWfdHUnLFxuICAgICAgICAgICAgICAgIGRhdGE6IG51bGxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gw5xyw7xuIGR1cnVtdW51IGfDvG5jZWxsZSAob25heS9yZWQpXG4gICAgYXN5bmMgdXBkYXRlUHJvZHVjdFN0YXR1cyhwcm9kdWN0SWQ6IG51bWJlciwgaXNBcHByb3ZlZDogYm9vbGVhbiwgbWVzc2FnZTogc3RyaW5nKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIMOccsO8biBkdXJ1bXUgZ8O8bmNlbGxlbml5b3I6JywgeyBwcm9kdWN0SWQsIGlzQXBwcm92ZWQsIG1lc3NhZ2UgfSk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KEFQSV9FTkRQT0lOVFMuVVBEQVRFX1BST0RVQ1RfU1RBVFVTLCB7XG4gICAgICAgICAgICAgICAgcHJvZHVjdElkLFxuICAgICAgICAgICAgICAgIGlzQXBwcm92ZWQsXG4gICAgICAgICAgICAgICAgbWVzc2FnZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIMOccsO8biBkdXJ1bXUgYmHFn2FyxLF5bGEgZ8O8bmNlbGxlbmRpOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDDnHLDvG4gZHVydW11IGfDvG5jZWxsZW5pcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gZHVydW11IGfDvG5jZWxsZW5pcmtlbiBiaXIgaGF0YSBvbHXFn3R1J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyDDnHLDvG4gYWRtaW4gbm90dW51IGdldGlyXG4gICAgYXN5bmMgZ2V0UHJvZHVjdE1lc3NhZ2UocHJvZHVjdElkOiBudW1iZXIpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk50gw5xyw7xuIGFkbWluIG5vdHUgYWzEsW7EsXlvciwgcHJvZHVjdElkOicsIHByb2R1Y3RJZCk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYCR7QVBJX0VORFBPSU5UUy5HRVRfUFJPRFVDVF9NRVNTQUdFfS8ke3Byb2R1Y3RJZH1gKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgw5xyw7xuIGFkbWluIG5vdHUgYmHFn2FyxLF5bGEgYWzEsW5kxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIMOccsO8biBhZG1pbiBub3R1IGFsxLFuxLFya2VuIGhhdGE6JywgZXJyb3IpO1xuXG4gICAgICAgICAgICAvLyA0MDQgaGF0YXPEsSBpc2Uga2F5xLF0IHlvayBkZW1la3RpclxuICAgICAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwNCkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIGRhdGE6IG51bGxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBEacSfZXIgaGF0YWxhciBpw6dpbiBmYWxzZSBkw7ZuZMO8clxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8OccsO8biBhZG1pbiBub3R1IGFsxLFuxLFya2VuIGJpciBoYXRhIG9sdcWfdHUnLFxuICAgICAgICAgICAgICAgIGRhdGE6IG51bGxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gVMO8bSBrYXRlZ29yaWxlcmkgZ2V0aXIgKHB1YmxpYylcbiAgICBhc3luYyBnZXRDYXRlZ29yaWVzKCk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TgiBLYXRlZ29yaWxlciBhbMSxbsSxeW9yLi4uJyk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoQVBJX0VORFBPSU5UUy5HRVRfQ0FURUdPUklFUyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEthdGVnb3JpbGVyIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBLYXRlZ29yaWxlciBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdLYXRlZ29yaWxlciBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBbXVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBLYXRlZ29yaXllIGfDtnJlIGFsdCBrYXRlZ29yaWxlcmkgZ2V0aXIgKHB1YmxpYylcbiAgICBhc3luYyBnZXRTdWJDYXRlZ29yaWVzQnlDYXRlZ29yeShjYXRlZ29yeUlkOiBudW1iZXIpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4EgQWx0IGthdGVnb3JpbGVyIGFsxLFuxLF5b3IsIGNhdGVnb3J5SWQ6JywgY2F0ZWdvcnlJZCk7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYCR7QVBJX0VORFBPSU5UUy5HRVRfU1VCQ0FURUdPUklFU19CWV9DQVRFR09SWS5yZXBsYWNlKCd7Y2F0ZWdvcnlJZH0nLCBjYXRlZ29yeUlkLnRvU3RyaW5nKCkpfWApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBBbHQga2F0ZWdvcmlsZXIgYmHFn2FyxLF5bGEgYWzEsW5kxLE6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFsdCBrYXRlZ29yaWxlciBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdBbHQga2F0ZWdvcmlsZXIgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogW11cbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gw5xyw7xubGVyaSBmaWx0cmVsZSAocHVibGljKVxuICAgIGFzeW5jIGZpbHRlclByb2R1Y3RzKGZpbHRlclJlcXVlc3Q6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSDDnHLDvG5sZXIgZmlsdHJlbGVuaXlvcjonLCBmaWx0ZXJSZXF1ZXN0KTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoQVBJX0VORFBPSU5UUy5GSUxURVJfUFJPRFVDVFMsIGZpbHRlclJlcXVlc3QpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDnHLDvG5sZXIgYmHFn2FyxLF5bGEgZmlsdHJlbGVuZGk6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIMOccsO8bmxlciBmaWx0cmVsZW5pcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG5sZXIgZmlsdHJlbGVuaXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogbnVsbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBSZWZlcmVuY2UgZGF0YSBnZXRpciAocHVibGljKVxuICAgIGFzeW5jIGdldFJlZmVyZW5jZURhdGEoKTogUHJvbWlzZTxhbnk+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OLIFJlZmVyZW5jZSBkYXRhIGFsxLFuxLF5b3IuLi4nKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChBUElfRU5EUE9JTlRTLkdFVF9SRUZFUkVOQ0VfREFUQSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFJlZmVyZW5jZSBkYXRhIGJhxZ9hcsSxeWxhIGFsxLFuZMSxOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBSZWZlcmVuY2UgZGF0YSBhbMSxbsSxcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdSZWZlcmVuY2UgZGF0YSBhbMSxbsSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1JyxcbiAgICAgICAgICAgICAgICBkYXRhOiBudWxsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSxcbn07XG5cbmV4cG9ydCBjb25zdCBjYXJ0U2VydmljZSA9IHtcbiAgICAvLyBTZXBldGUgw7xyw7xuIGVrbGVcbiAgICBhc3luYyBhZGRUb0NhcnQocHJvZHVjdFZhcmlhbnRJZDogbnVtYmVyLCBxdWFudGl0eTogbnVtYmVyLCBpc0N1c3RvbWVyUHJpY2U6IGJvb2xlYW4pOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfm5IgU2VwZXRlIMO8csO8biBla2xlbml5b3I6JywgeyBwcm9kdWN0VmFyaWFudElkLCBxdWFudGl0eSwgaXNDdXN0b21lclByaWNlIH0pO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChBUElfRU5EUE9JTlRTLkFERF9UT19DQVJULCB7XG4gICAgICAgICAgICAgICAgcHJvZHVjdFZhcmlhbnRJZCxcbiAgICAgICAgICAgICAgICBxdWFudGl0eSxcbiAgICAgICAgICAgICAgICBpc0N1c3RvbWVyUHJpY2VcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDnHLDvG4gc2VwZXRlIGJhxZ9hcsSxeWxhIGVrbGVuZGk6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFNlcGV0ZSDDvHLDvG4gZWtsZW5pcmtlbiBoYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gc2VwZXRlIGVrbGVuaXJrZW4gYmlyIGhhdGEgb2x1xZ90dSdcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxuXG4gICAgLy8gU2VwZXQgacOnZXJpa2xlcmluaSBnZXRpclxuICAgIGFzeW5jIGdldENhcnRJdGVtcygpOiBQcm9taXNlPGFueT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfm5IgU2VwZXQgacOnZXJpa2xlcmkgYWzEsW7EsXlvci4uLicpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuR0VUX0NBUlRfSVRFTVMpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXBldCBpw6dlcmlrbGVyaSBiYcWfYXLEsXlsYSBhbMSxbmTEsTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgU2VwZXQgacOnZXJpa2xlcmkgYWzEsW7EsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnU2VwZXQgacOnZXJpa2xlcmkgYWzEsW7EsXJrZW4gYmlyIGhhdGEgb2x1xZ90dScsXG4gICAgICAgICAgICAgICAgZGF0YTogbnVsbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBTZXBldHRlbiDDvHLDvG4gw6fEsWthclxuICAgIGFzeW5jIHJlbW92ZUZyb21DYXJ0KHByb2R1Y3RWYXJpYW50SWQ6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+Xke+4jyBTZXBldHRlbiDDvHLDvG4gw6fEsWthcsSxbMSxeW9yOicsIHsgcHJvZHVjdFZhcmlhbnRJZCB9KTtcbiAgICAgICAgICAgIGNvbnN0IHVybCA9IGAke0FQSV9FTkRQT0lOVFMuUkVNT1ZFX0ZST01fQ0FSVH0vJHtwcm9kdWN0VmFyaWFudElkfWA7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBBUEkgVVJMOicsIHVybCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBBUElfRU5EUE9JTlRTLlJFTU9WRV9GUk9NX0NBUlQ6JywgQVBJX0VORFBPSU5UUy5SRU1PVkVfRlJPTV9DQVJUKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmRlbGV0ZSh1cmwpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDDnHLDvG4gc2VwZXR0ZW4gYmHFn2FyxLF5bGEgw6fEsWthcsSxbGTEsTonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgU2VwZXR0ZW4gw7xyw7xuIMOnxLFrYXLEsWzEsXJrZW4gaGF0YTonLCBlcnJvcik7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgcmVzcG9uc2U6JywgZXJyb3IucmVzcG9uc2UpO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIHN0YXR1czonLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzKTtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBkYXRhOicsIGVycm9yLnJlc3BvbnNlPy5kYXRhKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfDnHLDvG4gc2VwZXR0ZW4gw6fEsWthcsSxbMSxcmtlbiBiaXIgaGF0YSBvbHXFn3R1J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG5cbiAgICAvLyBTZXBldCDDvHLDvG4gbWlrdGFyxLFuxLEgZ8O8bmNlbGxlXG4gICAgYXN5bmMgdXBkYXRlQ2FydFF1YW50aXR5KHByb2R1Y3RWYXJpYW50SWQ6IG51bWJlciwgcXVhbnRpdHk6IG51bWJlcik6IFByb21pc2U8YW55PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBTZXBldCDDvHLDvG4gbWlrdGFyxLEgZ8O8bmNlbGxlbml5b3I6JywgeyBwcm9kdWN0VmFyaWFudElkLCBxdWFudGl0eSB9KTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoQVBJX0VORFBPSU5UUy5VUERBVEVfQ0FSVF9RVUFOVElUWSwge1xuICAgICAgICAgICAgICAgIHByb2R1Y3RWYXJpYW50SWQsXG4gICAgICAgICAgICAgICAgcXVhbnRpdHlcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXBldCDDvHLDvG4gbWlrdGFyxLEgYmHFn2FyxLF5bGEgZ8O8bmNlbGxlbmRpOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTZXBldCDDvHLDvG4gbWlrdGFyxLEgZ8O8bmNlbGxlbmlya2VuIGhhdGE6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ8OccsO8biBtaWt0YXLEsSBnw7xuY2VsbGVuaXJrZW4gYmlyIGhhdGEgb2x1xZ90dSdcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LFxufTsiXSwibmFtZXMiOlsiYXhpb3MiLCJheGlvc1JldHJ5IiwiQVBJX0VORFBPSU5UUyIsInVzZU5ldHdvcmtTdG9yZSIsIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiZ2V0Q29va2llVmFsdWUiLCJuYW1lIiwiZG9jdW1lbnQiLCJ2YWx1ZSIsImNvb2tpZSIsInBhcnRzIiwic3BsaXQiLCJsZW5ndGgiLCJjb29raWVWYWx1ZSIsInBvcCIsInNoaWZ0IiwiYXBpQ2xpZW50IiwiY3JlYXRlIiwiYmFzZVVSTCIsImhlYWRlcnMiLCJ0aW1lb3V0Iiwid2l0aENyZWRlbnRpYWxzIiwicmV0cmllcyIsInJldHJ5Q29uZGl0aW9uIiwiZXJyb3IiLCJyZXNwb25zZSIsInN0YXR1cyIsImlzTmV0d29ya0Vycm9yIiwiaXNJZGVtcG90ZW50UmVxdWVzdEVycm9yIiwicmV0cnlEZWxheSIsInJldHJ5Q291bnQiLCJjb25zb2xlIiwid2FybiIsIm1lc3NhZ2UiLCJNYXRoIiwicG93IiwiaXNSZWZyZXNoaW5nIiwiZmFpbGVkUXVldWUiLCJwcm9jZXNzUXVldWUiLCJ0b2tlbiIsImZvckVhY2giLCJyZXNvbHZlIiwicmVqZWN0IiwicHVibGljRW5kcG9pbnRzIiwiTE9HSU4iLCJSRUdJU1RFUiIsIlJFRlJFU0hfVE9LRU4iLCJMT0dPVVQiLCJpbnRlcmNlcHRvcnMiLCJyZXF1ZXN0IiwidXNlIiwiY29uZmlnIiwibG9nIiwibWV0aG9kIiwidG9VcHBlckNhc2UiLCJ1cmwiLCJhY2Nlc3NUb2tlbiIsIkF1dGhvcml6YXRpb24iLCJQcm9taXNlIiwiY3VycmVudFN0YXR1cyIsImdldFN0YXRlIiwic2V0U3RhdHVzIiwib3JpZ2luYWxSZXF1ZXN0IiwiaW5jbHVkZXMiLCJfcmV0cnkiLCJwdXNoIiwidGhlbiIsImNhdGNoIiwiZXJyIiwiYXR0ZW1wdFJlZnJlc2giLCJyZWZyZXNoUmVzcG9uc2UiLCJnZXQiLCJyZWZyZXNoRXJyb3IiLCJpc0F4aW9zRXJyb3IiLCJpczQwMUVycm9yIiwic2hvdWxkUmV0cnkiLCJkZWxheSIsInNldFRpbWVvdXQiLCJ3aW5kb3ciLCJkaXNwYXRjaEV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJhZGRyZXNzU2VydmljZSIsImdldEFkZHJlc3NlcyIsIlVTRVJfQUREUkVTU0VTIiwiZGF0YSIsIkFycmF5IiwiaXNBcnJheSIsIk9iamVjdCIsImtleXMiLCJ2YWx1ZXMiLCJhZGRyZXNzZXMiLCJyZXN1bHQiLCJhZGRyZXNzRGF0YSIsInN1Y2Nlc3MiLCJjcmVhdGVBZGRyZXNzIiwicG9zdCIsIkNSRUFURV9BRERSRVNTIiwiZGVsZXRlQWRkcmVzcyIsImFkZHJlc3NJZCIsInVzZXJJZCIsIkRFTEVURV9BRERSRVNTIiwic2V0RGVmYXVsdEFkZHJlc3MiLCJTRVRfREVGQVVMVF9BRERSRVNTIiwidXNlclNlcnZpY2UiLCJ1cGRhdGVQcm9maWxlIiwicHJvZmlsZURhdGEiLCJVUERBVEVfUFJPRklMRSIsImdldFVzZXJzIiwicGFyYW1zIiwicmVxdWVzdEJvZHkiLCJwYWdlIiwicGFnZVNpemUiLCJzZWFyY2giLCJyb2xlSWQiLCJpc0FjdGl2ZSIsInVuZGVmaW5lZCIsIkdFVF9VU0VSUyIsImdldFVzZXJSb2xlQ291bnRzIiwiR0VUX1VTRVJfUk9MRV9DT1VOVFMiLCJnZXREaXNjb3VudFJhdGUiLCJHRVRfRElTQ09VTlRfUkFURSIsInVwZGF0ZUNhcnRUeXBlIiwiVVBEQVRFX0NBUlRfVFlQRSIsInByb2R1Y3RTZXJ2aWNlIiwiZ2V0QnJhbmRzIiwiR0VUX0JSQU5EUyIsImdldENhdGVnb3JpZXNCeUJyYW5kIiwiYnJhbmRJZCIsIkdFVF9DQVRFR09SSUVTX0JZX0JSQU5EIiwiZ2V0U3ViQ2F0ZWdvcmllcyIsImNhdGVnb3J5SWQiLCJHRVRfU1VCQ0FURUdPUklFUyIsImdldFN1YkNhdGVnb3J5RmVhdHVyZXMiLCJzdWJDYXRlZ29yeUlkIiwiR0VUX1NVQkNBVEVHT1JZX0ZFQVRVUkVTIiwiZ2V0RmVhdHVyZVZhbHVlcyIsImRlZmluaXRpb25JZCIsIkdFVF9GRUFUVVJFX1ZBTFVFUyIsImdldEFsbEZlYXR1cmVEZWZpbml0aW9ucyIsIkdFVF9BTExfRkVBVFVSRV9ERUZJTklUSU9OUyIsImNyZWF0ZUZ1bGxQcm9kdWN0IiwicHJvZHVjdERhdGEiLCJDUkVBVEVfRlVMTF9QUk9EVUNUIiwiY3JlYXRlRGVhbGVyc2hpcFByb2R1Y3QiLCJDUkVBVEVfREVBTEVSU0hJUF9QUk9EVUNUIiwiYWRkUHJvZHVjdEltYWdlIiwiaW1hZ2VEYXRhIiwiQUREX1BST0RVQ1RfSU1BR0UiLCJkZWxldGVQcm9kdWN0SW1hZ2UiLCJpbWFnZUlkIiwiZGVsZXRlIiwiREVMRVRFX1BST0RVQ1RfSU1BR0UiLCJyZXBsYWNlUHJvZHVjdEltYWdlIiwibmV3SW1hZ2VGaWxlIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCIsInB1dCIsIlJFUExBQ0VfUFJPRFVDVF9JTUFHRSIsImdldEFkbWluUHJvZHVjdHMiLCJHRVRfQURNSU5fUFJPRFVDVFMiLCJnZXRNeVByb2R1Y3RzIiwiR0VUX01ZX1BST0RVQ1RTIiwiZ2V0RGVhbGVyc2hpcFByb2R1Y3REZXRhaWwiLCJwcm9kdWN0SWQiLCJHRVRfREVBTEVSU0hJUF9QUk9EVUNUX0RFVEFJTCIsImRlbGV0ZVByb2R1Y3QiLCJERUxFVEVfUFJPRFVDVCIsInVwZGF0ZUZ1bGxQcm9kdWN0Iiwia2V5IiwiZW50cmllcyIsIlVQREFURV9GVUxMX1BST0RVQ1QiLCJlcnJvck1lc3NhZ2UiLCJ0aXRsZSIsIkVycm9yIiwiZ2V0QWRtaW5Qcm9kdWN0U3RhdGlzdGljcyIsIkdFVF9BRE1JTl9QUk9EVUNUX1NUQVRJU1RJQ1MiLCJnZXRNeVByb2R1Y3RTdGF0aXN0aWNzIiwiR0VUX01ZX1BST0RVQ1RfU1RBVElTVElDUyIsInVwZGF0ZVNpbXBsZVByb2R1Y3QiLCJwcm9kdWN0Rm9ybURhdGEiLCJVUERBVEVfU0lNUExFX1BST0RVQ1QiLCJGaWxlIiwic2l6ZSIsInR5cGUiLCJnZXRQcm9kdWN0RGV0YWlsIiwiR0VUX1BST0RVQ1RfREVUQUlMIiwiZ2V0Q2F0YWxvZ1Byb2R1Y3REZXRhaWwiLCJHRVRfQ0FUQUxPR19QUk9EVUNUX0RFVEFJTCIsInVwZGF0ZVByb2R1Y3RTdGF0dXMiLCJpc0FwcHJvdmVkIiwiVVBEQVRFX1BST0RVQ1RfU1RBVFVTIiwiZ2V0UHJvZHVjdE1lc3NhZ2UiLCJHRVRfUFJPRFVDVF9NRVNTQUdFIiwiZ2V0Q2F0ZWdvcmllcyIsIkdFVF9DQVRFR09SSUVTIiwiZ2V0U3ViQ2F0ZWdvcmllc0J5Q2F0ZWdvcnkiLCJHRVRfU1VCQ0FURUdPUklFU19CWV9DQVRFR09SWSIsInJlcGxhY2UiLCJ0b1N0cmluZyIsImZpbHRlclByb2R1Y3RzIiwiZmlsdGVyUmVxdWVzdCIsIkZJTFRFUl9QUk9EVUNUUyIsImdldFJlZmVyZW5jZURhdGEiLCJHRVRfUkVGRVJFTkNFX0RBVEEiLCJjYXJ0U2VydmljZSIsImFkZFRvQ2FydCIsInByb2R1Y3RWYXJpYW50SWQiLCJxdWFudGl0eSIsImlzQ3VzdG9tZXJQcmljZSIsIkFERF9UT19DQVJUIiwiZ2V0Q2FydEl0ZW1zIiwiR0VUX0NBUlRfSVRFTVMiLCJyZW1vdmVGcm9tQ2FydCIsIlJFTU9WRV9GUk9NX0NBUlQiLCJ1cGRhdGVDYXJ0UXVhbnRpdHkiLCJVUERBVEVfQ0FSVF9RVUFOVElUWSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});