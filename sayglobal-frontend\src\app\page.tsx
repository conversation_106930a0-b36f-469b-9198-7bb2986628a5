'use client';

import Image from "next/image";
import { categories } from "@/data/mocks/categories";
import { products } from "@/data/mocks/products";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import { useCart } from "@/contexts/CartContext";
import AddToCartModal from "@/components/AddToCartModal";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [currentDiscountSlide, setCurrentDiscountSlide] = useState(0);
  const [showAddToCartModal, setShowAddToCartModal] = useState(false);
  const [lastAddedProduct, setLastAddedProduct] = useState(null);
  const { addToCart } = useCart();

  // Ana sayfada gösterilecek popüler ürünleri filtreleyelim
  const popularProducts = products.filter(product => product.rating >= 4.5).slice(0, 4);

  // İndirimli ürünleri filtreleyelim
  const discountedProducts = products.filter(product => product.discountPercentage && product.discountPercentage > 0);

  // Hero carousel için resimler
  const heroSlides = [
    {
      id: 1,
      image: "https://picsum.photos/id/94/800/800",
      title: "Sağlıklı Bir",
      subtitle: "Yaşam İçin",
      description: "Say Global ile sağlıklı yaşamın kapılarını aralayın. Doğal içerikli ürünlerle kendinize yatırım yapın.",
      discount: "15"
    },
    {
      id: 2,
      image: "https://picsum.photos/id/96/800/800",
      title: "Doğal ve",
      subtitle: "Organik Ürünler",
      description: "Tamamen doğal içeriklerle üretilmiş, laboratuvar testlerinden geçmiş ürünlerimizi keşfedin.",
      discount: "20"
    },
    {
      id: 3,
      image: "https://picsum.photos/id/102/800/800",
      title: "Kaliteli",
      subtitle: "Yaşam Tarzı",
      description: "Sağlığınızı ön planda tutan, GMP sertifikalı tesislerde üretilen ürünlerle tanışın.",
      discount: "10"
    }
  ];

  // Component mount edildiğinde loading state'ini kaldır
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Hero carousel otomatik kayma
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [heroSlides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  const nextDiscountSlide = () => {
    setCurrentDiscountSlide((prev) => (prev + 1) % Math.ceil(discountedProducts.length / 3));
  };

  const prevDiscountSlide = () => {
    setCurrentDiscountSlide((prev) => (prev - 1 + Math.ceil(discountedProducts.length / 3)) % Math.ceil(discountedProducts.length / 3));
  };

  // Animasyon varyantları
  const fadeInUp = {
    initial: { y: 30, opacity: 0 },
    animate: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  const scaleOnHover = {
    initial: { scale: 1 },
    hover: {
      scale: 1.05,
      transition: { duration: 0.3, ease: "easeInOut" }
    }
  };

  // Loading durumu için fallback
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          {/* Hero skeleton */}
          <div className="bg-gray-300 rounded-2xl h-96 mb-16"></div>

          {/* Categories skeleton */}
          <div className="mb-20">
            <div className="bg-gray-300 h-8 w-64 mx-auto mb-12 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-gray-300 rounded-xl h-80"></div>
              ))}
            </div>
          </div>

          {/* Products skeleton */}
          <div className="mb-20">
            <div className="bg-gray-300 h-8 w-64 mx-auto mb-12 rounded"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="bg-gray-300 rounded-xl h-96"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        className="container mx-auto px-4 py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        key="home-page"
      >
        {/* Hero Carousel Bölümü */}
        <motion.section
          className="relative bg-gradient-to-r from-indigo-600 to-purple-700 rounded-2xl overflow-hidden mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="absolute inset-0 bg-pattern opacity-10"></div>
          <div className="relative min-h-[500px] md:h-[500px]">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                className="absolute inset-0 flex flex-col md:flex-row items-center justify-center md:justify-between py-8 md:py-20 px-6 md:px-16"
                initial={{ opacity: 0, x: 300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -300 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <motion.div
                  className="text-white w-full md:w-1/2 mb-8 md:mb-0 z-10 text-center md:text-left order-1 md:order-1"
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.8, ease: "easeOut" }}
                >
                  <h1 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6">
                    <span className="block">{heroSlides[currentSlide].title}</span>
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-200 to-indigo-200">
                      {heroSlides[currentSlide].subtitle}
                    </span>
                  </h1>
                  <p className="text-sm sm:text-base md:text-lg lg:text-xl mb-6 md:mb-8 text-indigo-100 max-w-md mx-auto md:mx-0 leading-relaxed">
                    {heroSlides[currentSlide].description}
                  </p>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href="/products"
                      className="bg-white text-indigo-700 px-6 md:px-8 py-3 md:py-4 rounded-lg font-medium hover:shadow-lg hover:bg-gray-50 transition duration-300 inline-flex items-center space-x-2 text-sm md:text-base"
                    >
                      <span>Ürünleri Keşfet</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </Link>
                  </motion.div>
                </motion.div>
                <motion.div
                  className="w-full md:w-1/2 flex justify-center order-2 md:order-2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
                >
                  <div className="relative w-56 h-56 sm:w-72 sm:h-72 md:w-80 md:h-80 lg:w-96 lg:h-96">
                    <Image
                      src={heroSlides[currentSlide].image}
                      alt="Say Global Ürünleri"
                      fill
                      className="object-cover rounded-2xl shadow-xl"
                    />
                    <motion.div
                      className="absolute -bottom-3 -right-3 md:-bottom-6 md:-right-6 bg-pink-500 rounded-lg p-2 md:p-4 shadow-lg"
                      animate={{ y: [0, -10, 0] }}
                      transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                    >
                      <span className="text-white font-bold text-xs sm:text-sm md:text-lg">%{heroSlides[currentSlide].discount} İndirim</span>
                    </motion.div>
                  </div>
                </motion.div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Arrows */}
            <button
              onClick={prevSlide}
              className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 z-20 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-2 transition-all duration-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 md:h-6 md:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 z-20 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-2 transition-all duration-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 md:h-6 md:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Dots Indicator */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-20 flex space-x-2">
              {heroSlides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-2 h-2 md:w-3 md:h-3 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-white' : 'bg-white/50'
                    }`}
                />
              ))}
            </div>
          </div>
        </motion.section>

        {/* İndirimli Ürünler Slider */}
        <motion.section
          className="mb-20"
          initial="initial"
          whileInView="animate"
          viewport={{ once: false, amount: 0.1 }}
          variants={fadeInUp}
        >
          <div className="text-center mb-12">
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-4 inline-block relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: false }}
            >
              İndirimli Ürünler
              <motion.span
                className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-pink-500"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ delay: 0.3, duration: 0.8 }}
                viewport={{ once: false }}
                style={{ bottom: '-8px' }}
              ></motion.span>
            </motion.h2>
            <motion.p
              className="text-gray-600 max-w-xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              viewport={{ once: false }}
            >
              Özel indirimli ürünlerimizi kaçırmayın! Sınırlı süre için geçerli.
            </motion.p>
          </div>

          <div className="relative px-12">
            <div className="overflow-hidden">
              <motion.div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentDiscountSlide * 100}%)` }}
              >
                {Array.from({ length: Math.ceil(discountedProducts.length / 3) }).map((_, slideIndex) => (
                  <div key={slideIndex} className="w-full flex-shrink-0">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 px-2">
                      {discountedProducts.slice(slideIndex * 3, (slideIndex + 1) * 3).map((product) => (
                        <motion.div
                          key={product.id}
                          variants={fadeInUp}
                          whileHover="hover"
                          className="group"
                        >
                          <motion.div
                            className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full flex flex-col"
                            variants={scaleOnHover}
                          >
                            <Link href={`/product/${product.id}`}>
                              <div className="relative h-64 overflow-hidden">
                                <Image
                                  src={product.thumbnail}
                                  alt={product.title}
                                  fill
                                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                                />
                                <motion.div
                                  className="absolute top-3 right-3 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg"
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ delay: 0.2, type: "spring" }}
                                >
                                  %{product.discountPercentage} İndirim
                                </motion.div>
                                <motion.div
                                  className="absolute top-3 left-3 bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center space-x-1"
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ delay: 0.3, type: "spring" }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                  </svg>
                                  <span>{product.points} Puan</span>
                                </motion.div>
                              </div>
                            </Link>
                            <div className="p-5 flex-1 flex flex-col">
                              <Link href={`/product/${product.id}`}>
                                <h3 className="text-lg font-semibold mb-1 text-gray-800 hover:text-purple-600 transition-colors">
                                  {product.title}
                                </h3>
                              </Link>
                              <p className="text-gray-600 mb-2 text-sm">{product.brand}</p>
                              <div className="mt-auto space-y-3">
                                <div className="flex justify-between items-center">
                                  <div>
                                    <span className="text-lg font-bold text-purple-700">
                                      {product.price.toFixed(2)} ₺
                                    </span>
                                    <span className="text-sm text-gray-500 line-through ml-2">
                                      {(product.price / (1 - product.discountPercentage / 100)).toFixed(2)} ₺
                                    </span>
                                  </div>
                                  <div className="flex items-center bg-yellow-50 px-2 py-1 rounded">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-5 w-5 text-yellow-400"
                                      viewBox="0 0 20 20"
                                      fill="currentColor"
                                    >
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <span className="ml-1 text-sm font-medium text-yellow-700">{product.rating}</span>
                                  </div>
                                </div>
                                <motion.button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    addToCart(product);
                                    setLastAddedProduct({
                                      id: product.id,
                                      title: product.title,
                                      price: product.price,
                                      thumbnail: product.thumbnail,
                                      brand: product.brand,
                                      discountPercentage: product.discountPercentage,
                                      points: product.points || 0,
                                      quantity: 1
                                    });
                                    setShowAddToCartModal(true);
                                  }}
                                  className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
                                  whileHover={{ scale: 1.02 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-5 w-5"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                                    />
                                  </svg>
                                  <span>Sepete Ekle</span>
                                </motion.button>
                              </div>
                            </div>
                          </motion.div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                ))}
              </motion.div>
            </div>

            {/* Navigation Arrows for Discount Slider - İyileştirilmiş pozisyon */}
            {Math.ceil(discountedProducts.length / 3) > 1 && (
              <>
                <button
                  onClick={prevDiscountSlide}
                  className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-300 group border border-gray-200"
                  style={{ transform: 'translateY(-50%) translateX(-50%)' }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600 group-hover:text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={nextDiscountSlide}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-300 group border border-gray-200"
                  style={{ transform: 'translateY(-50%) translateX(50%)' }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600 group-hover:text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}
          </div>
        </motion.section>

        {/* Kategoriler Bölümü */}
        <motion.section
          className="mb-20"
          initial="initial"
          whileInView="animate"
          viewport={{ once: false, amount: 0.2 }}
          variants={fadeInUp}
        >
          <div className="text-center mb-12">
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-4 inline-block relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: false }}
            >
              Kategorilerimiz
              <motion.span
                className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-indigo-500 mt-2"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ delay: 0.3, duration: 0.8 }}
                viewport={{ once: false }}
                style={{ bottom: '-8px' }}
              ></motion.span>
            </motion.h2>
            <motion.p
              className="text-gray-600 max-w-xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              viewport={{ once: false }}
            >
              İhtiyacınıza uygun ürünleri kategorilerimiz arasından kolayca bulabilirsiniz.
            </motion.p>
          </div>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: false, amount: 0.1 }}
          >
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                variants={fadeInUp}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Link href={`/products?category=${category.slug}`}>
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full flex flex-col">
                    <div className="relative h-56 overflow-hidden">
                      <Image
                        src={category.image}
                        alt={category.name}
                        fill
                        className="object-cover transition-transform duration-700 hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                      <h3 className="absolute bottom-4 left-4 text-2xl font-bold text-white">{category.name}</h3>
                    </div>
                    <div className="p-5">
                      <p className="text-gray-600">{category.description}</p>
                      <div className="mt-4 flex justify-end">
                        <span className="text-purple-600 font-medium inline-flex items-center group">
                          Keşfet
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 ml-1 transform transition-transform group-hover:translate-x-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </motion.section>

        {/* Popüler Ürünler Bölümü */}
        <motion.section
          className="mb-20"
          initial="initial"
          whileInView="animate"
          viewport={{ once: false, amount: 0.1 }}
          variants={fadeInUp}
        >
          <div className="text-center mb-12">
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-4 inline-block relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: false }}
            >
              Popüler Ürünler
              <motion.span
                className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-indigo-500"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ delay: 0.3, duration: 0.8 }}
                viewport={{ once: false }}
                style={{ bottom: '-8px' }}
              ></motion.span>
            </motion.h2>
            <motion.p
              className="text-gray-600 max-w-xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              viewport={{ once: false }}
            >
              En çok tercih edilen ve en yüksek puanlı ürünlerimize göz atın.
            </motion.p>
          </div>
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: false, amount: 0.1 }}
          >
            {popularProducts.map((product) => (
              <motion.div
                key={product.id}
                variants={fadeInUp}
                whileHover="hover"
                initial="initial"
                animate="animate"
              >
                <motion.div
                  className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full flex flex-col"
                  variants={scaleOnHover}
                >
                  <Link href={`/product/${product.id}`}>
                    <div className="relative h-64 overflow-hidden">
                      <Image
                        src={product.thumbnail}
                        alt={product.title}
                        fill
                        className="object-cover transition-transform duration-700 hover:scale-110"
                      />
                      {product.discountPercentage > 0 && (
                        <motion.div
                          className="absolute top-3 right-3 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.2, type: "spring" }}
                        >
                          %{product.discountPercentage} İndirim
                        </motion.div>
                      )}
                      <motion.div
                        className="absolute top-3 left-3 bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center space-x-1"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.3, type: "spring" }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                        </svg>
                        <span>{product.points} Puan</span>
                      </motion.div>
                    </div>
                  </Link>
                  <div className="p-5 flex-1 flex flex-col">
                    <Link href={`/product/${product.id}`}>
                      <h3 className="text-lg font-semibold mb-1 text-gray-800 hover:text-purple-600 transition-colors">
                        {product.title}
                      </h3>
                    </Link>
                    <p className="text-gray-600 mb-2 text-sm">{product.brand}</p>
                    <div className="mt-auto space-y-3">
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="text-lg font-bold text-purple-700">
                            {product.price.toFixed(2)} ₺
                          </span>
                          {product.discountPercentage > 0 && (
                            <span className="text-sm text-gray-500 line-through ml-2">
                              {(product.price / (1 - product.discountPercentage / 100)).toFixed(2)} ₺
                            </span>
                          )}
                        </div>
                        <div className="flex items-center bg-yellow-50 px-2 py-1 rounded">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-yellow-400"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span className="ml-1 text-sm font-medium text-yellow-700">{product.rating}</span>
                        </div>
                      </div>
                      <motion.button
                        onClick={(e) => {
                          e.preventDefault();
                          addToCart(product);
                          setLastAddedProduct({
                            id: product.id,
                            title: product.title,
                            price: product.price,
                            thumbnail: product.thumbnail,
                            brand: product.brand,
                            discountPercentage: product.discountPercentage,
                            points: product.points || 0,
                            quantity: 1
                          });
                          setShowAddToCartModal(true);
                        }}
                        className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                          />
                        </svg>
                        <span>Sepete Ekle</span>
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
          <div className="text-center mt-10">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              viewport={{ once: false }}
            >
              <Link
                href="/products"
                className="inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-lg font-medium hover:shadow-lg transition duration-300 group"
              >
                <span className="flex items-center">
                  Tüm Ürünleri Gör
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 ml-2 transform transition-transform group-hover:translate-x-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </span>
              </Link>
            </motion.div>
          </div>
        </motion.section>

        {/* Kampanya Bölümü */}
        <motion.section
          className="mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, amount: 0.3 }}
        >
          <div className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 rounded-2xl p-10 md:p-16 text-white text-center relative overflow-hidden shadow-xl">
            <motion.div
              className="absolute top-0 right-0 w-72 h-72 bg-white opacity-10 rounded-full -mr-20 -mt-20"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 5, ease: "easeInOut" }}
            />
            <motion.div
              className="absolute bottom-0 left-0 w-40 h-40 bg-white opacity-10 rounded-full -ml-10 -mb-10"
              animate={{ scale: [1, 1.3, 1] }}
              transition={{ repeat: Infinity, duration: 6, ease: "easeInOut", delay: 0.5 }}
            />
            <motion.h2
              className="text-3xl md:text-5xl font-bold mb-6 relative z-10"
              initial={{ y: -30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              viewport={{ once: true }}
            >
              Özel Kampanya
            </motion.h2>
            <motion.p
              className="text-xl md:text-2xl mb-6 max-w-xl mx-auto"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              viewport={{ once: true }}
            >
              İlk siparişinizde %15 indirim fırsatını kaçırmayın!
            </motion.p>
            <motion.p
              className="text-lg mb-10 text-indigo-100 max-w-2xl mx-auto"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              viewport={{ once: true }}
            >
              Sınırlı süre için geçerli bu kampanyadan yararlanmak için hemen hesap oluşturun.
            </motion.p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Link
                href="/register"
                className="bg-white text-pink-600 px-8 py-4 rounded-full font-medium hover:shadow-2xl transition duration-300 inline-flex items-center space-x-2"
              >
                <span>Hemen Üye Ol</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </motion.div>
          </div>
        </motion.section>

        {/* Neden Bizi Tercih Etmelisiniz? */}
        <motion.section
          className="mb-20"
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true, amount: 0.1 }}
        >
          <div className="text-center mb-12">
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-4 inline-block relative"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              Neden Bizi Tercih Etmelisiniz?
              <motion.span
                className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-indigo-500"
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ delay: 0.5, duration: 0.8 }}
                style={{ bottom: '-8px' }}
              ></motion.span>
            </motion.h2>
            <p className="text-gray-600 max-w-xl mx-auto">
              Say Global ile sağlıklı yaşamın kapılarını aralayın
            </p>
          </div>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.1 }}
          >
            {[
              {
                title: "Doğal İçerikler",
                description: "Tüm ürünlerimiz doğal içeriklerle hazırlanır ve zararlı kimyasallar içermez.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                )
              },
              {
                title: "Kalite Garantisi",
                description: "Ürünlerimiz en yüksek kalite standartlarında üretilir ve test edilir.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 text-blue-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                )
              },
              {
                title: "Hızlı Teslimat",
                description: "Siparişleriniz en hızlı şekilde hazırlanır ve kargoya verilir.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 text-purple-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                )
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 text-center"
                whileHover={{ y: -10 }}
              >
                {feature.icon}
                <h3 className="text-xl font-bold mb-3 text-gray-800">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.section>

        {/* Add To Cart Modal */}
        <AddToCartModal
          isOpen={showAddToCartModal}
          onClose={() => setShowAddToCartModal(false)}
          product={lastAddedProduct}
        />
      </motion.div>
    </AnimatePresence>
  );
}
