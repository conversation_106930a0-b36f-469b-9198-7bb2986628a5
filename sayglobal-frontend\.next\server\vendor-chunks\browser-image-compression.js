"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/browser-image-compression";
exports.ids = ["vendor-chunks/browser-image-compression"];
exports.modules = {

/***/ "(ssr)/./node_modules/browser-image-compression/dist/browser-image-compression.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/browser-image-compression/dist/browser-image-compression.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ imageCompression)\n/* harmony export */ });\n/**\n * Browser Image Compression\n * v2.0.2\n * by Donald <<EMAIL>>\n * https://github.com/Donaldcwl/browser-image-compression\n */\n\nfunction _mergeNamespaces(e,t){return t.forEach((function(t){t&&\"string\"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if(\"default\"!==r&&!(r in e)){var i=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,i.get?i:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}function copyExifWithoutOrientation(e,t){return new Promise((function(r,i){let o;return getApp1Segment(e).then((function(e){try{return o=e,r(new Blob([t.slice(0,2),o,t.slice(2)],{type:\"image/jpeg\"}))}catch(e){return i(e)}}),i)}))}const getApp1Segment=e=>new Promise(((t,r)=>{const i=new FileReader;i.addEventListener(\"load\",(({target:{result:e}})=>{const i=new DataView(e);let o=0;if(65496!==i.getUint16(o))return r(\"not a valid JPEG\");for(o+=2;;){const a=i.getUint16(o);if(65498===a)break;const s=i.getUint16(o+2);if(65505===a&&1165519206===i.getUint32(o+4)){const a=o+10;let f;switch(i.getUint16(a)){case 18761:f=!0;break;case 19789:f=!1;break;default:return r(\"TIFF header contains invalid endian\")}if(42!==i.getUint16(a+2,f))return r(\"TIFF header contains invalid version\");const l=i.getUint32(a+4,f),c=a+l+2+12*i.getUint16(a+l,f);for(let e=a+l+2;e<c;e+=12){if(274==i.getUint16(e,f)){if(3!==i.getUint16(e+2,f))return r(\"Orientation data type is invalid\");if(1!==i.getUint32(e+4,f))return r(\"Orientation data count is invalid\");i.setUint16(e+8,1,f);break}}return t(e.slice(o,o+2+s))}o+=2+s}return t(new Blob)})),i.readAsArrayBuffer(e)}));var e={},t={get exports(){return e},set exports(t){e=t}};!function(e){var r,i,UZIP={};t.exports=UZIP,UZIP.parse=function(e,t){for(var r=UZIP.bin.readUshort,i=UZIP.bin.readUint,o=0,a={},s=new Uint8Array(e),f=s.length-4;101010256!=i(s,f);)f--;o=f;o+=4;var l=r(s,o+=4);r(s,o+=2);var c=i(s,o+=2),u=i(s,o+=4);o+=4,o=u;for(var h=0;h<l;h++){i(s,o),o+=4,o+=4,o+=4,i(s,o+=4);c=i(s,o+=4);var d=i(s,o+=4),A=r(s,o+=4),g=r(s,o+2),p=r(s,o+4);o+=6;var m=i(s,o+=8);o+=4,o+=A+g+p,UZIP._readLocal(s,m,a,c,d,t)}return a},UZIP._readLocal=function(e,t,r,i,o,a){var s=UZIP.bin.readUshort,f=UZIP.bin.readUint;f(e,t),s(e,t+=4),s(e,t+=2);var l=s(e,t+=2);f(e,t+=2),f(e,t+=4),t+=4;var c=s(e,t+=8),u=s(e,t+=2);t+=2;var h=UZIP.bin.readUTF8(e,t,c);if(t+=c,t+=u,a)r[h]={size:o,csize:i};else{var d=new Uint8Array(e.buffer,t);if(0==l)r[h]=new Uint8Array(d.buffer.slice(t,t+i));else{if(8!=l)throw\"unknown compression method: \"+l;var A=new Uint8Array(o);UZIP.inflateRaw(d,A),r[h]=A}}},UZIP.inflateRaw=function(e,t){return UZIP.F.inflate(e,t)},UZIP.inflate=function(e,t){return e[0],e[1],UZIP.inflateRaw(new Uint8Array(e.buffer,e.byteOffset+2,e.length-6),t)},UZIP.deflate=function(e,t){null==t&&(t={level:6});var r=0,i=new Uint8Array(50+Math.floor(1.1*e.length));i[r]=120,i[r+1]=156,r+=2,r=UZIP.F.deflateRaw(e,i,r,t.level);var o=UZIP.adler(e,0,e.length);return i[r+0]=o>>>24&255,i[r+1]=o>>>16&255,i[r+2]=o>>>8&255,i[r+3]=o>>>0&255,new Uint8Array(i.buffer,0,r+4)},UZIP.deflateRaw=function(e,t){null==t&&(t={level:6});var r=new Uint8Array(50+Math.floor(1.1*e.length)),i=UZIP.F.deflateRaw(e,r,i,t.level);return new Uint8Array(r.buffer,0,i)},UZIP.encode=function(e,t){null==t&&(t=!1);var r=0,i=UZIP.bin.writeUint,o=UZIP.bin.writeUshort,a={};for(var s in e){var f=!UZIP._noNeed(s)&&!t,l=e[s],c=UZIP.crc.crc(l,0,l.length);a[s]={cpr:f,usize:l.length,crc:c,file:f?UZIP.deflateRaw(l):l}}for(var s in a)r+=a[s].file.length+30+46+2*UZIP.bin.sizeUTF8(s);r+=22;var u=new Uint8Array(r),h=0,d=[];for(var s in a){var A=a[s];d.push(h),h=UZIP._writeHeader(u,h,s,A,0)}var g=0,p=h;for(var s in a){A=a[s];d.push(h),h=UZIP._writeHeader(u,h,s,A,1,d[g++])}var m=h-p;return i(u,h,101010256),h+=4,o(u,h+=4,g),o(u,h+=2,g),i(u,h+=2,m),i(u,h+=4,p),h+=4,h+=2,u.buffer},UZIP._noNeed=function(e){var t=e.split(\".\").pop().toLowerCase();return-1!=\"png,jpg,jpeg,zip\".indexOf(t)},UZIP._writeHeader=function(e,t,r,i,o,a){var s=UZIP.bin.writeUint,f=UZIP.bin.writeUshort,l=i.file;return s(e,t,0==o?67324752:33639248),t+=4,1==o&&(t+=2),f(e,t,20),f(e,t+=2,0),f(e,t+=2,i.cpr?8:0),s(e,t+=2,0),s(e,t+=4,i.crc),s(e,t+=4,l.length),s(e,t+=4,i.usize),f(e,t+=4,UZIP.bin.sizeUTF8(r)),f(e,t+=2,0),t+=2,1==o&&(t+=2,t+=2,s(e,t+=6,a),t+=4),t+=UZIP.bin.writeUTF8(e,t,r),0==o&&(e.set(l,t),t+=l.length),t},UZIP.crc={table:function(){for(var e=new Uint32Array(256),t=0;t<256;t++){for(var r=t,i=0;i<8;i++)1&r?r=3988292384^r>>>1:r>>>=1;e[t]=r}return e}(),update:function(e,t,r,i){for(var o=0;o<i;o++)e=UZIP.crc.table[255&(e^t[r+o])]^e>>>8;return e},crc:function(e,t,r){return 4294967295^UZIP.crc.update(4294967295,e,t,r)}},UZIP.adler=function(e,t,r){for(var i=1,o=0,a=t,s=t+r;a<s;){for(var f=Math.min(a+5552,s);a<f;)o+=i+=e[a++];i%=65521,o%=65521}return o<<16|i},UZIP.bin={readUshort:function(e,t){return e[t]|e[t+1]<<8},writeUshort:function(e,t,r){e[t]=255&r,e[t+1]=r>>8&255},readUint:function(e,t){return 16777216*e[t+3]+(e[t+2]<<16|e[t+1]<<8|e[t])},writeUint:function(e,t,r){e[t]=255&r,e[t+1]=r>>8&255,e[t+2]=r>>16&255,e[t+3]=r>>24&255},readASCII:function(e,t,r){for(var i=\"\",o=0;o<r;o++)i+=String.fromCharCode(e[t+o]);return i},writeASCII:function(e,t,r){for(var i=0;i<r.length;i++)e[t+i]=r.charCodeAt(i)},pad:function(e){return e.length<2?\"0\"+e:e},readUTF8:function(e,t,r){for(var i,o=\"\",a=0;a<r;a++)o+=\"%\"+UZIP.bin.pad(e[t+a].toString(16));try{i=decodeURIComponent(o)}catch(i){return UZIP.bin.readASCII(e,t,r)}return i},writeUTF8:function(e,t,r){for(var i=r.length,o=0,a=0;a<i;a++){var s=r.charCodeAt(a);if(0==(4294967168&s))e[t+o]=s,o++;else if(0==(4294965248&s))e[t+o]=192|s>>6,e[t+o+1]=128|s>>0&63,o+=2;else if(0==(4294901760&s))e[t+o]=224|s>>12,e[t+o+1]=128|s>>6&63,e[t+o+2]=128|s>>0&63,o+=3;else{if(0!=(4292870144&s))throw\"e\";e[t+o]=240|s>>18,e[t+o+1]=128|s>>12&63,e[t+o+2]=128|s>>6&63,e[t+o+3]=128|s>>0&63,o+=4}}return o},sizeUTF8:function(e){for(var t=e.length,r=0,i=0;i<t;i++){var o=e.charCodeAt(i);if(0==(4294967168&o))r++;else if(0==(4294965248&o))r+=2;else if(0==(4294901760&o))r+=3;else{if(0!=(4292870144&o))throw\"e\";r+=4}}return r}},UZIP.F={},UZIP.F.deflateRaw=function(e,t,r,i){var o=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][i],a=UZIP.F.U,s=UZIP.F._goodIndex;UZIP.F._hash;var f=UZIP.F._putsE,l=0,c=r<<3,u=0,h=e.length;if(0==i){for(;l<h;){f(t,c,l+(_=Math.min(65535,h-l))==h?1:0),c=UZIP.F._copyExact(e,l,_,t,c+8),l+=_}return c>>>3}var d=a.lits,A=a.strt,g=a.prev,p=0,m=0,w=0,v=0,b=0,y=0;for(h>2&&(A[y=UZIP.F._hash(e,0)]=0),l=0;l<h;l++){if(b=y,l+1<h-2){y=UZIP.F._hash(e,l+1);var E=l+1&32767;g[E]=A[y],A[y]=E}if(u<=l){(p>14e3||m>26697)&&h-l>100&&(u<l&&(d[p]=l-u,p+=2,u=l),c=UZIP.F._writeBlock(l==h-1||u==h?1:0,d,p,v,e,w,l-w,t,c),p=m=v=0,w=l);var F=0;l<h-2&&(F=UZIP.F._bestMatch(e,l,g,b,Math.min(o[2],h-l),o[3]));var _=F>>>16,B=65535&F;if(0!=F){B=65535&F;var U=s(_=F>>>16,a.of0);a.lhst[257+U]++;var C=s(B,a.df0);a.dhst[C]++,v+=a.exb[U]+a.dxb[C],d[p]=_<<23|l-u,d[p+1]=B<<16|U<<8|C,p+=2,u=l+_}else a.lhst[e[l]]++;m++}}for(w==l&&0!=e.length||(u<l&&(d[p]=l-u,p+=2,u=l),c=UZIP.F._writeBlock(1,d,p,v,e,w,l-w,t,c),p=0,m=0,p=m=v=0,w=l);0!=(7&c);)c++;return c>>>3},UZIP.F._bestMatch=function(e,t,r,i,o,a){var s=32767&t,f=r[s],l=s-f+32768&32767;if(f==s||i!=UZIP.F._hash(e,t-l))return 0;for(var c=0,u=0,h=Math.min(32767,t);l<=h&&0!=--a&&f!=s;){if(0==c||e[t+c]==e[t+c-l]){var d=UZIP.F._howLong(e,t,l);if(d>c){if(u=l,(c=d)>=o)break;l+2<d&&(d=l+2);for(var A=0,g=0;g<d-2;g++){var p=t-l+g+32768&32767,m=p-r[p]+32768&32767;m>A&&(A=m,f=p)}}}l+=(s=f)-(f=r[s])+32768&32767}return c<<16|u},UZIP.F._howLong=function(e,t,r){if(e[t]!=e[t-r]||e[t+1]!=e[t+1-r]||e[t+2]!=e[t+2-r])return 0;var i=t,o=Math.min(e.length,t+258);for(t+=3;t<o&&e[t]==e[t-r];)t++;return t-i},UZIP.F._hash=function(e,t){return(e[t]<<8|e[t+1])+(e[t+2]<<4)&65535},UZIP.saved=0,UZIP.F._writeBlock=function(e,t,r,i,o,a,s,f,l){var c,u,h,d,A,g,p,m,w,v=UZIP.F.U,b=UZIP.F._putsF,y=UZIP.F._putsE;v.lhst[256]++,u=(c=UZIP.F.getTrees())[0],h=c[1],d=c[2],A=c[3],g=c[4],p=c[5],m=c[6],w=c[7];var E=32+(0==(l+3&7)?0:8-(l+3&7))+(s<<3),F=i+UZIP.F.contSize(v.fltree,v.lhst)+UZIP.F.contSize(v.fdtree,v.dhst),_=i+UZIP.F.contSize(v.ltree,v.lhst)+UZIP.F.contSize(v.dtree,v.dhst);_+=14+3*p+UZIP.F.contSize(v.itree,v.ihst)+(2*v.ihst[16]+3*v.ihst[17]+7*v.ihst[18]);for(var B=0;B<286;B++)v.lhst[B]=0;for(B=0;B<30;B++)v.dhst[B]=0;for(B=0;B<19;B++)v.ihst[B]=0;var U=E<F&&E<_?0:F<_?1:2;if(b(f,l,e),b(f,l+1,U),l+=3,0==U){for(;0!=(7&l);)l++;l=UZIP.F._copyExact(o,a,s,f,l)}else{var C,I;if(1==U&&(C=v.fltree,I=v.fdtree),2==U){UZIP.F.makeCodes(v.ltree,u),UZIP.F.revCodes(v.ltree,u),UZIP.F.makeCodes(v.dtree,h),UZIP.F.revCodes(v.dtree,h),UZIP.F.makeCodes(v.itree,d),UZIP.F.revCodes(v.itree,d),C=v.ltree,I=v.dtree,y(f,l,A-257),y(f,l+=5,g-1),y(f,l+=5,p-4),l+=4;for(var Q=0;Q<p;Q++)y(f,l+3*Q,v.itree[1+(v.ordr[Q]<<1)]);l+=3*p,l=UZIP.F._codeTiny(m,v.itree,f,l),l=UZIP.F._codeTiny(w,v.itree,f,l)}for(var M=a,x=0;x<r;x+=2){for(var S=t[x],R=S>>>23,T=M+(8388607&S);M<T;)l=UZIP.F._writeLit(o[M++],C,f,l);if(0!=R){var O=t[x+1],P=O>>16,H=O>>8&255,L=255&O;y(f,l=UZIP.F._writeLit(257+H,C,f,l),R-v.of0[H]),l+=v.exb[H],b(f,l=UZIP.F._writeLit(L,I,f,l),P-v.df0[L]),l+=v.dxb[L],M+=R}}l=UZIP.F._writeLit(256,C,f,l)}return l},UZIP.F._copyExact=function(e,t,r,i,o){var a=o>>>3;return i[a]=r,i[a+1]=r>>>8,i[a+2]=255-i[a],i[a+3]=255-i[a+1],a+=4,i.set(new Uint8Array(e.buffer,t,r),a),o+(r+4<<3)},UZIP.F.getTrees=function(){for(var e=UZIP.F.U,t=UZIP.F._hufTree(e.lhst,e.ltree,15),r=UZIP.F._hufTree(e.dhst,e.dtree,15),i=[],o=UZIP.F._lenCodes(e.ltree,i),a=[],s=UZIP.F._lenCodes(e.dtree,a),f=0;f<i.length;f+=2)e.ihst[i[f]]++;for(f=0;f<a.length;f+=2)e.ihst[a[f]]++;for(var l=UZIP.F._hufTree(e.ihst,e.itree,7),c=19;c>4&&0==e.itree[1+(e.ordr[c-1]<<1)];)c--;return[t,r,l,o,s,c,i,a]},UZIP.F.getSecond=function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(e[r+1]);return t},UZIP.F.nonZero=function(e){for(var t=\"\",r=0;r<e.length;r+=2)0!=e[r+1]&&(t+=(r>>1)+\",\");return t},UZIP.F.contSize=function(e,t){for(var r=0,i=0;i<t.length;i++)r+=t[i]*e[1+(i<<1)];return r},UZIP.F._codeTiny=function(e,t,r,i){for(var o=0;o<e.length;o+=2){var a=e[o],s=e[o+1];i=UZIP.F._writeLit(a,t,r,i);var f=16==a?2:17==a?3:7;a>15&&(UZIP.F._putsE(r,i,s,f),i+=f)}return i},UZIP.F._lenCodes=function(e,t){for(var r=e.length;2!=r&&0==e[r-1];)r-=2;for(var i=0;i<r;i+=2){var o=e[i+1],a=i+3<r?e[i+3]:-1,s=i+5<r?e[i+5]:-1,f=0==i?-1:e[i-1];if(0==o&&a==o&&s==o){for(var l=i+5;l+2<r&&e[l+2]==o;)l+=2;(c=Math.min(l+1-i>>>1,138))<11?t.push(17,c-3):t.push(18,c-11),i+=2*c-2}else if(o==f&&a==o&&s==o){for(l=i+5;l+2<r&&e[l+2]==o;)l+=2;var c=Math.min(l+1-i>>>1,6);t.push(16,c-3),i+=2*c-2}else t.push(o,0)}return r>>>1},UZIP.F._hufTree=function(e,t,r){var i=[],o=e.length,a=t.length,s=0;for(s=0;s<a;s+=2)t[s]=0,t[s+1]=0;for(s=0;s<o;s++)0!=e[s]&&i.push({lit:s,f:e[s]});var f=i.length,l=i.slice(0);if(0==f)return 0;if(1==f){var c=i[0].lit;l=0==c?1:0;return t[1+(c<<1)]=1,t[1+(l<<1)]=1,1}i.sort((function(e,t){return e.f-t.f}));var u=i[0],h=i[1],d=0,A=1,g=2;for(i[0]={lit:-1,f:u.f+h.f,l:u,r:h,d:0};A!=f-1;)u=d!=A&&(g==f||i[d].f<i[g].f)?i[d++]:i[g++],h=d!=A&&(g==f||i[d].f<i[g].f)?i[d++]:i[g++],i[A++]={lit:-1,f:u.f+h.f,l:u,r:h};var p=UZIP.F.setDepth(i[A-1],0);for(p>r&&(UZIP.F.restrictDepth(l,r,p),p=r),s=0;s<f;s++)t[1+(l[s].lit<<1)]=l[s].d;return p},UZIP.F.setDepth=function(e,t){return-1!=e.lit?(e.d=t,t):Math.max(UZIP.F.setDepth(e.l,t+1),UZIP.F.setDepth(e.r,t+1))},UZIP.F.restrictDepth=function(e,t,r){var i=0,o=1<<r-t,a=0;for(e.sort((function(e,t){return t.d==e.d?e.f-t.f:t.d-e.d})),i=0;i<e.length&&e[i].d>t;i++){var s=e[i].d;e[i].d=t,a+=o-(1<<r-s)}for(a>>>=r-t;a>0;){(s=e[i].d)<t?(e[i].d++,a-=1<<t-s-1):i++}for(;i>=0;i--)e[i].d==t&&a<0&&(e[i].d--,a++);0!=a&&console.log(\"debt left\")},UZIP.F._goodIndex=function(e,t){var r=0;return t[16|r]<=e&&(r|=16),t[8|r]<=e&&(r|=8),t[4|r]<=e&&(r|=4),t[2|r]<=e&&(r|=2),t[1|r]<=e&&(r|=1),r},UZIP.F._writeLit=function(e,t,r,i){return UZIP.F._putsF(r,i,t[e<<1]),i+t[1+(e<<1)]},UZIP.F.inflate=function(e,t){var r=Uint8Array;if(3==e[0]&&0==e[1])return t||new r(0);var i=UZIP.F,o=i._bitsF,a=i._bitsE,s=i._decodeTiny,f=i.makeCodes,l=i.codes2map,c=i._get17,u=i.U,h=null==t;h&&(t=new r(e.length>>>2<<3));for(var d,A,g=0,p=0,m=0,w=0,v=0,b=0,y=0,E=0,F=0;0==g;)if(g=o(e,F,1),p=o(e,F+1,2),F+=3,0!=p){if(h&&(t=UZIP.F._check(t,E+(1<<17))),1==p&&(d=u.flmap,A=u.fdmap,b=511,y=31),2==p){m=a(e,F,5)+257,w=a(e,F+5,5)+1,v=a(e,F+10,4)+4,F+=14;for(var _=0;_<38;_+=2)u.itree[_]=0,u.itree[_+1]=0;var B=1;for(_=0;_<v;_++){var U=a(e,F+3*_,3);u.itree[1+(u.ordr[_]<<1)]=U,U>B&&(B=U)}F+=3*v,f(u.itree,B),l(u.itree,B,u.imap),d=u.lmap,A=u.dmap,F=s(u.imap,(1<<B)-1,m+w,e,F,u.ttree);var C=i._copyOut(u.ttree,0,m,u.ltree);b=(1<<C)-1;var I=i._copyOut(u.ttree,m,w,u.dtree);y=(1<<I)-1,f(u.ltree,C),l(u.ltree,C,d),f(u.dtree,I),l(u.dtree,I,A)}for(;;){var Q=d[c(e,F)&b];F+=15&Q;var M=Q>>>4;if(M>>>8==0)t[E++]=M;else{if(256==M)break;var x=E+M-254;if(M>264){var S=u.ldef[M-257];x=E+(S>>>3)+a(e,F,7&S),F+=7&S}var R=A[c(e,F)&y];F+=15&R;var T=R>>>4,O=u.ddef[T],P=(O>>>4)+o(e,F,15&O);for(F+=15&O,h&&(t=UZIP.F._check(t,E+(1<<17)));E<x;)t[E]=t[E++-P],t[E]=t[E++-P],t[E]=t[E++-P],t[E]=t[E++-P];E=x}}}else{0!=(7&F)&&(F+=8-(7&F));var H=4+(F>>>3),L=e[H-4]|e[H-3]<<8;h&&(t=UZIP.F._check(t,E+L)),t.set(new r(e.buffer,e.byteOffset+H,L),E),F=H+L<<3,E+=L}return t.length==E?t:t.slice(0,E)},UZIP.F._check=function(e,t){var r=e.length;if(t<=r)return e;var i=new Uint8Array(Math.max(r<<1,t));return i.set(e,0),i},UZIP.F._decodeTiny=function(e,t,r,i,o,a){for(var s=UZIP.F._bitsE,f=UZIP.F._get17,l=0;l<r;){var c=e[f(i,o)&t];o+=15&c;var u=c>>>4;if(u<=15)a[l]=u,l++;else{var h=0,d=0;16==u?(d=3+s(i,o,2),o+=2,h=a[l-1]):17==u?(d=3+s(i,o,3),o+=3):18==u&&(d=11+s(i,o,7),o+=7);for(var A=l+d;l<A;)a[l]=h,l++}}return o},UZIP.F._copyOut=function(e,t,r,i){for(var o=0,a=0,s=i.length>>>1;a<r;){var f=e[a+t];i[a<<1]=0,i[1+(a<<1)]=f,f>o&&(o=f),a++}for(;a<s;)i[a<<1]=0,i[1+(a<<1)]=0,a++;return o},UZIP.F.makeCodes=function(e,t){for(var r,i,o,a,s=UZIP.F.U,f=e.length,l=s.bl_count,c=0;c<=t;c++)l[c]=0;for(c=1;c<f;c+=2)l[e[c]]++;var u=s.next_code;for(r=0,l[0]=0,i=1;i<=t;i++)r=r+l[i-1]<<1,u[i]=r;for(o=0;o<f;o+=2)0!=(a=e[o+1])&&(e[o]=u[a],u[a]++)},UZIP.F.codes2map=function(e,t,r){for(var i=e.length,o=UZIP.F.U.rev15,a=0;a<i;a+=2)if(0!=e[a+1])for(var s=a>>1,f=e[a+1],l=s<<4|f,c=t-f,u=e[a]<<c,h=u+(1<<c);u!=h;){r[o[u]>>>15-t]=l,u++}},UZIP.F.revCodes=function(e,t){for(var r=UZIP.F.U.rev15,i=15-t,o=0;o<e.length;o+=2){var a=e[o]<<t-e[o+1];e[o]=r[a]>>>i}},UZIP.F._putsE=function(e,t,r){r<<=7&t;var i=t>>>3;e[i]|=r,e[i+1]|=r>>>8},UZIP.F._putsF=function(e,t,r){r<<=7&t;var i=t>>>3;e[i]|=r,e[i+1]|=r>>>8,e[i+2]|=r>>>16},UZIP.F._bitsE=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8)>>>(7&t)&(1<<r)-1},UZIP.F._bitsF=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)&(1<<r)-1},UZIP.F._get17=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)},UZIP.F._get25=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16|e[3+(t>>>3)]<<24)>>>(7&t)},UZIP.F.U=(r=Uint16Array,i=Uint32Array,{next_code:new r(16),bl_count:new r(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new r(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new i(32),flmap:new r(512),fltree:[],fdmap:new r(32),fdtree:[],lmap:new r(32768),ltree:[],ttree:[],dmap:new r(32768),dtree:[],imap:new r(512),itree:[],rev15:new r(32768),lhst:new i(286),dhst:new i(30),ihst:new i(19),lits:new i(15e3),strt:new r(65536),prev:new r(32768)}),function(){for(var e=UZIP.F.U,t=0;t<32768;t++){var r=t;r=(4278255360&(r=(4042322160&(r=(3435973836&(r=(2863311530&r)>>>1|(1431655765&r)<<1))>>>2|(858993459&r)<<2))>>>4|(252645135&r)<<4))>>>8|(16711935&r)<<8,e.rev15[t]=(r>>>16|r<<16)>>>17}function pushV(e,t,r){for(;0!=t--;)e.push(0,r)}for(t=0;t<32;t++)e.ldef[t]=e.of0[t]<<3|e.exb[t],e.ddef[t]=e.df0[t]<<4|e.dxb[t];pushV(e.fltree,144,8),pushV(e.fltree,112,9),pushV(e.fltree,24,7),pushV(e.fltree,8,8),UZIP.F.makeCodes(e.fltree,9),UZIP.F.codes2map(e.fltree,9,e.flmap),UZIP.F.revCodes(e.fltree,9),pushV(e.fdtree,32,5),UZIP.F.makeCodes(e.fdtree,5),UZIP.F.codes2map(e.fdtree,5,e.fdmap),UZIP.F.revCodes(e.fdtree,5),pushV(e.itree,19,0),pushV(e.ltree,286,0),pushV(e.dtree,30,0),pushV(e.ttree,320,0)}()}();var UZIP=_mergeNamespaces({__proto__:null,default:e},[e]);const UPNG=function(){var e={nextZero(e,t){for(;0!=e[t];)t++;return t},readUshort:(e,t)=>e[t]<<8|e[t+1],writeUshort(e,t,r){e[t]=r>>8&255,e[t+1]=255&r},readUint:(e,t)=>16777216*e[t]+(e[t+1]<<16|e[t+2]<<8|e[t+3]),writeUint(e,t,r){e[t]=r>>24&255,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=255&r},readASCII(e,t,r){let i=\"\";for(let o=0;o<r;o++)i+=String.fromCharCode(e[t+o]);return i},writeASCII(e,t,r){for(let i=0;i<r.length;i++)e[t+i]=r.charCodeAt(i)},readBytes(e,t,r){const i=[];for(let o=0;o<r;o++)i.push(e[t+o]);return i},pad:e=>e.length<2?`0${e}`:e,readUTF8(t,r,i){let o,a=\"\";for(let o=0;o<i;o++)a+=`%${e.pad(t[r+o].toString(16))}`;try{o=decodeURIComponent(a)}catch(o){return e.readASCII(t,r,i)}return o}};function decodeImage(t,r,i,o){const a=r*i,s=_getBPP(o),f=Math.ceil(r*s/8),l=new Uint8Array(4*a),c=new Uint32Array(l.buffer),{ctype:u}=o,{depth:h}=o,d=e.readUshort;if(6==u){const e=a<<2;if(8==h)for(var A=0;A<e;A+=4)l[A]=t[A],l[A+1]=t[A+1],l[A+2]=t[A+2],l[A+3]=t[A+3];if(16==h)for(A=0;A<e;A++)l[A]=t[A<<1]}else if(2==u){const e=o.tabs.tRNS;if(null==e){if(8==h)for(A=0;A<a;A++){var g=3*A;c[A]=255<<24|t[g+2]<<16|t[g+1]<<8|t[g]}if(16==h)for(A=0;A<a;A++){g=6*A;c[A]=255<<24|t[g+4]<<16|t[g+2]<<8|t[g]}}else{var p=e[0];const r=e[1],i=e[2];if(8==h)for(A=0;A<a;A++){var m=A<<2;g=3*A;c[A]=255<<24|t[g+2]<<16|t[g+1]<<8|t[g],t[g]==p&&t[g+1]==r&&t[g+2]==i&&(l[m+3]=0)}if(16==h)for(A=0;A<a;A++){m=A<<2,g=6*A;c[A]=255<<24|t[g+4]<<16|t[g+2]<<8|t[g],d(t,g)==p&&d(t,g+2)==r&&d(t,g+4)==i&&(l[m+3]=0)}}}else if(3==u){const e=o.tabs.PLTE,s=o.tabs.tRNS,c=s?s.length:0;if(1==h)for(var w=0;w<i;w++){var v=w*f,b=w*r;for(A=0;A<r;A++){m=b+A<<2;var y=3*(E=t[v+(A>>3)]>>7-((7&A)<<0)&1);l[m]=e[y],l[m+1]=e[y+1],l[m+2]=e[y+2],l[m+3]=E<c?s[E]:255}}if(2==h)for(w=0;w<i;w++)for(v=w*f,b=w*r,A=0;A<r;A++){m=b+A<<2,y=3*(E=t[v+(A>>2)]>>6-((3&A)<<1)&3);l[m]=e[y],l[m+1]=e[y+1],l[m+2]=e[y+2],l[m+3]=E<c?s[E]:255}if(4==h)for(w=0;w<i;w++)for(v=w*f,b=w*r,A=0;A<r;A++){m=b+A<<2,y=3*(E=t[v+(A>>1)]>>4-((1&A)<<2)&15);l[m]=e[y],l[m+1]=e[y+1],l[m+2]=e[y+2],l[m+3]=E<c?s[E]:255}if(8==h)for(A=0;A<a;A++){var E;m=A<<2,y=3*(E=t[A]);l[m]=e[y],l[m+1]=e[y+1],l[m+2]=e[y+2],l[m+3]=E<c?s[E]:255}}else if(4==u){if(8==h)for(A=0;A<a;A++){m=A<<2;var F=t[_=A<<1];l[m]=F,l[m+1]=F,l[m+2]=F,l[m+3]=t[_+1]}if(16==h)for(A=0;A<a;A++){var _;m=A<<2,F=t[_=A<<2];l[m]=F,l[m+1]=F,l[m+2]=F,l[m+3]=t[_+2]}}else if(0==u)for(p=o.tabs.tRNS?o.tabs.tRNS:-1,w=0;w<i;w++){const e=w*f,i=w*r;if(1==h)for(var B=0;B<r;B++){var U=(F=255*(t[e+(B>>>3)]>>>7-(7&B)&1))==255*p?0:255;c[i+B]=U<<24|F<<16|F<<8|F}else if(2==h)for(B=0;B<r;B++){U=(F=85*(t[e+(B>>>2)]>>>6-((3&B)<<1)&3))==85*p?0:255;c[i+B]=U<<24|F<<16|F<<8|F}else if(4==h)for(B=0;B<r;B++){U=(F=17*(t[e+(B>>>1)]>>>4-((1&B)<<2)&15))==17*p?0:255;c[i+B]=U<<24|F<<16|F<<8|F}else if(8==h)for(B=0;B<r;B++){U=(F=t[e+B])==p?0:255;c[i+B]=U<<24|F<<16|F<<8|F}else if(16==h)for(B=0;B<r;B++){F=t[e+(B<<1)],U=d(t,e+(B<<1))==p?0:255;c[i+B]=U<<24|F<<16|F<<8|F}}return l}function _decompress(e,r,i,o){const a=_getBPP(e),s=Math.ceil(i*a/8),f=new Uint8Array((s+1+e.interlace)*o);return r=e.tabs.CgBI?t(r,f):_inflate(r,f),0==e.interlace?r=_filterZero(r,e,0,i,o):1==e.interlace&&(r=function _readInterlace(e,t){const r=t.width,i=t.height,o=_getBPP(t),a=o>>3,s=Math.ceil(r*o/8),f=new Uint8Array(i*s);let l=0;const c=[0,0,4,0,2,0,1],u=[0,4,0,2,0,1,0],h=[8,8,8,4,4,2,2],d=[8,8,4,4,2,2,1];let A=0;for(;A<7;){const p=h[A],m=d[A];let w=0,v=0,b=c[A];for(;b<i;)b+=p,v++;let y=u[A];for(;y<r;)y+=m,w++;const E=Math.ceil(w*o/8);_filterZero(e,t,l,w,v);let F=0,_=c[A];for(;_<i;){let t=u[A],i=l+F*E<<3;for(;t<r;){var g;if(1==o)g=(g=e[i>>3])>>7-(7&i)&1,f[_*s+(t>>3)]|=g<<7-((7&t)<<0);if(2==o)g=(g=e[i>>3])>>6-(7&i)&3,f[_*s+(t>>2)]|=g<<6-((3&t)<<1);if(4==o)g=(g=e[i>>3])>>4-(7&i)&15,f[_*s+(t>>1)]|=g<<4-((1&t)<<2);if(o>=8){const r=_*s+t*a;for(let t=0;t<a;t++)f[r+t]=e[(i>>3)+t]}i+=o,t+=m}F++,_+=p}w*v!=0&&(l+=v*(1+E)),A+=1}return f}(r,e)),r}function _inflate(e,r){return t(new Uint8Array(e.buffer,2,e.length-6),r)}var t=function(){const e={H:{}};return e.H.N=function(t,r){const i=Uint8Array;let o,a,s=0,f=0,l=0,c=0,u=0,h=0,d=0,A=0,g=0;if(3==t[0]&&0==t[1])return r||new i(0);const p=e.H,m=p.b,w=p.e,v=p.R,b=p.n,y=p.A,E=p.Z,F=p.m,_=null==r;for(_&&(r=new i(t.length>>>2<<5));0==s;)if(s=m(t,g,1),f=m(t,g+1,2),g+=3,0!=f){if(_&&(r=e.H.W(r,A+(1<<17))),1==f&&(o=F.J,a=F.h,h=511,d=31),2==f){l=w(t,g,5)+257,c=w(t,g+5,5)+1,u=w(t,g+10,4)+4,g+=14;let e=1;for(var B=0;B<38;B+=2)F.Q[B]=0,F.Q[B+1]=0;for(B=0;B<u;B++){const r=w(t,g+3*B,3);F.Q[1+(F.X[B]<<1)]=r,r>e&&(e=r)}g+=3*u,b(F.Q,e),y(F.Q,e,F.u),o=F.w,a=F.d,g=v(F.u,(1<<e)-1,l+c,t,g,F.v);const r=p.V(F.v,0,l,F.C);h=(1<<r)-1;const i=p.V(F.v,l,c,F.D);d=(1<<i)-1,b(F.C,r),y(F.C,r,o),b(F.D,i),y(F.D,i,a)}for(;;){const e=o[E(t,g)&h];g+=15&e;const i=e>>>4;if(i>>>8==0)r[A++]=i;else{if(256==i)break;{let e=A+i-254;if(i>264){const r=F.q[i-257];e=A+(r>>>3)+w(t,g,7&r),g+=7&r}const o=a[E(t,g)&d];g+=15&o;const s=o>>>4,f=F.c[s],l=(f>>>4)+m(t,g,15&f);for(g+=15&f;A<e;)r[A]=r[A++-l],r[A]=r[A++-l],r[A]=r[A++-l],r[A]=r[A++-l];A=e}}}}else{0!=(7&g)&&(g+=8-(7&g));const o=4+(g>>>3),a=t[o-4]|t[o-3]<<8;_&&(r=e.H.W(r,A+a)),r.set(new i(t.buffer,t.byteOffset+o,a),A),g=o+a<<3,A+=a}return r.length==A?r:r.slice(0,A)},e.H.W=function(e,t){const r=e.length;if(t<=r)return e;const i=new Uint8Array(r<<1);return i.set(e,0),i},e.H.R=function(t,r,i,o,a,s){const f=e.H.e,l=e.H.Z;let c=0;for(;c<i;){const e=t[l(o,a)&r];a+=15&e;const i=e>>>4;if(i<=15)s[c]=i,c++;else{let e=0,t=0;16==i?(t=3+f(o,a,2),a+=2,e=s[c-1]):17==i?(t=3+f(o,a,3),a+=3):18==i&&(t=11+f(o,a,7),a+=7);const r=c+t;for(;c<r;)s[c]=e,c++}}return a},e.H.V=function(e,t,r,i){let o=0,a=0;const s=i.length>>>1;for(;a<r;){const r=e[a+t];i[a<<1]=0,i[1+(a<<1)]=r,r>o&&(o=r),a++}for(;a<s;)i[a<<1]=0,i[1+(a<<1)]=0,a++;return o},e.H.n=function(t,r){const i=e.H.m,o=t.length;let a,s,f;let l;const c=i.j;for(var u=0;u<=r;u++)c[u]=0;for(u=1;u<o;u+=2)c[t[u]]++;const h=i.K;for(a=0,c[0]=0,s=1;s<=r;s++)a=a+c[s-1]<<1,h[s]=a;for(f=0;f<o;f+=2)l=t[f+1],0!=l&&(t[f]=h[l],h[l]++)},e.H.A=function(t,r,i){const o=t.length,a=e.H.m.r;for(let e=0;e<o;e+=2)if(0!=t[e+1]){const o=e>>1,s=t[e+1],f=o<<4|s,l=r-s;let c=t[e]<<l;const u=c+(1<<l);for(;c!=u;){i[a[c]>>>15-r]=f,c++}}},e.H.l=function(t,r){const i=e.H.m.r,o=15-r;for(let e=0;e<t.length;e+=2){const a=t[e]<<r-t[e+1];t[e]=i[a]>>>o}},e.H.M=function(e,t,r){r<<=7&t;const i=t>>>3;e[i]|=r,e[i+1]|=r>>>8},e.H.I=function(e,t,r){r<<=7&t;const i=t>>>3;e[i]|=r,e[i+1]|=r>>>8,e[i+2]|=r>>>16},e.H.e=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8)>>>(7&t)&(1<<r)-1},e.H.b=function(e,t,r){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)&(1<<r)-1},e.H.Z=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)},e.H.i=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16|e[3+(t>>>3)]<<24)>>>(7&t)},e.H.m=function(){const e=Uint16Array,t=Uint32Array;return{K:new e(16),j:new e(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new e(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new t(32),J:new e(512),_:[],h:new e(32),$:[],w:new e(32768),C:[],v:[],d:new e(32768),D:[],u:new e(512),Q:[],r:new e(32768),s:new t(286),Y:new t(30),a:new t(19),t:new t(15e3),k:new e(65536),g:new e(32768)}}(),function(){const t=e.H.m;for(var r=0;r<32768;r++){let e=r;e=(2863311530&e)>>>1|(1431655765&e)<<1,e=(3435973836&e)>>>2|(858993459&e)<<2,e=(4042322160&e)>>>4|(252645135&e)<<4,e=(4278255360&e)>>>8|(16711935&e)<<8,t.r[r]=(e>>>16|e<<16)>>>17}function n(e,t,r){for(;0!=t--;)e.push(0,r)}for(r=0;r<32;r++)t.q[r]=t.S[r]<<3|t.T[r],t.c[r]=t.p[r]<<4|t.z[r];n(t._,144,8),n(t._,112,9),n(t._,24,7),n(t._,8,8),e.H.n(t._,9),e.H.A(t._,9,t.J),e.H.l(t._,9),n(t.$,32,5),e.H.n(t.$,5),e.H.A(t.$,5,t.h),e.H.l(t.$,5),n(t.Q,19,0),n(t.C,286,0),n(t.D,30,0),n(t.v,320,0)}(),e.H.N}();function _getBPP(e){return[1,null,3,1,2,null,4][e.ctype]*e.depth}function _filterZero(e,t,r,i,o){let a=_getBPP(t);const s=Math.ceil(i*a/8);let f,l;a=Math.ceil(a/8);let c=e[r],u=0;if(c>1&&(e[r]=[0,0,1][c-2]),3==c)for(u=a;u<s;u++)e[u+1]=e[u+1]+(e[u+1-a]>>>1)&255;for(let t=0;t<o;t++)if(f=r+t*s,l=f+t+1,c=e[l-1],u=0,0==c)for(;u<s;u++)e[f+u]=e[l+u];else if(1==c){for(;u<a;u++)e[f+u]=e[l+u];for(;u<s;u++)e[f+u]=e[l+u]+e[f+u-a]}else if(2==c)for(;u<s;u++)e[f+u]=e[l+u]+e[f+u-s];else if(3==c){for(;u<a;u++)e[f+u]=e[l+u]+(e[f+u-s]>>>1);for(;u<s;u++)e[f+u]=e[l+u]+(e[f+u-s]+e[f+u-a]>>>1)}else{for(;u<a;u++)e[f+u]=e[l+u]+_paeth(0,e[f+u-s],0);for(;u<s;u++)e[f+u]=e[l+u]+_paeth(e[f+u-a],e[f+u-s],e[f+u-a-s])}return e}function _paeth(e,t,r){const i=e+t-r,o=i-e,a=i-t,s=i-r;return o*o<=a*a&&o*o<=s*s?e:a*a<=s*s?t:r}function _IHDR(t,r,i){i.width=e.readUint(t,r),r+=4,i.height=e.readUint(t,r),r+=4,i.depth=t[r],r++,i.ctype=t[r],r++,i.compress=t[r],r++,i.filter=t[r],r++,i.interlace=t[r],r++}function _copyTile(e,t,r,i,o,a,s,f,l){const c=Math.min(t,o),u=Math.min(r,a);let h=0,d=0;for(let r=0;r<u;r++)for(let a=0;a<c;a++)if(s>=0&&f>=0?(h=r*t+a<<2,d=(f+r)*o+s+a<<2):(h=(-f+r)*t-s+a<<2,d=r*o+a<<2),0==l)i[d]=e[h],i[d+1]=e[h+1],i[d+2]=e[h+2],i[d+3]=e[h+3];else if(1==l){var A=e[h+3]*(1/255),g=e[h]*A,p=e[h+1]*A,m=e[h+2]*A,w=i[d+3]*(1/255),v=i[d]*w,b=i[d+1]*w,y=i[d+2]*w;const t=1-A,r=A+w*t,o=0==r?0:1/r;i[d+3]=255*r,i[d+0]=(g+v*t)*o,i[d+1]=(p+b*t)*o,i[d+2]=(m+y*t)*o}else if(2==l){A=e[h+3],g=e[h],p=e[h+1],m=e[h+2],w=i[d+3],v=i[d],b=i[d+1],y=i[d+2];A==w&&g==v&&p==b&&m==y?(i[d]=0,i[d+1]=0,i[d+2]=0,i[d+3]=0):(i[d]=g,i[d+1]=p,i[d+2]=m,i[d+3]=A)}else if(3==l){A=e[h+3],g=e[h],p=e[h+1],m=e[h+2],w=i[d+3],v=i[d],b=i[d+1],y=i[d+2];if(A==w&&g==v&&p==b&&m==y)continue;if(A<220&&w>20)return!1}return!0}return{decode:function decode(r){const i=new Uint8Array(r);let o=8;const a=e,s=a.readUshort,f=a.readUint,l={tabs:{},frames:[]},c=new Uint8Array(i.length);let u,h=0,d=0;const A=[137,80,78,71,13,10,26,10];for(var g=0;g<8;g++)if(i[g]!=A[g])throw\"The input is not a PNG file!\";for(;o<i.length;){const e=a.readUint(i,o);o+=4;const r=a.readASCII(i,o,4);if(o+=4,\"IHDR\"==r)_IHDR(i,o,l);else if(\"iCCP\"==r){for(var p=o;0!=i[p];)p++;a.readASCII(i,o,p-o),i[p+1];const s=i.slice(p+2,o+e);let f=null;try{f=_inflate(s)}catch(e){f=t(s)}l.tabs[r]=f}else if(\"CgBI\"==r)l.tabs[r]=i.slice(o,o+4);else if(\"IDAT\"==r){for(g=0;g<e;g++)c[h+g]=i[o+g];h+=e}else if(\"acTL\"==r)l.tabs[r]={num_frames:f(i,o),num_plays:f(i,o+4)},u=new Uint8Array(i.length);else if(\"fcTL\"==r){if(0!=d)(E=l.frames[l.frames.length-1]).data=_decompress(l,u.slice(0,d),E.rect.width,E.rect.height),d=0;const e={x:f(i,o+12),y:f(i,o+16),width:f(i,o+4),height:f(i,o+8)};let t=s(i,o+22);t=s(i,o+20)/(0==t?100:t);const r={rect:e,delay:Math.round(1e3*t),dispose:i[o+24],blend:i[o+25]};l.frames.push(r)}else if(\"fdAT\"==r){for(g=0;g<e-4;g++)u[d+g]=i[o+g+4];d+=e-4}else if(\"pHYs\"==r)l.tabs[r]=[a.readUint(i,o),a.readUint(i,o+4),i[o+8]];else if(\"cHRM\"==r){l.tabs[r]=[];for(g=0;g<8;g++)l.tabs[r].push(a.readUint(i,o+4*g))}else if(\"tEXt\"==r||\"zTXt\"==r){null==l.tabs[r]&&(l.tabs[r]={});var m=a.nextZero(i,o),w=a.readASCII(i,o,m-o),v=o+e-m-1;if(\"tEXt\"==r)y=a.readASCII(i,m+1,v);else{var b=_inflate(i.slice(m+2,m+2+v));y=a.readUTF8(b,0,b.length)}l.tabs[r][w]=y}else if(\"iTXt\"==r){null==l.tabs[r]&&(l.tabs[r]={});m=0,p=o;m=a.nextZero(i,p);w=a.readASCII(i,p,m-p);const t=i[p=m+1];var y;i[p+1],p+=2,m=a.nextZero(i,p),a.readASCII(i,p,m-p),p=m+1,m=a.nextZero(i,p),a.readUTF8(i,p,m-p);v=e-((p=m+1)-o);if(0==t)y=a.readUTF8(i,p,v);else{b=_inflate(i.slice(p,p+v));y=a.readUTF8(b,0,b.length)}l.tabs[r][w]=y}else if(\"PLTE\"==r)l.tabs[r]=a.readBytes(i,o,e);else if(\"hIST\"==r){const e=l.tabs.PLTE.length/3;l.tabs[r]=[];for(g=0;g<e;g++)l.tabs[r].push(s(i,o+2*g))}else if(\"tRNS\"==r)3==l.ctype?l.tabs[r]=a.readBytes(i,o,e):0==l.ctype?l.tabs[r]=s(i,o):2==l.ctype&&(l.tabs[r]=[s(i,o),s(i,o+2),s(i,o+4)]);else if(\"gAMA\"==r)l.tabs[r]=a.readUint(i,o)/1e5;else if(\"sRGB\"==r)l.tabs[r]=i[o];else if(\"bKGD\"==r)0==l.ctype||4==l.ctype?l.tabs[r]=[s(i,o)]:2==l.ctype||6==l.ctype?l.tabs[r]=[s(i,o),s(i,o+2),s(i,o+4)]:3==l.ctype&&(l.tabs[r]=i[o]);else if(\"IEND\"==r)break;o+=e,a.readUint(i,o),o+=4}var E;return 0!=d&&((E=l.frames[l.frames.length-1]).data=_decompress(l,u.slice(0,d),E.rect.width,E.rect.height)),l.data=_decompress(l,c,l.width,l.height),delete l.compress,delete l.interlace,delete l.filter,l},toRGBA8:function toRGBA8(e){const t=e.width,r=e.height;if(null==e.tabs.acTL)return[decodeImage(e.data,t,r,e).buffer];const i=[];null==e.frames[0].data&&(e.frames[0].data=e.data);const o=t*r*4,a=new Uint8Array(o),s=new Uint8Array(o),f=new Uint8Array(o);for(let c=0;c<e.frames.length;c++){const u=e.frames[c],h=u.rect.x,d=u.rect.y,A=u.rect.width,g=u.rect.height,p=decodeImage(u.data,A,g,e);if(0!=c)for(var l=0;l<o;l++)f[l]=a[l];if(0==u.blend?_copyTile(p,A,g,a,t,r,h,d,0):1==u.blend&&_copyTile(p,A,g,a,t,r,h,d,1),i.push(a.buffer.slice(0)),0==u.dispose);else if(1==u.dispose)_copyTile(s,A,g,a,t,r,h,d,0);else if(2==u.dispose)for(l=0;l<o;l++)a[l]=f[l]}return i},_paeth:_paeth,_copyTile:_copyTile,_bin:e}}();!function(){const{_copyTile:e}=UPNG,{_bin:t}=UPNG,r=UPNG._paeth;var i={table:function(){const e=new Uint32Array(256);for(let t=0;t<256;t++){let r=t;for(let e=0;e<8;e++)1&r?r=3988292384^r>>>1:r>>>=1;e[t]=r}return e}(),update(e,t,r,o){for(let a=0;a<o;a++)e=i.table[255&(e^t[r+a])]^e>>>8;return e},crc:(e,t,r)=>4294967295^i.update(4294967295,e,t,r)};function addErr(e,t,r,i){t[r]+=e[0]*i>>4,t[r+1]+=e[1]*i>>4,t[r+2]+=e[2]*i>>4,t[r+3]+=e[3]*i>>4}function N(e){return Math.max(0,Math.min(255,e))}function D(e,t){const r=e[0]-t[0],i=e[1]-t[1],o=e[2]-t[2],a=e[3]-t[3];return r*r+i*i+o*o+a*a}function dither(e,t,r,i,o,a,s){null==s&&(s=1);const f=i.length,l=[];for(var c=0;c<f;c++){const e=i[c];l.push([e>>>0&255,e>>>8&255,e>>>16&255,e>>>24&255])}for(c=0;c<f;c++){let e=4294967295;for(var u=0,h=0;h<f;h++){var d=D(l[c],l[h]);h!=c&&d<e&&(e=d,u=h)}}const A=new Uint32Array(o.buffer),g=new Int16Array(t*r*4),p=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(c=0;c<p.length;c++)p[c]=255*((p[c]+.5)/16-.5);for(let o=0;o<r;o++)for(let w=0;w<t;w++){var m;c=4*(o*t+w);if(2!=s)m=[N(e[c]+g[c]),N(e[c+1]+g[c+1]),N(e[c+2]+g[c+2]),N(e[c+3]+g[c+3])];else{d=p[4*(3&o)+(3&w)];m=[N(e[c]+d),N(e[c+1]+d),N(e[c+2]+d),N(e[c+3]+d)]}u=0;let v=16777215;for(h=0;h<f;h++){const e=D(m,l[h]);e<v&&(v=e,u=h)}const b=l[u],y=[m[0]-b[0],m[1]-b[1],m[2]-b[2],m[3]-b[3]];1==s&&(w!=t-1&&addErr(y,g,c+4,7),o!=r-1&&(0!=w&&addErr(y,g,c+4*t-4,3),addErr(y,g,c+4*t,5),w!=t-1&&addErr(y,g,c+4*t+4,1))),a[c>>2]=u,A[c>>2]=i[u]}}function _main(e,r,o,a,s){null==s&&(s={});const{crc:f}=i,l=t.writeUint,c=t.writeUshort,u=t.writeASCII;let h=8;const d=e.frames.length>1;let A,g=!1,p=33+(d?20:0);if(null!=s.sRGB&&(p+=13),null!=s.pHYs&&(p+=21),null!=s.iCCP&&(A=pako.deflate(s.iCCP),p+=21+A.length+4),3==e.ctype){for(var m=e.plte.length,w=0;w<m;w++)e.plte[w]>>>24!=255&&(g=!0);p+=8+3*m+4+(g?8+1*m+4:0)}for(var v=0;v<e.frames.length;v++){d&&(p+=38),p+=(F=e.frames[v]).cimg.length+12,0!=v&&(p+=4)}p+=12;const b=new Uint8Array(p),y=[137,80,78,71,13,10,26,10];for(w=0;w<8;w++)b[w]=y[w];if(l(b,h,13),h+=4,u(b,h,\"IHDR\"),h+=4,l(b,h,r),h+=4,l(b,h,o),h+=4,b[h]=e.depth,h++,b[h]=e.ctype,h++,b[h]=0,h++,b[h]=0,h++,b[h]=0,h++,l(b,h,f(b,h-17,17)),h+=4,null!=s.sRGB&&(l(b,h,1),h+=4,u(b,h,\"sRGB\"),h+=4,b[h]=s.sRGB,h++,l(b,h,f(b,h-5,5)),h+=4),null!=s.iCCP){const e=13+A.length;l(b,h,e),h+=4,u(b,h,\"iCCP\"),h+=4,u(b,h,\"ICC profile\"),h+=11,h+=2,b.set(A,h),h+=A.length,l(b,h,f(b,h-(e+4),e+4)),h+=4}if(null!=s.pHYs&&(l(b,h,9),h+=4,u(b,h,\"pHYs\"),h+=4,l(b,h,s.pHYs[0]),h+=4,l(b,h,s.pHYs[1]),h+=4,b[h]=s.pHYs[2],h++,l(b,h,f(b,h-13,13)),h+=4),d&&(l(b,h,8),h+=4,u(b,h,\"acTL\"),h+=4,l(b,h,e.frames.length),h+=4,l(b,h,null!=s.loop?s.loop:0),h+=4,l(b,h,f(b,h-12,12)),h+=4),3==e.ctype){l(b,h,3*(m=e.plte.length)),h+=4,u(b,h,\"PLTE\"),h+=4;for(w=0;w<m;w++){const t=3*w,r=e.plte[w],i=255&r,o=r>>>8&255,a=r>>>16&255;b[h+t+0]=i,b[h+t+1]=o,b[h+t+2]=a}if(h+=3*m,l(b,h,f(b,h-3*m-4,3*m+4)),h+=4,g){l(b,h,m),h+=4,u(b,h,\"tRNS\"),h+=4;for(w=0;w<m;w++)b[h+w]=e.plte[w]>>>24&255;h+=m,l(b,h,f(b,h-m-4,m+4)),h+=4}}let E=0;for(v=0;v<e.frames.length;v++){var F=e.frames[v];d&&(l(b,h,26),h+=4,u(b,h,\"fcTL\"),h+=4,l(b,h,E++),h+=4,l(b,h,F.rect.width),h+=4,l(b,h,F.rect.height),h+=4,l(b,h,F.rect.x),h+=4,l(b,h,F.rect.y),h+=4,c(b,h,a[v]),h+=2,c(b,h,1e3),h+=2,b[h]=F.dispose,h++,b[h]=F.blend,h++,l(b,h,f(b,h-30,30)),h+=4);const t=F.cimg;l(b,h,(m=t.length)+(0==v?0:4)),h+=4;const r=h;u(b,h,0==v?\"IDAT\":\"fdAT\"),h+=4,0!=v&&(l(b,h,E++),h+=4),b.set(t,h),h+=m,l(b,h,f(b,r,h-r)),h+=4}return l(b,h,0),h+=4,u(b,h,\"IEND\"),h+=4,l(b,h,f(b,h-4,4)),h+=4,b.buffer}function compressPNG(e,t,r){for(let i=0;i<e.frames.length;i++){const o=e.frames[i];o.rect.width;const a=o.rect.height,s=new Uint8Array(a*o.bpl+a);o.cimg=_filterZero(o.img,a,o.bpp,o.bpl,s,t,r)}}function compress(t,r,i,o,a){const s=a[0],f=a[1],l=a[2],c=a[3],u=a[4],h=a[5];let d=6,A=8,g=255;for(var p=0;p<t.length;p++){const e=new Uint8Array(t[p]);for(var m=e.length,w=0;w<m;w+=4)g&=e[w+3]}const v=255!=g,b=function framize(t,r,i,o,a,s){const f=[];for(var l=0;l<t.length;l++){const h=new Uint8Array(t[l]),A=new Uint32Array(h.buffer);var c;let g=0,p=0,m=r,w=i,v=o?1:0;if(0!=l){const b=s||o||1==l||0!=f[l-2].dispose?1:2;let y=0,E=1e9;for(let e=0;e<b;e++){var u=new Uint8Array(t[l-1-e]);const o=new Uint32Array(t[l-1-e]);let s=r,f=i,c=-1,h=-1;for(let e=0;e<i;e++)for(let t=0;t<r;t++){A[d=e*r+t]!=o[d]&&(t<s&&(s=t),t>c&&(c=t),e<f&&(f=e),e>h&&(h=e))}-1==c&&(s=f=c=h=0),a&&(1==(1&s)&&s--,1==(1&f)&&f--);const v=(c-s+1)*(h-f+1);v<E&&(E=v,y=e,g=s,p=f,m=c-s+1,w=h-f+1)}u=new Uint8Array(t[l-1-y]);1==y&&(f[l-1].dispose=2),c=new Uint8Array(m*w*4),e(u,r,i,c,m,w,-g,-p,0),v=e(h,r,i,c,m,w,-g,-p,3)?1:0,1==v?_prepareDiff(h,r,i,c,{x:g,y:p,width:m,height:w}):e(h,r,i,c,m,w,-g,-p,0)}else c=h.slice(0);f.push({rect:{x:g,y:p,width:m,height:w},img:c,blend:v,dispose:0})}if(o)for(l=0;l<f.length;l++){if(1==(A=f[l]).blend)continue;const e=A.rect,o=f[l-1].rect,s=Math.min(e.x,o.x),c=Math.min(e.y,o.y),u={x:s,y:c,width:Math.max(e.x+e.width,o.x+o.width)-s,height:Math.max(e.y+e.height,o.y+o.height)-c};f[l-1].dispose=1,l-1!=0&&_updateFrame(t,r,i,f,l-1,u,a),_updateFrame(t,r,i,f,l,u,a)}let h=0;if(1!=t.length)for(var d=0;d<f.length;d++){var A;h+=(A=f[d]).rect.width*A.rect.height}return f}(t,r,i,s,f,l),y={},E=[],F=[];if(0!=o){const e=[];for(w=0;w<b.length;w++)e.push(b[w].img.buffer);const t=function concatRGBA(e){let t=0;for(var r=0;r<e.length;r++)t+=e[r].byteLength;const i=new Uint8Array(t);let o=0;for(r=0;r<e.length;r++){const t=new Uint8Array(e[r]),a=t.length;for(let e=0;e<a;e+=4){let r=t[e],a=t[e+1],s=t[e+2];const f=t[e+3];0==f&&(r=a=s=0),i[o+e]=r,i[o+e+1]=a,i[o+e+2]=s,i[o+e+3]=f}o+=a}return i.buffer}(e),r=quantize(t,o);for(w=0;w<r.plte.length;w++)E.push(r.plte[w].est.rgba);let i=0;for(w=0;w<b.length;w++){const e=(B=b[w]).img.length;var _=new Uint8Array(r.inds.buffer,i>>2,e>>2);F.push(_);const t=new Uint8Array(r.abuf,i,e);h&&dither(B.img,B.rect.width,B.rect.height,E,t,_),B.img.set(t),i+=e}}else for(p=0;p<b.length;p++){var B=b[p];const e=new Uint32Array(B.img.buffer);var U=B.rect.width;m=e.length,_=new Uint8Array(m);F.push(_);for(w=0;w<m;w++){const t=e[w];if(0!=w&&t==e[w-1])_[w]=_[w-1];else if(w>U&&t==e[w-U])_[w]=_[w-U];else{let e=y[t];if(null==e&&(y[t]=e=E.length,E.push(t),E.length>=300))break;_[w]=e}}}const C=E.length;C<=256&&0==u&&(A=C<=2?1:C<=4?2:C<=16?4:8,A=Math.max(A,c));for(p=0;p<b.length;p++){(B=b[p]).rect.x,B.rect.y;U=B.rect.width;const e=B.rect.height;let t=B.img;new Uint32Array(t.buffer);let r=4*U,i=4;if(C<=256&&0==u){r=Math.ceil(A*U/8);var I=new Uint8Array(r*e);const o=F[p];for(let t=0;t<e;t++){w=t*r;const e=t*U;if(8==A)for(var Q=0;Q<U;Q++)I[w+Q]=o[e+Q];else if(4==A)for(Q=0;Q<U;Q++)I[w+(Q>>1)]|=o[e+Q]<<4-4*(1&Q);else if(2==A)for(Q=0;Q<U;Q++)I[w+(Q>>2)]|=o[e+Q]<<6-2*(3&Q);else if(1==A)for(Q=0;Q<U;Q++)I[w+(Q>>3)]|=o[e+Q]<<7-1*(7&Q)}t=I,d=3,i=1}else if(0==v&&1==b.length){I=new Uint8Array(U*e*3);const o=U*e;for(w=0;w<o;w++){const e=3*w,r=4*w;I[e]=t[r],I[e+1]=t[r+1],I[e+2]=t[r+2]}t=I,d=2,i=3,r=3*U}B.img=t,B.bpl=r,B.bpp=i}return{ctype:d,depth:A,plte:E,frames:b}}function _updateFrame(t,r,i,o,a,s,f){const l=Uint8Array,c=Uint32Array,u=new l(t[a-1]),h=new c(t[a-1]),d=a+1<t.length?new l(t[a+1]):null,A=new l(t[a]),g=new c(A.buffer);let p=r,m=i,w=-1,v=-1;for(let e=0;e<s.height;e++)for(let t=0;t<s.width;t++){const i=s.x+t,f=s.y+e,l=f*r+i,c=g[l];0==c||0==o[a-1].dispose&&h[l]==c&&(null==d||0!=d[4*l+3])||(i<p&&(p=i),i>w&&(w=i),f<m&&(m=f),f>v&&(v=f))}-1==w&&(p=m=w=v=0),f&&(1==(1&p)&&p--,1==(1&m)&&m--),s={x:p,y:m,width:w-p+1,height:v-m+1};const b=o[a];b.rect=s,b.blend=1,b.img=new Uint8Array(s.width*s.height*4),0==o[a-1].dispose?(e(u,r,i,b.img,s.width,s.height,-s.x,-s.y,0),_prepareDiff(A,r,i,b.img,s)):e(A,r,i,b.img,s.width,s.height,-s.x,-s.y,0)}function _prepareDiff(t,r,i,o,a){e(t,r,i,o,a.width,a.height,-a.x,-a.y,2)}function _filterZero(e,t,r,i,o,a,s){const f=[];let l,c=[0,1,2,3,4];-1!=a?c=[a]:(t*i>5e5||1==r)&&(c=[0]),s&&(l={level:0});const u=UZIP;for(var h=0;h<c.length;h++){for(let a=0;a<t;a++)_filterLine(o,e,a,i,r,c[h]);f.push(u.deflate(o,l))}let d,A=1e9;for(h=0;h<f.length;h++)f[h].length<A&&(d=h,A=f[h].length);return f[d]}function _filterLine(e,t,i,o,a,s){const f=i*o;let l=f+i;if(e[l]=s,l++,0==s)if(o<500)for(var c=0;c<o;c++)e[l+c]=t[f+c];else e.set(new Uint8Array(t.buffer,f,o),l);else if(1==s){for(c=0;c<a;c++)e[l+c]=t[f+c];for(c=a;c<o;c++)e[l+c]=t[f+c]-t[f+c-a]+256&255}else if(0==i){for(c=0;c<a;c++)e[l+c]=t[f+c];if(2==s)for(c=a;c<o;c++)e[l+c]=t[f+c];if(3==s)for(c=a;c<o;c++)e[l+c]=t[f+c]-(t[f+c-a]>>1)+256&255;if(4==s)for(c=a;c<o;c++)e[l+c]=t[f+c]-r(t[f+c-a],0,0)+256&255}else{if(2==s)for(c=0;c<o;c++)e[l+c]=t[f+c]+256-t[f+c-o]&255;if(3==s){for(c=0;c<a;c++)e[l+c]=t[f+c]+256-(t[f+c-o]>>1)&255;for(c=a;c<o;c++)e[l+c]=t[f+c]+256-(t[f+c-o]+t[f+c-a]>>1)&255}if(4==s){for(c=0;c<a;c++)e[l+c]=t[f+c]+256-r(0,t[f+c-o],0)&255;for(c=a;c<o;c++)e[l+c]=t[f+c]+256-r(t[f+c-a],t[f+c-o],t[f+c-a-o])&255}}}function quantize(e,t){const r=new Uint8Array(e),i=r.slice(0),o=new Uint32Array(i.buffer),a=getKDtree(i,t),s=a[0],f=a[1],l=r.length,c=new Uint8Array(l>>2);let u;if(r.length<2e7)for(var h=0;h<l;h+=4){u=getNearest(s,d=r[h]*(1/255),A=r[h+1]*(1/255),g=r[h+2]*(1/255),p=r[h+3]*(1/255)),c[h>>2]=u.ind,o[h>>2]=u.est.rgba}else for(h=0;h<l;h+=4){var d=r[h]*(1/255),A=r[h+1]*(1/255),g=r[h+2]*(1/255),p=r[h+3]*(1/255);for(u=s;u.left;)u=planeDst(u.est,d,A,g,p)<=0?u.left:u.right;c[h>>2]=u.ind,o[h>>2]=u.est.rgba}return{abuf:i.buffer,inds:c,plte:f}}function getKDtree(e,t,r){null==r&&(r=1e-4);const i=new Uint32Array(e.buffer),o={i0:0,i1:e.length,bst:null,est:null,tdst:0,left:null,right:null};o.bst=stats(e,o.i0,o.i1),o.est=estats(o.bst);const a=[o];for(;a.length<t;){let t=0,o=0;for(var s=0;s<a.length;s++)a[s].est.L>t&&(t=a[s].est.L,o=s);if(t<r)break;const f=a[o],l=splitPixels(e,i,f.i0,f.i1,f.est.e,f.est.eMq255);if(f.i0>=l||f.i1<=l){f.est.L=0;continue}const c={i0:f.i0,i1:l,bst:null,est:null,tdst:0,left:null,right:null};c.bst=stats(e,c.i0,c.i1),c.est=estats(c.bst);const u={i0:l,i1:f.i1,bst:null,est:null,tdst:0,left:null,right:null};u.bst={R:[],m:[],N:f.bst.N-c.bst.N};for(s=0;s<16;s++)u.bst.R[s]=f.bst.R[s]-c.bst.R[s];for(s=0;s<4;s++)u.bst.m[s]=f.bst.m[s]-c.bst.m[s];u.est=estats(u.bst),f.left=c,f.right=u,a[o]=c,a.push(u)}a.sort(((e,t)=>t.bst.N-e.bst.N));for(s=0;s<a.length;s++)a[s].ind=s;return[o,a]}function getNearest(e,t,r,i,o){if(null==e.left)return e.tdst=function dist(e,t,r,i,o){const a=t-e[0],s=r-e[1],f=i-e[2],l=o-e[3];return a*a+s*s+f*f+l*l}(e.est.q,t,r,i,o),e;const a=planeDst(e.est,t,r,i,o);let s=e.left,f=e.right;a>0&&(s=e.right,f=e.left);const l=getNearest(s,t,r,i,o);if(l.tdst<=a*a)return l;const c=getNearest(f,t,r,i,o);return c.tdst<l.tdst?c:l}function planeDst(e,t,r,i,o){const{e:a}=e;return a[0]*t+a[1]*r+a[2]*i+a[3]*o-e.eMq}function splitPixels(e,t,r,i,o,a){for(i-=4;r<i;){for(;vecDot(e,r,o)<=a;)r+=4;for(;vecDot(e,i,o)>a;)i-=4;if(r>=i)break;const s=t[r>>2];t[r>>2]=t[i>>2],t[i>>2]=s,r+=4,i-=4}for(;vecDot(e,r,o)>a;)r-=4;return r+4}function vecDot(e,t,r){return e[t]*r[0]+e[t+1]*r[1]+e[t+2]*r[2]+e[t+3]*r[3]}function stats(e,t,r){const i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o=[0,0,0,0],a=r-t>>2;for(let a=t;a<r;a+=4){const t=e[a]*(1/255),r=e[a+1]*(1/255),s=e[a+2]*(1/255),f=e[a+3]*(1/255);o[0]+=t,o[1]+=r,o[2]+=s,o[3]+=f,i[0]+=t*t,i[1]+=t*r,i[2]+=t*s,i[3]+=t*f,i[5]+=r*r,i[6]+=r*s,i[7]+=r*f,i[10]+=s*s,i[11]+=s*f,i[15]+=f*f}return i[4]=i[1],i[8]=i[2],i[9]=i[6],i[12]=i[3],i[13]=i[7],i[14]=i[11],{R:i,m:o,N:a}}function estats(e){const{R:t}=e,{m:r}=e,{N:i}=e,a=r[0],s=r[1],f=r[2],l=r[3],c=0==i?0:1/i,u=[t[0]-a*a*c,t[1]-a*s*c,t[2]-a*f*c,t[3]-a*l*c,t[4]-s*a*c,t[5]-s*s*c,t[6]-s*f*c,t[7]-s*l*c,t[8]-f*a*c,t[9]-f*s*c,t[10]-f*f*c,t[11]-f*l*c,t[12]-l*a*c,t[13]-l*s*c,t[14]-l*f*c,t[15]-l*l*c],h=u,d=o;let A=[Math.random(),Math.random(),Math.random(),Math.random()],g=0,p=0;if(0!=i)for(let e=0;e<16&&(A=d.multVec(h,A),p=Math.sqrt(d.dot(A,A)),A=d.sml(1/p,A),!(0!=e&&Math.abs(p-g)<1e-9));e++)g=p;const m=[a*c,s*c,f*c,l*c];return{Cov:u,q:m,e:A,L:g,eMq255:d.dot(d.sml(255,m),A),eMq:d.dot(A,m),rgba:(Math.round(255*m[3])<<24|Math.round(255*m[2])<<16|Math.round(255*m[1])<<8|Math.round(255*m[0])<<0)>>>0}}var o={multVec:(e,t)=>[e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3],e[4]*t[0]+e[5]*t[1]+e[6]*t[2]+e[7]*t[3],e[8]*t[0]+e[9]*t[1]+e[10]*t[2]+e[11]*t[3],e[12]*t[0]+e[13]*t[1]+e[14]*t[2]+e[15]*t[3]],dot:(e,t)=>e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3],sml:(e,t)=>[e*t[0],e*t[1],e*t[2],e*t[3]]};UPNG.encode=function encode(e,t,r,i,o,a,s){null==i&&(i=0),null==s&&(s=!1);const f=compress(e,t,r,i,[!1,!1,!1,0,s,!1]);return compressPNG(f,-1),_main(f,t,r,o,a)},UPNG.encodeLL=function encodeLL(e,t,r,i,o,a,s,f){const l={ctype:0+(1==i?0:2)+(0==o?0:4),depth:a,frames:[]},c=(i+o)*a,u=c*t;for(let i=0;i<e.length;i++)l.frames.push({rect:{x:0,y:0,width:t,height:r},img:new Uint8Array(e[i]),blend:0,dispose:1,bpp:Math.ceil(c/8),bpl:Math.ceil(u/8)});return compressPNG(l,0,!0),_main(l,t,r,s,f)},UPNG.encode.compress=compress,UPNG.encode.dither=dither,UPNG.quantize=quantize,UPNG.quantize.getKDtree=getKDtree,UPNG.quantize.getNearest=getNearest}();const r={toArrayBuffer(e,t){const i=e.width,o=e.height,a=i<<2,s=e.getContext(\"2d\").getImageData(0,0,i,o),f=new Uint32Array(s.data.buffer),l=(32*i+31)/32<<2,c=l*o,u=122+c,h=new ArrayBuffer(u),d=new DataView(h),A=1<<20;let g,p,m,w,v=A,b=0,y=0,E=0;function set16(e){d.setUint16(y,e,!0),y+=2}function set32(e){d.setUint32(y,e,!0),y+=4}function seek(e){y+=e}set16(19778),set32(u),seek(4),set32(122),set32(108),set32(i),set32(-o>>>0),set16(1),set16(32),set32(3),set32(c),set32(2835),set32(2835),seek(8),set32(16711680),set32(65280),set32(255),set32(4278190080),set32(1466527264),function convert(){for(;b<o&&v>0;){for(w=122+b*l,g=0;g<a;)v--,p=f[E++],m=p>>>24,d.setUint32(w+g,p<<8|m),g+=4;b++}E<f.length?(v=A,setTimeout(convert,r._dly)):t(h)}()},toBlob(e,t){this.toArrayBuffer(e,(e=>{t(new Blob([e],{type:\"image/bmp\"}))}))},_dly:9};var i={CHROME:\"CHROME\",FIREFOX:\"FIREFOX\",DESKTOP_SAFARI:\"DESKTOP_SAFARI\",IE:\"IE\",IOS:\"IOS\",ETC:\"ETC\"},o={[i.CHROME]:16384,[i.FIREFOX]:11180,[i.DESKTOP_SAFARI]:16384,[i.IE]:8192,[i.IOS]:4096,[i.ETC]:8192};const a=\"undefined\"!=typeof window,s=\"undefined\"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,f=a&&window.cordova&&window.cordova.require&&window.cordova.require(\"cordova/modulemapper\"),CustomFile=(a||s)&&(f&&f.getOriginalSymbol(window,\"File\")||\"undefined\"!=typeof File&&File),CustomFileReader=(a||s)&&(f&&f.getOriginalSymbol(window,\"FileReader\")||\"undefined\"!=typeof FileReader&&FileReader);function getFilefromDataUrl(e,t,r=Date.now()){return new Promise((i=>{const o=e.split(\",\"),a=o[0].match(/:(.*?);/)[1],s=globalThis.atob(o[1]);let f=s.length;const l=new Uint8Array(f);for(;f--;)l[f]=s.charCodeAt(f);const c=new Blob([l],{type:a});c.name=t,c.lastModified=r,i(c)}))}function getDataUrlFromFile(e){return new Promise(((t,r)=>{const i=new CustomFileReader;i.onload=()=>t(i.result),i.onerror=e=>r(e),i.readAsDataURL(e)}))}function loadImage(e){return new Promise(((t,r)=>{const i=new Image;i.onload=()=>t(i),i.onerror=e=>r(e),i.src=e}))}function getBrowserName(){if(void 0!==getBrowserName.cachedResult)return getBrowserName.cachedResult;let e=i.ETC;const{userAgent:t}=navigator;return/Chrom(e|ium)/i.test(t)?e=i.CHROME:/iP(ad|od|hone)/i.test(t)&&/WebKit/i.test(t)?e=i.IOS:/Safari/i.test(t)?e=i.DESKTOP_SAFARI:/Firefox/i.test(t)?e=i.FIREFOX:(/MSIE/i.test(t)||!0==!!document.documentMode)&&(e=i.IE),getBrowserName.cachedResult=e,getBrowserName.cachedResult}function approximateBelowMaximumCanvasSizeOfBrowser(e,t){const r=getBrowserName(),i=o[r];let a=e,s=t,f=a*s;const l=a>s?s/a:a/s;for(;f>i*i;){const e=(i+a)/2,t=(i+s)/2;e<t?(s=t,a=t*l):(s=e*l,a=e),f=a*s}return{width:a,height:s}}function getNewCanvasAndCtx(e,t){let r,i;try{if(r=new OffscreenCanvas(e,t),i=r.getContext(\"2d\"),null===i)throw new Error(\"getContext of OffscreenCanvas returns null\")}catch(e){r=document.createElement(\"canvas\"),i=r.getContext(\"2d\")}return r.width=e,r.height=t,[r,i]}function drawImageInCanvas(e,t){const{width:r,height:i}=approximateBelowMaximumCanvasSizeOfBrowser(e.width,e.height),[o,a]=getNewCanvasAndCtx(r,i);return t&&/jpe?g/.test(t)&&(a.fillStyle=\"white\",a.fillRect(0,0,o.width,o.height)),a.drawImage(e,0,0,o.width,o.height),o}function isIOS(){return void 0!==isIOS.cachedResult||(isIOS.cachedResult=[\"iPad Simulator\",\"iPhone Simulator\",\"iPod Simulator\",\"iPad\",\"iPhone\",\"iPod\"].includes(navigator.platform)||navigator.userAgent.includes(\"Mac\")&&\"undefined\"!=typeof document&&\"ontouchend\"in document),isIOS.cachedResult}function drawFileInCanvas(e,t={}){return new Promise((function(r,o){let a,s;var $Try_2_Post=function(){try{return s=drawImageInCanvas(a,t.fileType||e.type),r([a,s])}catch(e){return o(e)}},$Try_2_Catch=function(t){try{0;var $Try_3_Catch=function(e){try{throw e}catch(e){return o(e)}};try{let t;return getDataUrlFromFile(e).then((function(e){try{return t=e,loadImage(t).then((function(e){try{return a=e,function(){try{return $Try_2_Post()}catch(e){return o(e)}}()}catch(e){return $Try_3_Catch(e)}}),$Try_3_Catch)}catch(e){return $Try_3_Catch(e)}}),$Try_3_Catch)}catch(e){$Try_3_Catch(e)}}catch(e){return o(e)}};try{if(isIOS()||[i.DESKTOP_SAFARI,i.MOBILE_SAFARI].includes(getBrowserName()))throw new Error(\"Skip createImageBitmap on IOS and Safari\");return createImageBitmap(e).then((function(e){try{return a=e,$Try_2_Post()}catch(e){return $Try_2_Catch()}}),$Try_2_Catch)}catch(e){$Try_2_Catch()}}))}function canvasToFile(e,t,i,o,a=1){return new Promise((function(s,f){let l;if(\"image/png\"===t){let c,u,h;return c=e.getContext(\"2d\"),({data:u}=c.getImageData(0,0,e.width,e.height)),h=UPNG.encode([u.buffer],e.width,e.height,4096*a),l=new Blob([h],{type:t}),l.name=i,l.lastModified=o,$If_4.call(this)}{if(\"image/bmp\"===t)return new Promise((t=>r.toBlob(e,t))).then(function(e){try{return l=e,l.name=i,l.lastModified=o,$If_5.call(this)}catch(e){return f(e)}}.bind(this),f);{if(\"function\"==typeof OffscreenCanvas&&e instanceof OffscreenCanvas)return e.convertToBlob({type:t,quality:a}).then(function(e){try{return l=e,l.name=i,l.lastModified=o,$If_6.call(this)}catch(e){return f(e)}}.bind(this),f);{let d;return d=e.toDataURL(t,a),getFilefromDataUrl(d,i,o).then(function(e){try{return l=e,$If_6.call(this)}catch(e){return f(e)}}.bind(this),f)}function $If_6(){return $If_5.call(this)}}function $If_5(){return $If_4.call(this)}}function $If_4(){return s(l)}}))}function cleanupCanvasMemory(e){e.width=0,e.height=0}function isAutoOrientationInBrowser(){return new Promise((function(e,t){let r,i,o,a,s;return void 0!==isAutoOrientationInBrowser.cachedResult?e(isAutoOrientationInBrowser.cachedResult):(r=\"data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==\",getFilefromDataUrl(\"data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==\",\"test.jpg\",Date.now()).then((function(r){try{return i=r,drawFileInCanvas(i).then((function(r){try{return o=r[1],canvasToFile(o,i.type,i.name,i.lastModified).then((function(r){try{return a=r,cleanupCanvasMemory(o),drawFileInCanvas(a).then((function(r){try{return s=r[0],isAutoOrientationInBrowser.cachedResult=1===s.width&&2===s.height,e(isAutoOrientationInBrowser.cachedResult)}catch(e){return t(e)}}),t)}catch(e){return t(e)}}),t)}catch(e){return t(e)}}),t)}catch(e){return t(e)}}),t))}))}function getExifOrientation(e){return new Promise(((t,r)=>{const i=new CustomFileReader;i.onload=e=>{const r=new DataView(e.target.result);if(65496!=r.getUint16(0,!1))return t(-2);const i=r.byteLength;let o=2;for(;o<i;){if(r.getUint16(o+2,!1)<=8)return t(-1);const e=r.getUint16(o,!1);if(o+=2,65505==e){if(1165519206!=r.getUint32(o+=2,!1))return t(-1);const e=18761==r.getUint16(o+=6,!1);o+=r.getUint32(o+4,e);const i=r.getUint16(o,e);o+=2;for(let a=0;a<i;a++)if(274==r.getUint16(o+12*a,e))return t(r.getUint16(o+12*a+8,e))}else{if(65280!=(65280&e))break;o+=r.getUint16(o,!1)}}return t(-1)},i.onerror=e=>r(e),i.readAsArrayBuffer(e)}))}function handleMaxWidthOrHeight(e,t){const{width:r}=e,{height:i}=e,{maxWidthOrHeight:o}=t;let a,s=e;return isFinite(o)&&(r>o||i>o)&&([s,a]=getNewCanvasAndCtx(r,i),r>i?(s.width=o,s.height=i/r*o):(s.width=r/i*o,s.height=o),a.drawImage(e,0,0,s.width,s.height),cleanupCanvasMemory(e)),s}function followExifOrientation(e,t){const{width:r}=e,{height:i}=e,[o,a]=getNewCanvasAndCtx(r,i);switch(t>4&&t<9?(o.width=i,o.height=r):(o.width=r,o.height=i),t){case 2:a.transform(-1,0,0,1,r,0);break;case 3:a.transform(-1,0,0,-1,r,i);break;case 4:a.transform(1,0,0,-1,0,i);break;case 5:a.transform(0,1,1,0,0,0);break;case 6:a.transform(0,1,-1,0,i,0);break;case 7:a.transform(0,-1,-1,0,i,r);break;case 8:a.transform(0,-1,1,0,0,r)}return a.drawImage(e,0,0,r,i),cleanupCanvasMemory(e),o}function compress(e,t,r=0){return new Promise((function(i,o){let a,s,f,l,c,u,h,d,A,g,p,m,w,v,b,y,E,F,_,B;function incProgress(e=5){if(t.signal&&t.signal.aborted)throw t.signal.reason;a+=e,t.onProgress(Math.min(a,100))}function setProgress(e){if(t.signal&&t.signal.aborted)throw t.signal.reason;a=Math.min(Math.max(e,a),100),t.onProgress(a)}return a=r,s=t.maxIteration||10,f=1024*t.maxSizeMB*1024,incProgress(),drawFileInCanvas(e,t).then(function(r){try{return[,l]=r,incProgress(),c=handleMaxWidthOrHeight(l,t),incProgress(),new Promise((function(r,i){var o;if(!(o=t.exifOrientation))return getExifOrientation(e).then(function(e){try{return o=e,$If_2.call(this)}catch(e){return i(e)}}.bind(this),i);function $If_2(){return r(o)}return $If_2.call(this)})).then(function(r){try{return u=r,incProgress(),isAutoOrientationInBrowser().then(function(r){try{return h=r?c:followExifOrientation(c,u),incProgress(),d=t.initialQuality||1,A=t.fileType||e.type,canvasToFile(h,A,e.name,e.lastModified,d).then(function(r){try{{if(g=r,incProgress(),p=g.size>f,m=g.size>e.size,!p&&!m)return setProgress(100),i(g);var a;function $Loop_3(){if(s--&&(b>f||b>w)){let t,r;return t=B?.95*_.width:_.width,r=B?.95*_.height:_.height,[E,F]=getNewCanvasAndCtx(t,r),F.drawImage(_,0,0,t,r),d*=\"image/png\"===A?.85:.95,canvasToFile(E,A,e.name,e.lastModified,d).then((function(e){try{return y=e,cleanupCanvasMemory(_),_=E,b=y.size,setProgress(Math.min(99,Math.floor((v-b)/(v-f)*100))),$Loop_3}catch(e){return o(e)}}),o)}return[1]}return w=e.size,v=g.size,b=v,_=h,B=!t.alwaysKeepResolution&&p,(a=function(e){for(;e;){if(e.then)return void e.then(a,o);try{if(e.pop){if(e.length)return e.pop()?$Loop_3_exit.call(this):e;e=$Loop_3}else e=e.call(this)}catch(e){return o(e)}}}.bind(this))($Loop_3);function $Loop_3_exit(){return cleanupCanvasMemory(_),cleanupCanvasMemory(E),cleanupCanvasMemory(c),cleanupCanvasMemory(h),cleanupCanvasMemory(l),setProgress(100),i(y)}}}catch(u){return o(u)}}.bind(this),o)}catch(e){return o(e)}}.bind(this),o)}catch(e){return o(e)}}.bind(this),o)}catch(e){return o(e)}}.bind(this),o)}))}const l=\"\\nlet scriptImported = false\\nself.addEventListener('message', async (e) => {\\n  const { file, id, imageCompressionLibUrl, options } = e.data\\n  options.onProgress = (progress) => self.postMessage({ progress, id })\\n  try {\\n    if (!scriptImported) {\\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\\n      self.importScripts(imageCompressionLibUrl)\\n      scriptImported = true\\n    }\\n    // console.log('[worker] self', self)\\n    const compressedFile = await imageCompression(file, options)\\n    self.postMessage({ file: compressedFile, id })\\n  } catch (e) {\\n    // console.error('[worker] error', e)\\n    self.postMessage({ error: e.message + '\\\\n' + e.stack, id })\\n  }\\n})\\n\";let c;function compressOnWebWorker(e,t){return new Promise(((r,i)=>{c||(c=function createWorkerScriptURL(e){const t=[];return\"function\"==typeof e?t.push(`(${e})()`):t.push(e),URL.createObjectURL(new Blob(t))}(l));const o=new Worker(c);o.addEventListener(\"message\",(function handler(e){if(t.signal&&t.signal.aborted)o.terminate();else if(void 0===e.data.progress){if(e.data.error)return i(new Error(e.data.error)),void o.terminate();r(e.data.file),o.terminate()}else t.onProgress(e.data.progress)})),o.addEventListener(\"error\",i),t.signal&&t.signal.addEventListener(\"abort\",(()=>{i(t.signal.reason),o.terminate()})),o.postMessage({file:e,imageCompressionLibUrl:t.libURL,options:{...t,onProgress:void 0,signal:void 0}})}))}function imageCompression(e,t){return new Promise((function(r,i){let o,a,s,f,l,c;if(o={...t},s=0,({onProgress:f}=o),o.maxSizeMB=o.maxSizeMB||Number.POSITIVE_INFINITY,l=\"boolean\"!=typeof o.useWebWorker||o.useWebWorker,delete o.useWebWorker,o.onProgress=e=>{s=e,\"function\"==typeof f&&f(s)},!(e instanceof Blob||e instanceof CustomFile))return i(new Error(\"The file given is not an instance of Blob or File\"));if(!/^image/.test(e.type))return i(new Error(\"The file given is not an image\"));if(c=\"undefined\"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,!l||\"function\"!=typeof Worker||c)return compress(e,o).then(function(e){try{return a=e,$If_4.call(this)}catch(e){return i(e)}}.bind(this),i);var u=function(){try{return $If_4.call(this)}catch(e){return i(e)}}.bind(this),$Try_1_Catch=function(t){try{return compress(e,o).then((function(e){try{return a=e,u()}catch(e){return i(e)}}),i)}catch(e){return i(e)}};try{return o.libURL=o.libURL||\"https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js\",compressOnWebWorker(e,o).then((function(e){try{return a=e,u()}catch(e){return $Try_1_Catch()}}),$Try_1_Catch)}catch(e){$Try_1_Catch()}function $If_4(){try{a.name=e.name,a.lastModified=e.lastModified}catch(e){}try{o.preserveExif&&\"image/jpeg\"===e.type&&(!o.fileType||o.fileType&&o.fileType===e.type)&&(a=copyExifWithoutOrientation(e,a))}catch(e){}return r(a)}}))}imageCompression.getDataUrlFromFile=getDataUrlFromFile,imageCompression.getFilefromDataUrl=getFilefromDataUrl,imageCompression.loadImage=loadImage,imageCompression.drawImageInCanvas=drawImageInCanvas,imageCompression.drawFileInCanvas=drawFileInCanvas,imageCompression.canvasToFile=canvasToFile,imageCompression.getExifOrientation=getExifOrientation,imageCompression.handleMaxWidthOrHeight=handleMaxWidthOrHeight,imageCompression.followExifOrientation=followExifOrientation,imageCompression.cleanupCanvasMemory=cleanupCanvasMemory,imageCompression.isAutoOrientationInBrowser=isAutoOrientationInBrowser,imageCompression.approximateBelowMaximumCanvasSizeOfBrowser=approximateBelowMaximumCanvasSizeOfBrowser,imageCompression.copyExifWithoutOrientation=copyExifWithoutOrientation,imageCompression.getBrowserName=getBrowserName,imageCompression.version=\"2.0.2\";\n//# sourceMappingURL=browser-image-compression.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/browser-image-compression/dist/browser-image-compression.mjs\n");

/***/ })

};
;