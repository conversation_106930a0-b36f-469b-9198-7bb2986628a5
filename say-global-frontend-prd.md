# Say Global - Frontend Geliştirme Özeti

## Projenin Amacı

Say Global, ürün satışını hem bireysel müşterilere hem de distribütör ağı üzerinden yapan bir e-ticaret platformudur. Bu geliştirme aşaması<PERSON>, uygulamanın sadece **kullanıcı arayüzleri** oluşturulacaktır. Arka plan bağlantıları yoktur, her şey **örnek veriler (mock data)** ile çalışacaktır.

---

## <PERSON><PERSON> He<PERSON><PERSON>, modern ve anlaşılır bir tasarımla aşağıdaki kullanıcı deneyimlerini sunmak:

* Bir ziyaretçi ürünleri inceleyip sepete ekleyebilsin.
* Kay<PERSON>t olabilir, giriş yapabilir gibi hissetsin.
* Sipariş verme ekranlarını görebilsin.
* Bir distribütör kazancını ve ekibini görüntüleyebilsin.
* <PERSON><PERSON><PERSON>im kurulu ve yatırımcılar, bu prototipi inceleyip projenin vizyonunu anlayabilsin.

---

## Geliştirilecek Sayfalar ve Kullanıcı Deneyimi

### 1. Anasayfa (`/`)

* Büyük tanıtım görselleri ve başlıklar
* Kategori kartları (örneğin “Cilt Bakımı”, “Takviye Ürünler”)
* Popüler veya yeni ürünler
* Kampanya alanları ve haber kutuları

### 2. Ürün Listeleme (`/urunler`)

* Ürün kartları (görsel, fiyat, isim, puan)
* Filtre seçenekleri: kategori, fiyat aralığı, marka vb.
* Sıralama menüsü (örneğin “Fiyata göre”, “Popüler”)
* Sayfa geçiş (sayfalama)

### 3. Ürün Detay (`/urun/[id]`)

* Büyük ürün görselleri (tıklayınca büyür)
* Ürün açıklaması, fiyat, stok bilgisi
* Kullanıcı yorumları ve yıldız puanları
* Sepete ekleme seçeneği ve miktar seçici

### 4. Sepet (`/sepet`)

* Sepetteki ürünlerin listesi
* Miktar değiştirme ve ürün çıkarma
* Ara toplam, KDV, kargo ve genel toplam
* “Siparişi Tamamla” butonu

### 5. Sipariş Tamamlama (`/siparis/odeme`)

* Teslimat ve fatura bilgileri (form olarak)
* Kargo seçimi (örnek firmalar)
* Ödeme yöntemi (örnek kredi kartı formu)
* Sipariş özeti ve “Onayla” butonu

### 6. Giriş (`/giris`)

* E-posta ve şifre alanları
* “Şifremi Unuttum” ve “Kayıt Ol” linkleri
* (Gerçek giriş yapılmaz, sadece arayüz)

### 7. Kayıt Ol (`/kayit-ol`)

* Ad, soyad, e-posta, şifre formu
* KVKK onay kutucuğu
* “Kayıt Ol” butonu

### 8. Şifremi Unuttum (`/sifremi-unuttum`)

* E-posta girerek sıfırlama linki isteği
* “E-postaya gönderildi” mesajı

### 9. Şifre Sıfırla (`/sifre-sifirla`)

* Yeni şifre ve tekrar alanları
* “Şifreyi Güncelle” ve yönlendirme mesajı

### 10. Müşteri Paneli (`/hesabim`)

Kullanıcının bilgilerini görebileceği bir sayfa:

* “Profilim”, “Siparişlerim”, “Adreslerim”, “Çıkış Yap” gibi bölümler
* Sipariş geçmişi listesi (sipariş no, tarih, durum)
* **Son Siparişlerim**: En son verilen siparişlerin listesi (sipariş no, tarih, durum, toplam tutar)
* **Bakiye**: Kullanıcının mevcut hesap bakiyesi (örneğin, hediye çeki veya iade bakiyesi)
* **Puanlarım**: Kullanıcının ürün satın alarak kazandığı veya harcadığı puanlar

### 11. Siparişlerim (`/siparislerim`)

* Kullanıcının geçmiş siparişlerinin listesi (sipariş no, tarih, durum, toplam tutar)
* Her sipariş için detay görüntüleme seçeneği
* **Sipariş Detay**: Seçilen siparişin ürünleri, miktarları, fiyatları, kargo bilgisi ve toplam tutarı

### 12. Duyurular (`/duyurular`)

* Sistem genelindeki duyuruların listelendiği sayfa
* Duyuru başlığı, tarihi ve kısa açıklaması
* Detaylı duyuru içeriği için tıklama seçeneği

### 13. Distribütör Paneli (`/panel`)

Distribütörler için özel bir panel yer alacaktır. Burada:

* **Toplam Kazanç**: Kullanıcının şimdiye kadarki kazancı
* **Aylık Puan**: O ay topladığı puan (örneğin 350 Puan)
* **Aylık Aktiflik**: Bu ay içerisinde minimum 75 puanlık sipariş vererek aktif olma durumu (yüzde olarak gösterim).
* **Ekip Sayısı**: Sisteme dahil ettiği kişi sayısı
* **Aylık Bakiye**: Kullanıcının o ay çekilebilir mevcut bakiyesi
* **Bakiye**: Hesapta biriken toplam bakiye.
* **Puanlar**: Üyeliğin başladığı tarihten itibaren toplam puan.
* **Grafik**: Aylara göre kazanç değişimi ve aylık aktiflik durumu (basit çizgi grafik)
* **Ekibim Tablosu**: Alt üyelerin isimleri, seviyeleri, katılım tarihleri, puanları
* Toplam organizasyon puanı

Kullanıcıya ilerlediği hissini vermeli. Kartlar renkli, ikonlu ve motive edici görünmeli. “Bir üst seviyeye ulaşmak için şu kadar puan kaldı” gibi metinler eklenebilir (UI bazlı).

### 14. Kazançlar (`/kazanclar`)

* Distribütörün kazanç detaylarını gösteren sayfa
* **Tablo**: Tarih, Referans (kimden geldiği), Puan (kazanılan puan), Miktar (kazanç miktarı), Seviye (kazancın geldiği seviye), Yüzde (kazanç yüzdesi) bilgilerini içeren liste

### 15. Yönetici Paneli (`/admin`) (Basit Prototip Amaçlı)

* Giriş ekranı
* Ürün, sipariş, üye gibi listelerin görüntülenebileceği basit bir tablo
* Gerçek işlem yapılmaz, sadece nasıl görüneceğine dair önizleme sunulur

---

## Veri ve Görseller

* Tüm veriler örnek olarak hazırlanacak (**mock ürünler, kullanıcılar, yorumlar, siparişler**).
* Görseller için **picsum.photos** veya **placeholder.com** gibi servisler kullanılabilir.
* Tip tanımlamaları (örnek: Ürün, Sipariş, Kullanıcı) sade olarak hazırlanacak.

---

## Teknolojiler

* **Next.js** (yeni nesil sayfa yapısı için)
* **Tailwind CSS** (hızlı ve responsive tasarım için)
* **TypeScript** (düzenli ve güvenli geliştirme için)
* Veriler **.ts** veya **.json** dosyası olarak kullanılacak.
* Giriş, kayıt gibi işlemler sadece görsel olarak çalışır (API yok).

---

## Proje Yapısı

Bu proje, aşağıdaki gibi sade ve düzenli bir yapıda kurgulanacaktır:

* Tüm kaynaklar `src/` klasöründe yer alır.
* `src/app/` klasörü altında sayfalar oluşturulur. Örneğin:
    * `giris/`, `kayit-ol/`, `urunler/`, `sepet/`, `panel/` vb. route klasörleri bulunur.
    * Her klasörde bir `page.tsx` dosyası yer alır.
* `src/app/` ayrıca `layout.tsx`, `page.tsx` (anasayfa) ve `globals.css` dosyalarını içerir.
* Yeniden kullanılabilir bileşenler `src/components/` klasörüne yazılır.
* Örnek veriler `src/data/mocks/` klasöründe tutulur.
* Tip tanımlamaları `src/types/` klasöründe toplanır.
* Projenin kök dizininde yapılandırma dosyaları bulunur (`next.config.ts`, `tsconfig.json`, `.env`, `package.json` vb.).

---

## Sonuçta Ne Elde Edilecek?

* Her sayfanın modern, sade ve işlevsel arayüzü hazırlanmış olacak.
* Yönetim kuruluna veya yatırımcıya canlı bir prototip sunulabilecek.
* Kullanıcı bir e-ticaret sisteminde neler yapabileceğini gerçekmiş gibi deneyimleyecek.