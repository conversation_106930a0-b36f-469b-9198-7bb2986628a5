'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { DealershipApplicationData, DealershipApplication } from '@/types';
import { productCategories, estimatedProductCountOptions, mockDealershipApplications } from '@/data/mocks/dealership';
import DealerApplicationSuccessModal from '@/components/DealerApplicationSuccessModal';
import {
    Building2,
    User,
    Phone,
    Mail,
    Package,
    FileText,
    CheckCircle,
    ArrowRight,
    AlertCircle,
    Eye,
    Upload,
    X
} from 'lucide-react';

const BecomeDealerPage = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();

    const [formData, setFormData] = useState<DealershipApplicationData>({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        mainProductCategory: '',
        subProductCategories: [],
        estimatedProductCount: '',
        sampleProductListUrl: '',
        companyName: '',
        taxNumber: '',
        taxOffice: '',
        companyAddress: '',
        taxCertificate: null,
        tradeRegistryGazette: null,
        signatureCircular: null,
        additionalDocuments: [],
        authorizedPersonName: '',
        authorizedPersonTcId: '',
        alternativeContactNumber: '',
        userAgreementAccepted: false,
        dealershipAgreementAccepted: false,
        privacyPolicyAccepted: false
    });

    const [currentStep, setCurrentStep] = useState(1);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [showSuccessModal, setShowSuccessModal] = useState(false);

    // Kullanıcı bilgilerini forma yükle
    useEffect(() => {
        if (user) {
            setFormData(prev => ({
                ...prev,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                phone: user.phone,
                authorizedPersonName: `${user.firstName} ${user.lastName}`
            }));
        }
    }, [user]);

    // Giriş yapmamış kullanıcıları login sayfasına yönlendir
    useEffect(() => {
        if (!isLoading && !user) {
            router.push('/login?redirect=/become-dealer');
        }
    }, [user, isLoading, router]);

    const handleInputChange = (field: keyof DealershipApplicationData, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Hata varsa temizle
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    // Dosya yükleme işlemi
    const handleFileUpload = (field: keyof DealershipApplicationData, file: File, inputElement?: HTMLInputElement) => {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

        if (file.size > maxSize) {
            alert('Dosya boyutu 5MB\'dan büyük olamaz');
            // Input'u temizle
            if (inputElement) inputElement.value = '';
            return;
        }

        if (!allowedTypes.includes(file.type)) {
            alert('Sadece JPEG, PNG ve PDF dosyaları yükleyebilirsiniz');
            // Input'u temizle
            if (inputElement) inputElement.value = '';
            return;
        }

        handleInputChange(field, file);

        // Dosya başarıyla yüklendikten sonra input'u temizle
        if (inputElement) inputElement.value = '';
    };

    // Dosya silme işlemi
    const handleFileRemove = (field: keyof DealershipApplicationData) => {
        handleInputChange(field, null);
    };

    // Ek dosya ekleme
    const handleAdditionalFileAdd = (file: File, inputElement?: HTMLInputElement) => {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

        if (file.size > maxSize) {
            alert('Dosya boyutu 5MB\'dan büyük olamaz');
            // Input'u temizle
            if (inputElement) inputElement.value = '';
            return;
        }

        if (!allowedTypes.includes(file.type)) {
            alert('Sadece JPEG, PNG ve PDF dosyaları yükleyebilirsiniz');
            // Input'u temizle
            if (inputElement) inputElement.value = '';
            return;
        }

        setFormData(prev => ({
            ...prev,
            additionalDocuments: [...(prev.additionalDocuments || []), file]
        }));

        // Dosya başarıyla eklendikten sonra input'u temizle
        if (inputElement) inputElement.value = '';
    };

    // Ek dosya silme
    const handleAdditionalFileRemove = (index: number) => {
        setFormData(prev => ({
            ...prev,
            additionalDocuments: prev.additionalDocuments?.filter((_, i) => i !== index) || []
        }));
    };

    const validateStep = (step: number): boolean => {
        const newErrors: { [key: string]: string } = {};

        switch (step) {
            case 1: // Ürün Bilgileri
                if (!formData.mainProductCategory) {
                    newErrors.mainProductCategory = 'Ana ürün kategorisi seçmelisiniz';
                }
                if (!formData.estimatedProductCount) {
                    newErrors.estimatedProductCount = 'Tahmini ürün sayısı seçmelisiniz';
                }
                break;

            case 2: // Şirket Bilgileri
                if (!formData.companyName) {
                    newErrors.companyName = 'Şirket adı gereklidir';
                }
                if (!formData.taxNumber) {
                    newErrors.taxNumber = 'Vergi kimlik numarası gereklidir';
                }
                if (!formData.taxOffice) {
                    newErrors.taxOffice = 'Vergi dairesi gereklidir';
                }
                if (!formData.companyAddress) {
                    newErrors.companyAddress = 'Şirket adresi gereklidir';
                }
                break;

            case 3: // Belgeler - zorunlu belge kontrolü
                if (!formData.taxCertificate) {
                    newErrors.taxCertificate = 'Vergi levhası gereklidir';
                }
                break;

            case 4: // Yetkili Kişi Bilgileri
                if (!formData.authorizedPersonName) {
                    newErrors.authorizedPersonName = 'Yetkili kişi adı gereklidir';
                }
                if (!formData.authorizedPersonTcId) {
                    newErrors.authorizedPersonTcId = 'T.C. kimlik numarası gereklidir';
                }
                if (!formData.alternativeContactNumber) {
                    newErrors.alternativeContactNumber = 'Alternatif iletişim numarası gereklidir';
                }
                break;

            case 5: // Sözleşme Onayları
                if (!formData.userAgreementAccepted) {
                    newErrors.userAgreementAccepted = 'Kullanıcı sözleşmesini kabul etmelisiniz';
                }
                if (!formData.dealershipAgreementAccepted) {
                    newErrors.dealershipAgreementAccepted = 'Satıcı sözleşmesini kabul etmelisiniz';
                }
                if (!formData.privacyPolicyAccepted) {
                    newErrors.privacyPolicyAccepted = 'Gizlilik politikasını kabul etmelisiniz';
                }
                break;

            case 6: // Önizleme - validation gerekmez
                break;
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleNext = () => {
        if (validateStep(currentStep)) {
            setCurrentStep(prev => prev + 1);
        }
    };

    const handleCloseSuccessModal = () => {
        setShowSuccessModal(false);
        router.push('/account');
    };

    const handleGoToAccount = () => {
        setShowSuccessModal(false);
        router.push('/account');
    };

    const handleSubmit = async () => {
        if (!validateStep(6) || !user) return;

        setIsSubmitting(true);

        try {
            // Mock başvuru oluştur
            const newApplication: DealershipApplication = {
                id: Date.now(), // Basit ID üretimi
                userId: user.id,
                userName: `${user.firstName} ${user.lastName}`,
                userEmail: user.email,
                applicationData: formData,
                status: 'pending',
                submittedAt: new Date().toISOString()
            };

            // Mock data'ya ekle (gerçek uygulamada bu backend'e gönderilir)
            mockDealershipApplications.push(newApplication);

            // Simülasyon için bekleme
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Başarılı modalı göster
            setShowSuccessModal(true);
        } catch (error) {
            alert('Başvuru gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
        } finally {
            setIsSubmitting(false);
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user) {
        return null;
    }

    const steps = [
        { number: 1, title: 'Ürün Bilgileri', icon: Package },
        { number: 2, title: 'Şirket Bilgileri', icon: Building2 },
        { number: 3, title: 'Belgeler', icon: Upload },
        { number: 4, title: 'Yetkili Kişi', icon: User },
        { number: 5, title: 'Sözleşmeler', icon: FileText },
        { number: 6, title: 'Önizleme', icon: Eye }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Satıcı Ol
                    </h1>
                    <p className="text-gray-600">
                        Say Global platformunda satıcı olmak için aşağıdaki formu doldurun
                    </p>
                </div>

                {/* Progress Steps */}
                <div className="mb-8">
                    <div className="flex justify-center items-center">
                        <div className="flex items-center space-x-4">
                            {steps.map((step, index) => {
                                const Icon = step.icon;
                                const isActive = currentStep === step.number;
                                const isCompleted = currentStep > step.number;

                                return (
                                    <div key={step.number} className="flex items-center">
                                        <div className="flex flex-col items-center">
                                            <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-colors ${isCompleted
                                                ? 'bg-green-500 border-green-500 text-white'
                                                : isActive
                                                    ? 'bg-purple-600 border-purple-600 text-white'
                                                    : 'bg-white border-gray-300 text-gray-400'
                                                }`}>
                                                {isCompleted ? (
                                                    <CheckCircle className="h-6 w-6" />
                                                ) : (
                                                    <Icon className="h-6 w-6" />
                                                )}
                                            </div>
                                            <div className="mt-2 text-center">
                                                <p className={`text-xs font-medium whitespace-nowrap ${isActive ? 'text-purple-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                                                    }`}>
                                                    {step.title}
                                                </p>
                                            </div>
                                        </div>
                                        {index < steps.length - 1 && (
                                            <div className={`w-8 md:w-16 h-0.5 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'
                                                }`} />
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>

                {/* Form Content */}
                <motion.div
                    key={currentStep}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-white rounded-xl shadow-lg p-6 mb-6"
                >
                    {/* Step 1: Ürün Bilgileri */}
                    {currentStep === 1 && (
                        <div className="space-y-6">
                            <div className="flex items-center mb-6">
                                <Package className="h-6 w-6 text-purple-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Ürün Bilgileri</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Ana Ürün Kategorisi *
                                    </label>
                                    <select
                                        value={formData.mainProductCategory}
                                        onChange={(e) => handleInputChange('mainProductCategory', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-600 ${errors.mainProductCategory ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                    >
                                        <option value="">Kategori seçin</option>
                                        {productCategories.map(category => (
                                            <option key={category} value={category}>{category}</option>
                                        ))}
                                    </select>
                                    {errors.mainProductCategory && (
                                        <p className="text-red-500 text-sm mt-1">{errors.mainProductCategory}</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Tahmini Ürün Sayısı *
                                    </label>
                                    <select
                                        value={formData.estimatedProductCount}
                                        onChange={(e) => handleInputChange('estimatedProductCount', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-600 ${errors.estimatedProductCount ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                    >
                                        <option value="">Ürün sayısı seçin</option>
                                        {estimatedProductCountOptions.map(option => (
                                            <option key={option} value={option}>{option}</option>
                                        ))}
                                    </select>
                                    {errors.estimatedProductCount && (
                                        <p className="text-red-500 text-sm mt-1">{errors.estimatedProductCount}</p>
                                    )}
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Örnek Ürün Listesi / URL (İsteğe Bağlı)
                                </label>
                                <input
                                    type="url"
                                    value={formData.sampleProductListUrl || ''}
                                    onChange={(e) => handleInputChange('sampleProductListUrl', e.target.value)}
                                    placeholder="https://example.com/products"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                                />
                            </div>
                        </div>
                    )}

                    {/* Step 2: Şirket Bilgileri */}
                    {currentStep === 2 && (
                        <div className="space-y-6">
                            <div className="flex items-center mb-6">
                                <Building2 className="h-6 w-6 text-purple-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Şirket Bilgileri</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Şirket Adı / Unvanı *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.companyName}
                                        onChange={(e) => handleInputChange('companyName', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.companyName ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="Şirket adınızı girin"
                                    />
                                    {errors.companyName && (
                                        <p className="text-red-500 text-sm mt-1">{errors.companyName}</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Vergi Kimlik Numarası *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.taxNumber}
                                        onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.taxNumber ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="1234567890"
                                    />
                                    {errors.taxNumber && (
                                        <p className="text-red-500 text-sm mt-1">{errors.taxNumber}</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Vergi Dairesi *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.taxOffice}
                                        onChange={(e) => handleInputChange('taxOffice', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.taxOffice ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="İstanbul Vergi Dairesi"
                                    />
                                    {errors.taxOffice && (
                                        <p className="text-red-500 text-sm mt-1">{errors.taxOffice}</p>
                                    )}
                                </div>

                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Şirket Adresi *
                                    </label>
                                    <textarea
                                        value={formData.companyAddress}
                                        onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                                        rows={3}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.companyAddress ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="Tam şirket adresini girin"
                                    />
                                    {errors.companyAddress && (
                                        <p className="text-red-500 text-sm mt-1">{errors.companyAddress}</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Step 3: Belgeler */}
                    {currentStep === 3 && (
                        <div className="space-y-6">
                            <div className="flex items-center mb-6">
                                <Upload className="h-6 w-6 text-purple-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Belgeler</h2>
                            </div>

                            <div className="space-y-6">
                                {/* Vergi Levhası - Zorunlu */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Vergi Levhası *
                                        <span className="text-xs text-gray-500">(JPEG, PNG, PDF - Max 5MB)</span>
                                    </label>
                                    <div className={`border-2 border-dashed rounded-lg p-4 ${errors.taxCertificate ? 'border-red-500 bg-red-50' : 'border-gray-300'}`}>
                                        {formData.taxCertificate ? (
                                            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                                <div className="flex items-center">
                                                    <FileText className="h-5 w-5 text-green-600 mr-2" />
                                                    <span className="text-sm font-medium text-gray-700">
                                                        {formData.taxCertificate.name}
                                                    </span>
                                                    <span className="text-xs text-gray-500 ml-2">
                                                        ({(formData.taxCertificate.size / 1024 / 1024).toFixed(2)} MB)
                                                    </span>
                                                </div>
                                                <button
                                                    type="button"
                                                    onClick={() => handleFileRemove('taxCertificate')}
                                                    className="text-red-500 hover:text-red-700"
                                                >
                                                    <X className="h-4 w-4" />
                                                </button>
                                            </div>
                                        ) : (
                                            <label className="cursor-pointer block">
                                                <input
                                                    type="file"
                                                    accept=".jpg,.jpeg,.png,.pdf"
                                                    className="hidden"
                                                    onChange={(e) => {
                                                        const file = e.target.files?.[0];
                                                        if (file) handleFileUpload('taxCertificate', file, e.target);
                                                    }}
                                                />
                                                <div className="text-center">
                                                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                                    <p className="text-sm text-gray-600">
                                                        Vergi levhanızı yüklemek için tıklayın
                                                    </p>
                                                </div>
                                            </label>
                                        )}
                                    </div>
                                    {errors.taxCertificate && (
                                        <p className="text-red-500 text-sm mt-1">{errors.taxCertificate}</p>
                                    )}
                                </div>

                                {/* Ticaret Sicil Gazetesi - İsteğe Bağlı */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Ticaret Sicil Gazetesi (İsteğe Bağlı)
                                        <span className="text-xs text-gray-500">(JPEG, PNG, PDF - Max 5MB)</span>
                                    </label>
                                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                        {formData.tradeRegistryGazette ? (
                                            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                                <div className="flex items-center">
                                                    <FileText className="h-5 w-5 text-green-600 mr-2" />
                                                    <span className="text-sm font-medium text-gray-700">
                                                        {formData.tradeRegistryGazette.name}
                                                    </span>
                                                    <span className="text-xs text-gray-500 ml-2">
                                                        ({(formData.tradeRegistryGazette.size / 1024 / 1024).toFixed(2)} MB)
                                                    </span>
                                                </div>
                                                <button
                                                    type="button"
                                                    onClick={() => handleFileRemove('tradeRegistryGazette')}
                                                    className="text-red-500 hover:text-red-700"
                                                >
                                                    <X className="h-4 w-4" />
                                                </button>
                                            </div>
                                        ) : (
                                            <label className="cursor-pointer block">
                                                <input
                                                    type="file"
                                                    accept=".jpg,.jpeg,.png,.pdf"
                                                    className="hidden"
                                                    onChange={(e) => {
                                                        const file = e.target.files?.[0];
                                                        if (file) handleFileUpload('tradeRegistryGazette', file, e.target);
                                                    }}
                                                />
                                                <div className="text-center">
                                                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                                    <p className="text-sm text-gray-600">
                                                        Ticaret sicil gazetenizi yüklemek için tıklayın
                                                    </p>
                                                </div>
                                            </label>
                                        )}
                                    </div>
                                </div>

                                {/* İmza Sirküleri - İsteğe Bağlı */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        İmza Sirküleri (İsteğe Bağlı)
                                        <span className="text-xs text-gray-500">(JPEG, PNG, PDF - Max 5MB)</span>
                                    </label>
                                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                        {formData.signatureCircular ? (
                                            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                                <div className="flex items-center">
                                                    <FileText className="h-5 w-5 text-green-600 mr-2" />
                                                    <span className="text-sm font-medium text-gray-700">
                                                        {formData.signatureCircular.name}
                                                    </span>
                                                    <span className="text-xs text-gray-500 ml-2">
                                                        ({(formData.signatureCircular.size / 1024 / 1024).toFixed(2)} MB)
                                                    </span>
                                                </div>
                                                <button
                                                    type="button"
                                                    onClick={() => handleFileRemove('signatureCircular')}
                                                    className="text-red-500 hover:text-red-700"
                                                >
                                                    <X className="h-4 w-4" />
                                                </button>
                                            </div>
                                        ) : (
                                            <label className="cursor-pointer block">
                                                <input
                                                    type="file"
                                                    accept=".jpg,.jpeg,.png,.pdf"
                                                    className="hidden"
                                                    onChange={(e) => {
                                                        const file = e.target.files?.[0];
                                                        if (file) handleFileUpload('signatureCircular', file, e.target);
                                                    }}
                                                />
                                                <div className="text-center">
                                                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                                    <p className="text-sm text-gray-600">
                                                        İmza sirkülernizi yüklemek için tıklayın
                                                    </p>
                                                </div>
                                            </label>
                                        )}
                                    </div>
                                </div>

                                {/* Ek Belgeler */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Ek Belgeler (İsteğe Bağlı)
                                        <span className="text-xs text-gray-500">(JPEG, PNG, PDF - Max 5MB)</span>
                                    </label>

                                    {/* Yüklenmiş ek belgeler */}
                                    {formData.additionalDocuments && formData.additionalDocuments.length > 0 && (
                                        <div className="space-y-2 mb-4">
                                            {formData.additionalDocuments.map((file, index) => (
                                                <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                                    <div className="flex items-center">
                                                        <FileText className="h-5 w-5 text-green-600 mr-2" />
                                                        <span className="text-sm font-medium text-gray-700">
                                                            {file.name}
                                                        </span>
                                                        <span className="text-xs text-gray-500 ml-2">
                                                            ({(file.size / 1024 / 1024).toFixed(2)} MB)
                                                        </span>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        onClick={() => handleAdditionalFileRemove(index)}
                                                        className="text-red-500 hover:text-red-700"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    {/* Ek belge yükleme alanı */}
                                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                        <label className="cursor-pointer block">
                                            <input
                                                type="file"
                                                accept=".jpg,.jpeg,.png,.pdf"
                                                className="hidden"
                                                onChange={(e) => {
                                                    const file = e.target.files?.[0];
                                                    if (file) handleAdditionalFileAdd(file, e.target);
                                                }}
                                            />
                                            <div className="text-center">
                                                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                                <p className="text-sm text-gray-600">
                                                    Ek belge eklemek için tıklayın
                                                </p>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Bilgilendirme */}
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div className="flex items-start">
                                    <AlertCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
                                    <div>
                                        <h4 className="text-sm font-medium text-blue-900 mb-1">Belge Yükleme Bilgileri</h4>
                                        <p className="text-sm text-blue-700">
                                            • Vergi levhası zorunludur<br />
                                            • Diğer belgeler isteğe bağlıdır ancak başvuru sürecini hızlandırır<br />
                                            • Dosya boyutu maksimum 5MB olmalıdır<br />
                                            • Desteklenen formatlar: JPEG, PNG, PDF
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Step 4: Yetkili Kişi Bilgileri */}
                    {currentStep === 4 && (
                        <div className="space-y-6">
                            <div className="flex items-center mb-6">
                                <User className="h-6 w-6 text-purple-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Yetkili Kişi Bilgileri</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Yetkili Kişi Adı Soyadı *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.authorizedPersonName}
                                        onChange={(e) => handleInputChange('authorizedPersonName', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.authorizedPersonName ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="Yetkili kişinin adı soyadı"
                                    />
                                    {errors.authorizedPersonName && (
                                        <p className="text-red-500 text-sm mt-1">{errors.authorizedPersonName}</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        T.C. Kimlik Numarası *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.authorizedPersonTcId}
                                        onChange={(e) => handleInputChange('authorizedPersonTcId', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.authorizedPersonTcId ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="12345678901"
                                        maxLength={11}
                                    />
                                    {errors.authorizedPersonTcId && (
                                        <p className="text-red-500 text-sm mt-1">{errors.authorizedPersonTcId}</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Alternatif İletişim Numarası *
                                    </label>
                                    <input
                                        type="tel"
                                        value={formData.alternativeContactNumber}
                                        onChange={(e) => handleInputChange('alternativeContactNumber', e.target.value)}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black ${errors.alternativeContactNumber ? 'border-red-500' : 'border-gray-300'
                                            }`}
                                        placeholder="+90 ************"
                                    />
                                    {errors.alternativeContactNumber && (
                                        <p className="text-red-500 text-sm mt-1">{errors.alternativeContactNumber}</p>
                                    )}
                                </div>
                            </div>

                            {/* Hesap Bilgileri Gösterimi */}
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="text-sm font-medium text-gray-700 mb-3">Hesap Bilgileri (Otomatik Dolduruldu)</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div className="flex items-center">
                                        <User className="h-4 w-4 text-gray-400 mr-2" />
                                        <span className="text-gray-600">Ad Soyad:</span>
                                        <span className="ml-2 font-medium text-gray-700">{user.firstName} {user.lastName}</span>
                                    </div>
                                    <div className="flex items-center">
                                        <Mail className="h-4 w-4 text-gray-400 mr-2" />
                                        <span className="text-gray-600">E-posta:</span>
                                        <span className="ml-2 font-medium text-gray-700">{user.email}</span>
                                    </div>
                                    <div className="flex items-center">
                                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                                        <span className="text-gray-600">Telefon:</span>
                                        <span className="ml-2 font-medium text-gray-700">{user.phone}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Step 5: Sözleşme Onayları */}
                    {currentStep === 5 && (
                        <div className="space-y-6">
                            <div className="flex items-center mb-6">
                                <FileText className="h-6 w-6 text-purple-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Sözleşme ve Politika Onayları</h2>
                            </div>

                            <div className="space-y-4">
                                <div className={`border rounded-lg p-4 ${errors.userAgreementAccepted ? 'border-red-500 bg-red-50' : 'border-gray-200'
                                    }`}>
                                    <label className="flex items-start cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={formData.userAgreementAccepted}
                                            onChange={(e) => handleInputChange('userAgreementAccepted', e.target.checked)}
                                            className="mt-1 mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                        />
                                        <div>
                                            <span className="text-sm font-medium text-gray-900">
                                                Kullanıcı Sözleşmesi Onayı *
                                            </span>
                                            <p className="text-sm text-gray-600 mt-1">
                                                <a href="#" className="text-purple-600 hover:underline">Kullanıcı Sözleşmesi</a>'ni okudum ve kabul ediyorum.
                                            </p>
                                        </div>
                                    </label>
                                    {errors.userAgreementAccepted && (
                                        <p className="text-red-500 text-sm mt-2">{errors.userAgreementAccepted}</p>
                                    )}
                                </div>

                                <div className={`border rounded-lg p-4 ${errors.dealershipAgreementAccepted ? 'border-red-500 bg-red-50' : 'border-gray-200'
                                    }`}>
                                    <label className="flex items-start cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={formData.dealershipAgreementAccepted}
                                            onChange={(e) => handleInputChange('dealershipAgreementAccepted', e.target.checked)}
                                            className="mt-1 mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                        />
                                        <div>
                                            <span className="text-sm font-medium text-gray-900">
                                                Satıcı Sözleşmesi Onayı *
                                            </span>
                                            <p className="text-sm text-gray-600 mt-1">
                                                <a href="#" className="text-purple-600 hover:underline">Satıcı Sözleşmesi</a>'ni okudum ve kabul ediyorum.
                                            </p>
                                        </div>
                                    </label>
                                    {errors.dealershipAgreementAccepted && (
                                        <p className="text-red-500 text-sm mt-2">{errors.dealershipAgreementAccepted}</p>
                                    )}
                                </div>

                                <div className={`border rounded-lg p-4 ${errors.privacyPolicyAccepted ? 'border-red-500 bg-red-50' : 'border-gray-200'
                                    }`}>
                                    <label className="flex items-start cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={formData.privacyPolicyAccepted}
                                            onChange={(e) => handleInputChange('privacyPolicyAccepted', e.target.checked)}
                                            className="mt-1 mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                        />
                                        <div>
                                            <span className="text-sm font-medium text-gray-900">
                                                Gizlilik Politikası Onayı *
                                            </span>
                                            <p className="text-sm text-gray-600 mt-1">
                                                <a href="#" className="text-purple-600 hover:underline">Gizlilik Politikası</a>'nı okudum ve kabul ediyorum.
                                            </p>
                                        </div>
                                    </label>
                                    {errors.privacyPolicyAccepted && (
                                        <p className="text-red-500 text-sm mt-2">{errors.privacyPolicyAccepted}</p>
                                    )}
                                </div>
                            </div>

                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                                <div className="flex items-start">
                                    <AlertCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
                                    <div>
                                        <h4 className="text-sm font-medium text-blue-900 mb-1">Onay Süreci</h4>
                                        <p className="text-sm text-blue-700">
                                            Başvurunuz gönderildikten sonra yönetim ekibimiz tarafından incelenecektir.
                                            Onay süreci genellikle 3-5 iş günü sürmektedir. Hesabınızdan başvuru durumunuzu takip edebilirsiniz.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Step 6: Önizleme */}
                    {currentStep === 6 && (
                        <div className="space-y-6">
                            <div className="flex items-center mb-6">
                                <Eye className="h-6 w-6 text-purple-600 mr-3" />
                                <h2 className="text-xl font-semibold text-gray-900">Başvuru Önizlemesi</h2>
                            </div>

                            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-6 mb-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                                    Başvuru Bilgileriniz
                                </h3>
                                <p className="text-sm text-gray-600 mb-4">
                                    Aşağıdaki bilgileri kontrol edin. Gerekirse geri dönüp düzenleyebilirsiniz.
                                </p>
                            </div>

                            {/* Ürün Bilgileri Önizleme */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <Package className="h-5 w-5 text-purple-600 mr-2" />
                                    Ürün Bilgileri
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-gray-600 font-medium">Ana Ürün Kategorisi:</span>
                                        <p className="text-gray-900 mt-1">{formData.mainProductCategory || '-'}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">Tahmini Ürün Sayısı:</span>
                                        <p className="text-gray-900 mt-1">{formData.estimatedProductCount || '-'}</p>
                                    </div>
                                    {formData.sampleProductListUrl && (
                                        <div className="md:col-span-2">
                                            <span className="text-gray-600 font-medium">Örnek Ürün Listesi:</span>
                                            <p className="text-blue-600 mt-1 break-all">
                                                <a href={formData.sampleProductListUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                                    {formData.sampleProductListUrl}
                                                </a>
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Şirket Bilgileri Önizleme */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <Building2 className="h-5 w-5 text-purple-600 mr-2" />
                                    Şirket Bilgileri
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div className="md:col-span-2">
                                        <span className="text-gray-600 font-medium">Şirket Adı:</span>
                                        <p className="text-gray-900 mt-1">{formData.companyName || '-'}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">Vergi Kimlik No:</span>
                                        <p className="text-gray-900 mt-1">{formData.taxNumber || '-'}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">Vergi Dairesi:</span>
                                        <p className="text-gray-900 mt-1">{formData.taxOffice || '-'}</p>
                                    </div>
                                    <div className="md:col-span-2">
                                        <span className="text-gray-600 font-medium">Şirket Adresi:</span>
                                        <p className="text-gray-900 mt-1">{formData.companyAddress || '-'}</p>
                                    </div>
                                </div>
                            </div>

                            {/* Belgeler Önizleme */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <Upload className="h-5 w-5 text-purple-600 mr-2" />
                                    Yüklenen Belgeler
                                </h4>
                                <div className="space-y-3 text-sm">
                                    <div className="flex items-center">
                                        <FileText className={`h-4 w-4 mr-2 ${formData.taxCertificate ? 'text-green-600' : 'text-gray-300'}`} />
                                        <span className="text-gray-900">Vergi Levhası</span>
                                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${formData.taxCertificate ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                            {formData.taxCertificate ? `${formData.taxCertificate.name} (${(formData.taxCertificate.size / 1024 / 1024).toFixed(2)} MB)` : 'Yüklenmedi'}
                                        </span>
                                    </div>
                                    <div className="flex items-center">
                                        <FileText className={`h-4 w-4 mr-2 ${formData.tradeRegistryGazette ? 'text-green-600' : 'text-gray-300'}`} />
                                        <span className="text-gray-900">Ticaret Sicil Gazetesi</span>
                                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${formData.tradeRegistryGazette ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                            {formData.tradeRegistryGazette ? `${formData.tradeRegistryGazette.name} (${(formData.tradeRegistryGazette.size / 1024 / 1024).toFixed(2)} MB)` : 'Yüklenmedi'}
                                        </span>
                                    </div>
                                    <div className="flex items-center">
                                        <FileText className={`h-4 w-4 mr-2 ${formData.signatureCircular ? 'text-green-600' : 'text-gray-300'}`} />
                                        <span className="text-gray-900">İmza Sirküleri</span>
                                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${formData.signatureCircular ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                            {formData.signatureCircular ? `${formData.signatureCircular.name} (${(formData.signatureCircular.size / 1024 / 1024).toFixed(2)} MB)` : 'Yüklenmedi'}
                                        </span>
                                    </div>
                                    {formData.additionalDocuments && formData.additionalDocuments.length > 0 && (
                                        <div>
                                            <span className="text-gray-600 font-medium block mb-2">Ek Belgeler:</span>
                                            {formData.additionalDocuments.map((file, index) => (
                                                <div key={index} className="flex items-center ml-4 mb-1">
                                                    <FileText className="h-4 w-4 mr-2 text-green-600" />
                                                    <span className="text-gray-900">{file.name}</span>
                                                    <span className="ml-2 text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                                                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Yetkili Kişi Bilgileri Önizleme */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <User className="h-5 w-5 text-purple-600 mr-2" />
                                    Yetkili Kişi Bilgileri
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-gray-600 font-medium">Yetkili Kişi:</span>
                                        <p className="text-gray-900 mt-1">{formData.authorizedPersonName || '-'}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">T.C. Kimlik No:</span>
                                        <p className="text-gray-900 mt-1">{formData.authorizedPersonTcId || '-'}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">E-posta:</span>
                                        <p className="text-gray-900 mt-1">{user.email}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">Telefon:</span>
                                        <p className="text-gray-900 mt-1">{user.phone}</p>
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">Alternatif Telefon:</span>
                                        <p className="text-gray-900 mt-1">{formData.alternativeContactNumber || '-'}</p>
                                    </div>
                                </div>
                            </div>

                            {/* Sözleşme Onayları Önizleme */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <FileText className="h-5 w-5 text-purple-600 mr-2" />
                                    Sözleşme Onayları
                                </h4>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center">
                                        <CheckCircle className={`h-4 w-4 mr-2 ${formData.userAgreementAccepted ? 'text-green-600' : 'text-gray-300'}`} />
                                        <span className="text-gray-900">Kullanıcı Sözleşmesi</span>
                                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${formData.userAgreementAccepted ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                            {formData.userAgreementAccepted ? 'Kabul Edildi' : 'Kabul Edilmedi'}
                                        </span>
                                    </div>
                                    <div className="flex items-center">
                                        <CheckCircle className={`h-4 w-4 mr-2 ${formData.dealershipAgreementAccepted ? 'text-green-600' : 'text-gray-300'}`} />
                                        <span className="text-gray-900">Satıcı Sözleşmesi</span>
                                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${formData.dealershipAgreementAccepted ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                            {formData.dealershipAgreementAccepted ? 'Kabul Edildi' : 'Kabul Edilmedi'}
                                        </span>
                                    </div>
                                    <div className="flex items-center">
                                        <CheckCircle className={`h-4 w-4 mr-2 ${formData.privacyPolicyAccepted ? 'text-green-600' : 'text-gray-300'}`} />
                                        <span className="text-gray-900">Gizlilik Politikası</span>
                                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${formData.privacyPolicyAccepted ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                            {formData.privacyPolicyAccepted ? 'Kabul Edildi' : 'Kabul Edilmedi'}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Final Confirmation */}
                            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                                <div className="flex items-start">
                                    <CheckCircle className="h-6 w-6 text-green-600 mr-3 mt-0.5" />
                                    <div>
                                        <h4 className="text-lg font-semibold text-green-900 mb-2">Başvuru Hazır</h4>
                                        <p className="text-sm text-green-700 mb-3">
                                            Bilgileriniz kontrol edildi ve başvurunuz gönderilmeye hazır. "Başvuruyu Gönder" butonuna tıklayarak
                                            başvurunuzu tamamlayabilirsiniz.
                                        </p>
                                        <div className="flex items-center text-sm text-green-600">
                                            <AlertCircle className="h-4 w-4 mr-2" />
                                            <span>Başvuru gönderildikten sonra 1-3 iş günü içinde değerlendirilecektir.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </motion.div>

                {/* Navigation Buttons */}
                <div className="flex justify-between">
                    <button
                        onClick={() => setCurrentStep(prev => prev - 1)}
                        disabled={currentStep === 1}
                        className={`flex items-center px-6 py-3 rounded-lg font-medium transition-colors ${currentStep === 1
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                    >
                        Geri
                    </button>

                    {currentStep < 6 ? (
                        <button
                            onClick={handleNext}
                            className="flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
                        >
                            İleri
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </button>
                    ) : (
                        <button
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                            className="flex items-center px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isSubmitting ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Gönderiliyor...
                                </>
                            ) : (
                                <>
                                    Başvuruyu Gönder
                                    <CheckCircle className="ml-2 h-4 w-4" />
                                </>
                            )}
                        </button>
                    )}
                </div>
            </div>

            {/* Success Modal */}
            <DealerApplicationSuccessModal
                isOpen={showSuccessModal}
                onClose={handleCloseSuccessModal}
                onGoToAccount={handleGoToAccount}
            />
        </div>
    );
};

export default BecomeDealerPage;