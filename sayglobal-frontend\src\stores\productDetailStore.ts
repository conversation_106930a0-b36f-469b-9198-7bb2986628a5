import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ProductDetailResponse } from '@/types';

// 🎯 Product Detail Store State
interface ProductDetailState {
    // Modal state
    isModalOpen: boolean;
    selectedProductId: number | null;
    selectedVariantIndex: number;
    currentImageIndex: number;

    // Cache state
    cachedProducts: Map<number, ProductDetailResponse>;
    lastFetchTime: Map<number, number>;

    // UI state
    isLoading: boolean;
    error: string | null;

    // Prefetch queue
    prefetchQueue: Set<number>;
}

// 🎯 Product Detail Store Actions
interface ProductDetailActions {
    // Modal actions
    openModal: (productId: number) => void;
    closeModal: () => void;

    // Navigation actions
    setSelectedVariant: (index: number) => void;
    setCurrentImage: (index: number) => void;
    nextImage: () => void;
    prevImage: () => void;

    // Cache actions
    setCachedProduct: (productId: number, data: ProductDetailResponse) => void;
    getCachedProduct: (productId: number) => ProductDetailResponse | null;
    isCacheValid: (productId: number, maxAge?: number) => boolean;
    clearCache: () => void;
    clearProductCache: (productId: number) => void;

    // Prefetch actions
    addToPrefetchQueue: (productId: number) => void;
    removeFromPrefetchQueue: (productId: number) => void;
    clearPrefetchQueue: () => void;

    // State actions
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    reset: () => void;
}

type ProductDetailStore = ProductDetailState & ProductDetailActions;

// 🏪 Initial state
const initialState: ProductDetailState = {
    isModalOpen: false,
    selectedProductId: null,
    selectedVariantIndex: 0,
    currentImageIndex: 0,
    cachedProducts: new Map(),
    lastFetchTime: new Map(),
    isLoading: false,
    error: null,
    prefetchQueue: new Set(),
};

// 🐻 Create store
export const useProductDetailStore = create<ProductDetailStore>()(
    devtools(
        (set, get) => ({
            // Initial state
            ...initialState,

            // Modal actions
            openModal: (productId: number) => {
                set({
                    isModalOpen: true,
                    selectedProductId: productId,
                    selectedVariantIndex: 0,
                    currentImageIndex: 0,
                    error: null,
                }, false, 'productDetail/openModal');
            },

            closeModal: () => {
                set({
                    isModalOpen: false,
                    selectedProductId: null,
                    selectedVariantIndex: 0,
                    currentImageIndex: 0,
                    error: null,
                }, false, 'productDetail/closeModal');
            },

            // Navigation actions
            setSelectedVariant: (index: number) => {
                set({
                    selectedVariantIndex: index,
                    currentImageIndex: 0, // Reset image index when variant changes
                }, false, 'productDetail/setSelectedVariant');
            },

            setCurrentImage: (index: number) => {
                set({
                    currentImageIndex: index,
                }, false, 'productDetail/setCurrentImage');
            },

            nextImage: () => {
                const { selectedProductId, selectedVariantIndex, currentImageIndex, cachedProducts } = get();
                if (!selectedProductId) return;

                const product = cachedProducts.get(selectedProductId);
                if (!product || !product.variants[selectedVariantIndex]) return;

                const imageCount = product.variants[selectedVariantIndex].images.length;
                if (imageCount > 0) {
                    const nextIndex = (currentImageIndex + 1) % imageCount;
                    set({
                        currentImageIndex: nextIndex,
                    }, false, 'productDetail/nextImage');
                }
            },

            prevImage: () => {
                const { selectedProductId, selectedVariantIndex, currentImageIndex, cachedProducts } = get();
                if (!selectedProductId) return;

                const product = cachedProducts.get(selectedProductId);
                if (!product || !product.variants[selectedVariantIndex]) return;

                const imageCount = product.variants[selectedVariantIndex].images.length;
                if (imageCount > 0) {
                    const prevIndex = (currentImageIndex - 1 + imageCount) % imageCount;
                    set({
                        currentImageIndex: prevIndex,
                    }, false, 'productDetail/prevImage');
                }
            },

            // Cache actions
            setCachedProduct: (productId: number, data: ProductDetailResponse) => {
                const { cachedProducts, lastFetchTime } = get();
                const newCachedProducts = new Map(cachedProducts);
                const newLastFetchTime = new Map(lastFetchTime);

                newCachedProducts.set(productId, data);
                newLastFetchTime.set(productId, Date.now());

                set({
                    cachedProducts: newCachedProducts,
                    lastFetchTime: newLastFetchTime,
                }, false, 'productDetail/setCachedProduct');
            },

            getCachedProduct: (productId: number) => {
                const { cachedProducts } = get();
                return cachedProducts.get(productId) || null;
            },

            isCacheValid: (productId: number, maxAge: number = 5 * 60 * 1000) => { // 5 minutes default
                const { lastFetchTime } = get();
                const fetchTime = lastFetchTime.get(productId);
                if (!fetchTime) return false;
                return (Date.now() - fetchTime) < maxAge;
            },

            clearCache: () => {
                set({
                    cachedProducts: new Map(),
                    lastFetchTime: new Map(),
                }, false, 'productDetail/clearCache');
            },

            clearProductCache: (productId: number) => {
                const { cachedProducts, lastFetchTime } = get();
                const newCachedProducts = new Map(cachedProducts);
                const newLastFetchTime = new Map(lastFetchTime);

                newCachedProducts.delete(productId);
                newLastFetchTime.delete(productId);

                set({
                    cachedProducts: newCachedProducts,
                    lastFetchTime: newLastFetchTime,
                }, false, 'productDetail/clearProductCache');
            },

            // Prefetch actions
            addToPrefetchQueue: (productId: number) => {
                const { prefetchQueue } = get();
                const newPrefetchQueue = new Set(prefetchQueue);
                newPrefetchQueue.add(productId);

                set({
                    prefetchQueue: newPrefetchQueue,
                }, false, 'productDetail/addToPrefetchQueue');
            },

            removeFromPrefetchQueue: (productId: number) => {
                const { prefetchQueue } = get();
                const newPrefetchQueue = new Set(prefetchQueue);
                newPrefetchQueue.delete(productId);

                set({
                    prefetchQueue: newPrefetchQueue,
                }, false, 'productDetail/removeFromPrefetchQueue');
            },

            clearPrefetchQueue: () => {
                set({
                    prefetchQueue: new Set(),
                }, false, 'productDetail/clearPrefetchQueue');
            },

            // State actions
            setLoading: (loading: boolean) => {
                set({
                    isLoading: loading,
                }, false, 'productDetail/setLoading');
            },

            setError: (error: string | null) => {
                set({
                    error,
                }, false, 'productDetail/setError');
            },

            reset: () => {
                set({
                    ...initialState,
                    cachedProducts: new Map(), // Keep cache on reset
                    lastFetchTime: new Map(),
                }, false, 'productDetail/reset');
            },
        }),
        {
            name: 'product-detail-store',
            enabled: process.env.NODE_ENV === 'development',
        }
    )
);

// 🔧 Selector hooks for performance optimization
export const useProductDetailModal = () =>
    useProductDetailStore((state) => state.isModalOpen);

export const useSelectedProductId = () =>
    useProductDetailStore((state) => state.selectedProductId);

export const useSelectedVariantIndex = () =>
    useProductDetailStore((state) => state.selectedVariantIndex);

export const useCurrentImageIndex = () =>
    useProductDetailStore((state) => state.currentImageIndex);

export const useProductDetailLoading = () =>
    useProductDetailStore((state) => state.isLoading);

export const useProductDetailError = () =>
    useProductDetailStore((state) => state.error);

export const useCachedProduct = (productId: number | null) =>
    useProductDetailStore((state) =>
        productId ? state.getCachedProduct(productId) : null
    );

// 🎯 Action hooks - Stable reference to prevent infinite loops
export const useProductDetailActions = () => {
    const openModal = useProductDetailStore((state) => state.openModal);
    const closeModal = useProductDetailStore((state) => state.closeModal);
    const setSelectedVariant = useProductDetailStore((state) => state.setSelectedVariant);
    const setCurrentImage = useProductDetailStore((state) => state.setCurrentImage);
    const nextImage = useProductDetailStore((state) => state.nextImage);
    const prevImage = useProductDetailStore((state) => state.prevImage);
    const setCachedProduct = useProductDetailStore((state) => state.setCachedProduct);
    const getCachedProduct = useProductDetailStore((state) => state.getCachedProduct);
    const isCacheValid = useProductDetailStore((state) => state.isCacheValid);
    const clearCache = useProductDetailStore((state) => state.clearCache);
    const clearProductCache = useProductDetailStore((state) => state.clearProductCache);
    const addToPrefetchQueue = useProductDetailStore((state) => state.addToPrefetchQueue);
    const removeFromPrefetchQueue = useProductDetailStore((state) => state.removeFromPrefetchQueue);
    const clearPrefetchQueue = useProductDetailStore((state) => state.clearPrefetchQueue);
    const setLoading = useProductDetailStore((state) => state.setLoading);
    const setError = useProductDetailStore((state) => state.setError);
    const reset = useProductDetailStore((state) => state.reset);

    return {
        openModal,
        closeModal,
        setSelectedVariant,
        setCurrentImage,
        nextImage,
        prevImage,
        setCachedProduct,
        getCachedProduct,
        isCacheValid,
        clearCache,
        clearProductCache,
        addToPrefetchQueue,
        removeFromPrefetchQueue,
        clearPrefetchQueue,
        setLoading,
        setError,
        reset,
    };
};
