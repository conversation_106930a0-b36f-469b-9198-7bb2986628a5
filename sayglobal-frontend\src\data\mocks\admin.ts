import { AdminStats, AdminUser, AdminOrder, AdminProduct, MembershipLevelIds } from '@/types';

// Admin istatistikleri mock verisi
export const adminStats: AdminStats = {
    totalUsers: 1247,
    totalOrders: 2856,
    totalRevenue: 185420.75,
    totalProducts: 156,
    newUsersThisMonth: 87,
    ordersThisMonth: 234,
    revenueThisMonth: 18650.50,
    monthlyStats: [
        { month: 'Ocak', users: 65, orders: 180, revenue: 12450.75, products: 145 },
        { month: 'Şubat', users: 78, orders: 210, revenue: 15280.50, products: 148 },
        { month: 'Mart', users: 92, orders: 245, revenue: 18320.25, products: 152 },
        { month: 'Nisan', users: 68, orders: 190, revenue: 14680.75, products: 154 },
        { month: 'Mayıs', users: 85, orders: 220, revenue: 16850.00, products: 155 },
        { month: 'Haziran', users: 87, orders: 234, revenue: 18650.50, products: 156 }
    ]
};

// Ad<PERSON> kull<PERSON>ıları mock verisi
export const adminUsers: AdminUser[] = [
    {
        id: 1,
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '+90 ************',
        role: 'admin',
        membershipLevel: MembershipLevelIds.None,
        joinDate: '2023-01-01',
        isActive: true,
        lastLogin: '2024-06-15T10:30:00',
        totalOrders: 0,
        totalSpent: 0
    },
    {
        id: 2,
        firstName: 'Distribütör',
        lastName: 'Ahmet',
        email: '<EMAIL>',
        phone: '+90 ************',
        role: 'customer',
        membershipLevel: MembershipLevelIds.Gumus,
        joinDate: '2023-06-15',
        isActive: true,
        lastLogin: '2024-06-14T16:45:00',
        totalOrders: 15,
        totalSpent: 4250.75
    },
    {
        id: 3,
        firstName: 'Müşteri',
        lastName: 'Ali',
        email: '<EMAIL>',
        phone: '+90 ************',
        role: 'customer',
        membershipLevel: MembershipLevelIds.None,
        joinDate: '2024-01-10',
        isActive: true,
        lastLogin: '2024-06-15T09:20:00',
        totalOrders: 8,
        totalSpent: 1650.25
    },
    {
        id: 4,
        firstName: 'Zeynep',
        lastName: 'Kaya',
        email: '<EMAIL>',
        phone: '+90 ************',
        role: 'customer',
        membershipLevel: MembershipLevelIds.Bronz,
        joinDate: '2024-02-15',
        isActive: true,
        lastLogin: '2024-06-13T14:30:00',
        totalOrders: 12,
        totalSpent: 2850.00
    },
    {
        id: 5,
        firstName: 'Mehmet',
        lastName: 'Demir',
        email: '<EMAIL>',
        phone: '+90 ************',
        role: 'dealership',
        membershipLevel: MembershipLevelIds.Bronz,
        joinDate: '2023-09-20',
        isActive: true,
        lastLogin: '2024-06-12T11:15:00',
        totalOrders: 25,
        totalSpent: 6750.50
    },
    {
        id: 6,
        firstName: 'Ayşe',
        lastName: 'Yılmaz',
        email: '<EMAIL>',
        phone: '+90 ************',
        role: 'customer',
        membershipLevel: MembershipLevelIds.None,
        joinDate: '2024-03-08',
        isActive: false,
        lastLogin: '2024-05-20T16:45:00',
        totalOrders: 3,
        totalSpent: 450.75
    }
];

// Admin siparişleri mock verisi
export const adminOrders: AdminOrder[] = [
    {
        id: 'ORD-2024-1001',
        userId: 3,
        userName: 'Müşteri Ali',
        userEmail: '<EMAIL>',
        orderDate: '2024-06-15T08:30:00',
        status: 'processing',
        total: 1245.50,
        itemCount: 3,
        shippingMethod: 'Hızlı Kargo',
        paymentMethod: 'Kredi Kartı'
    },
    {
        id: 'ORD-2024-1000',
        userId: 4,
        userName: 'Zeynep Kaya',
        userEmail: '<EMAIL>',
        orderDate: '2024-06-14T16:20:00',
        status: 'shipped',
        total: 890.25,
        itemCount: 2,
        shippingMethod: 'Standart Kargo',
        paymentMethod: 'Kredi Kartı'
    },
    {
        id: 'ORD-2024-0999',
        userId: 5,
        userName: 'Mehmet Demir',
        userEmail: '<EMAIL>',
        orderDate: '2024-06-14T12:15:00',
        status: 'delivered',
        total: 2150.75,
        itemCount: 5,
        shippingMethod: 'Express Kargo',
        paymentMethod: 'Havale/EFT'
    },
    {
        id: 'ORD-2024-0998',
        userId: 2,
        userName: 'Distribütör Ahmet',
        userEmail: '<EMAIL>',
        orderDate: '2024-06-13T14:45:00',
        status: 'delivered',
        total: 1650.00,
        itemCount: 4,
        shippingMethod: 'Hızlı Kargo',
        paymentMethod: 'Kredi Kartı'
    },
    {
        id: 'ORD-2024-0997',
        userId: 6,
        userName: 'Ayşe Yılmaz',
        userEmail: '<EMAIL>',
        orderDate: '2024-06-12T10:30:00',
        status: 'cancelled',
        total: 450.75,
        itemCount: 1,
        shippingMethod: 'Standart Kargo',
        paymentMethod: 'Kredi Kartı'
    }
];

// Admin ürünleri mock verisi (ilk 10 ürün örneği)
export const adminProducts: AdminProduct[] = [
    {
        id: 1,
        title: 'Premium Collagen Plus',
        price: 299.99,
        stock: 85,
        category: 'Takviye Ürünler',
        brand: 'Say Global',
        isActive: true,
        totalSold: 156,
        createdAt: '2023-01-15',
        updatedAt: '2024-06-10'
    },
    {
        id: 2,
        title: 'Vitamin D3 + K2',
        price: 189.99,
        stock: 120,
        category: 'Vitaminler',
        brand: 'Say Global',
        isActive: true,
        totalSold: 203,
        createdAt: '2023-02-10',
        updatedAt: '2024-06-08'
    },
    {
        id: 3,
        title: 'Omega-3 Fish Oil',
        price: 249.99,
        stock: 65,
        category: 'Takviye Ürünler',
        brand: 'Say Global',
        isActive: true,
        totalSold: 128,
        createdAt: '2023-03-05',
        updatedAt: '2024-06-12'
    },
    {
        id: 4,
        title: 'Hyaluronic Acid Serum',
        price: 159.99,
        stock: 0,
        category: 'Cilt Bakımı',
        brand: 'Say Global',
        isActive: false,
        totalSold: 89,
        createdAt: '2023-04-20',
        updatedAt: '2024-05-15'
    },
    {
        id: 5,
        title: 'Probiotics Advanced',
        price: 219.99,
        stock: 95,
        category: 'Takviye Ürünler',
        brand: 'Say Global',
        isActive: true,
        totalSold: 142,
        createdAt: '2023-05-12',
        updatedAt: '2024-06-05'
    },
    {
        id: 6,
        title: 'Biotin Hair Growth',
        price: 179.99,
        stock: 110,
        category: 'Saç Bakımı',
        brand: 'Say Global',
        isActive: true,
        totalSold: 175,
        createdAt: '2023-06-08',
        updatedAt: '2024-06-09'
    },
    {
        id: 7,
        title: 'Magnesium Glycinate',
        price: 139.99,
        stock: 78,
        category: 'Mineraller',
        brand: 'Say Global',
        isActive: true,
        totalSold: 96,
        createdAt: '2023-07-15',
        updatedAt: '2024-06-11'
    },
    {
        id: 8,
        title: 'Green Tea Extract',
        price: 129.99,
        stock: 150,
        category: 'Antioksidanlar',
        brand: 'Say Global',
        isActive: true,
        totalSold: 234,
        createdAt: '2023-08-22',
        updatedAt: '2024-06-07'
    },
    {
        id: 9,
        title: 'Zinc Immune Support',
        price: 99.99,
        stock: 200,
        category: 'Mineraller',
        brand: 'Say Global',
        isActive: true,
        totalSold: 187,
        createdAt: '2023-09-10',
        updatedAt: '2024-06-06'
    },
    {
        id: 10,
        title: 'Turmeric Curcumin',
        price: 169.99,
        stock: 88,
        category: 'Antioksidanlar',
        brand: 'Say Global',
        isActive: true,
        totalSold: 152,
        createdAt: '2023-10-05',
        updatedAt: '2024-06-13'
    }
]; 