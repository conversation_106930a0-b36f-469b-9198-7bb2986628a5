import { useQuery, useMutation, useQueryClient, useQueries } from '@tanstack/react-query';
import { productService } from '@/services/api';
import { AdminProduct, MyProduct, DealershipProductDetail, ProductDetailResponse, ProductMessage, AdminProductStatistics, MyProductStatistics } from '@/types';
import { keepPreviousData } from '@tanstack/react-query';
import { useProductDetailStore } from '@/stores/productDetailStore';
import { useEffect, useMemo } from 'react';

// API'den admin ürün istatistiklerini çeker
export const useAdminProductStatistics = () => {
    return useQuery<AdminProductStatistics>({
        queryKey: ['adminProductStatistics'],
        queryFn: async () => {
            const response = await productService.getAdminProductStatistics();
            if (response.success) {
                return response.data;
            }
            throw new Error(response.error || 'İstatistikler alınamadı');
        },
        staleTime: 30 * 1000, // 30 saniye boyunca veriyi taze kabul et (daha kısa süre)
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
        refetchInterval: 60 * 1000, // 1 dakikada bir otomatik yenile
    });
};

// API'den kullanıcı ürün istatistiklerini çeker
export const useMyProductStatistics = () => {
    return useQuery<MyProductStatistics>({
        queryKey: ['myProductStatistics'],
        queryFn: async () => {
            const response = await productService.getMyProductStatistics();
            if (response.success) {
                return response.data;
            }
            throw new Error(response.error || 'İstatistikler alınamadı');
        },
        staleTime: 30 * 1000, // 30 saniye boyunca veriyi taze kabul et (daha kısa süre)
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
        refetchInterval: 60 * 1000, // 1 dakikada bir otomatik yenile
    });
};

// Basit ürün güncelleme mutation'ı (dealership için)
export const useUpdateSimpleProduct = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (productFormData: FormData) => {
            console.log('🔄 Hook: updateSimpleProduct mutation başlatılıyor...');
            return productService.updateSimpleProduct(productFormData);
        },
        onSuccess: (data) => {
            console.log('✅ Hook: Ürün başarıyla güncellendi. Cache temizleniyor...');
            console.log('📄 Hook: Success data:', data);
            queryClient.invalidateQueries({ queryKey: ['myProducts'] });
            queryClient.invalidateQueries({ queryKey: ['myProductStatistics'] });
            queryClient.invalidateQueries({ queryKey: ['products'] });
        },
        onError: (error: any) => {
            console.error('❌ Hook: Ürün güncelleme hatası:', error);
            console.error('❌ Hook: Error details:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
        }
    });
};

// Bu hook, sadece sayfanın üstündeki istatistik kartları için tüm ürünleri bir kere çeker. (DEPRECATED - useAdminProductStatistics kullanın)
export const useAdminProductStats = () => {
    return useQuery<AdminProduct[]>({
        queryKey: ['adminProductsStats'],
        queryFn: () => productService.getAdminProducts({ pageSize: 9999 }), // Çok büyük bir sayı ile tüm ürünleri çek
        staleTime: 5 * 60 * 1000, // 5 dakika boyunca veriyi taze kabul et
    });
};

// Bu hook, tablo için sayfalanmış ve aranmış veriyi çeker.
export const useAdminProducts = (page: number, searchTerm: string) => {
    return useQuery<AdminProduct[]>({
        queryKey: ['adminProducts', page, searchTerm],
        queryFn: () => productService.getAdminProducts({
            pageNumber: page,
            pageSize: 10,
            productName: searchTerm
        }),
        placeholderData: keepPreviousData, // Yeni veri yüklenirken eski veriyi göstermeye devam et
        staleTime: 30 * 1000, // 30 saniye boyunca taze kabul et (daha kısa süre)
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
        refetchInterval: 2 * 60 * 1000, // 2 dakikada bir otomatik yenile
    });
};

// Kullanıcıya ait ürünleri getiren hook
export const useMyProducts = (page: number, searchTerm: string, statusFilter?: number) => {
    return useQuery({
        queryKey: ['myProducts', page, searchTerm, statusFilter],
        queryFn: async () => {
            const params: any = {
                page: page,
                pageSize: 10,
                name: searchTerm
            };

            // Status filter varsa ekle
            if (statusFilter !== undefined && statusFilter !== -1) {
                params.status = statusFilter;
            }

            const response = await productService.getMyProducts(params);

            if (response.success) {
                return response.data;
            }
            throw new Error(response.error || 'Ürünler alınamadı');
        },
        placeholderData: keepPreviousData,
        staleTime: 30 * 1000,
        refetchOnWindowFocus: true,
        refetchOnMount: true,
        refetchInterval: 2 * 60 * 1000,
    });
};

// Dealership ürün detayını getiren hook
export const useDealershipProductDetail = (productId: number | null) => {
    return useQuery({
        queryKey: ['dealershipProductDetail', productId],
        queryFn: async () => {
            if (!productId) return null;

            const response = await productService.getDealershipProductDetail(productId);

            if (response.success) {
                return response.data;
            }
            throw new Error(response.error || 'Ürün detayı alınamadı');
        },
        enabled: !!productId,
        staleTime: 30 * 1000,
        refetchOnWindowFocus: true,
        refetchOnMount: true,
    });
};

// Bu hook, ürün silme işlemini yönetir.
export const useDeleteProduct = () => {
    const { refreshProductLists } = useProductCacheRefresh();

    return useMutation({
        mutationFn: (productId: number) => productService.deleteProduct(productId),
        onSuccess: (data, productId) => {
            console.log('✅ Ürün başarıyla silindi. Listeler güncelleniyor...');

            // Cache yenileme fonksiyonunu kullan
            refreshProductLists();

            console.log(`🔄 Ürün ${productId} silindi, cache'ler yenilendi`);
        },
        onError: (error) => {
            console.error('❌ Silme işlemi sırasında hata:', error);
            // Burada kullanıcıya bir bildirim (toast) gösterilebilir.
        }
    });
};

// Bu hook, ürün detayını çeker.
export const useProductDetail = (productId: number | null) => {
    return useQuery<ProductDetailResponse>({
        queryKey: ['productDetail', productId],
        queryFn: async () => {
            if (!productId) throw new Error('Product ID is required');
            const response = await productService.getProductDetail(productId);
            if (!response.success) {
                throw new Error(response.error || 'Ürün detayı alınamadı');
            }
            return response.data;
        },
        enabled: !!productId, // Sadece productId varsa sorguyu çalıştır
        staleTime: 5 * 60 * 1000, // 5 dakika boyunca veriyi taze kabul et
    });
};

// 🚀 Optimized Product Detail Hook with Zustand Cache
export const useOptimizedProductDetail = (productId: number | null) => {
    // Get individual actions to avoid dependency issues
    const getCachedProduct = useProductDetailStore((state) => state.getCachedProduct);
    const isCacheValidFn = useProductDetailStore((state) => state.isCacheValid);
    const setCachedProduct = useProductDetailStore((state) => state.setCachedProduct);
    const setLoading = useProductDetailStore((state) => state.setLoading);
    const setError = useProductDetailStore((state) => state.setError);

    const cachedProduct = useMemo(() =>
        productId ? getCachedProduct(productId) : null,
        [productId, getCachedProduct]
    );

    // Check if cache is valid
    const isCacheValid = useMemo(() =>
        productId ? isCacheValidFn(productId) : false,
        [productId, isCacheValidFn]
    );

    // TanStack Query with cache integration
    const query = useQuery<ProductDetailResponse>({
        queryKey: ['productDetail', productId],
        queryFn: async () => {
            if (!productId) throw new Error('Product ID is required');

            // Check Zustand cache first
            if (isCacheValid && cachedProduct) {
                console.log('🎯 Using cached product data for ID:', productId);
                return cachedProduct;
            }

            console.log('🌐 Fetching fresh product data for ID:', productId);
            setLoading(true);

            try {
                const response = await productService.getProductDetail(productId);
                if (!response.success) {
                    throw new Error(response.error || 'Ürün detayı alınamadı');
                }

                // Cache the result in Zustand
                setCachedProduct(productId, response.data);
                setError(null);

                return response.data;
            } catch (error: any) {
                setError(error.message);
                throw error;
            } finally {
                setLoading(false);
            }
        },
        enabled: !!productId,
        staleTime: 10 * 60 * 1000, // 10 minutes - longer since we have Zustand cache
        gcTime: 15 * 60 * 1000, // 15 minutes garbage collection
        retry: 2,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Return cached data immediately if available
        initialData: isCacheValid && cachedProduct ? cachedProduct : undefined,
        // Keep previous data during refetch
        placeholderData: keepPreviousData,
    });

    // Update Zustand cache when query succeeds
    useEffect(() => {
        if (query.data && productId && !query.isError) {
            setCachedProduct(productId, query.data);
        }
    }, [query.data, productId, query.isError, setCachedProduct]);

    return {
        ...query,
        // Additional cache info
        isCached: isCacheValid && !!cachedProduct,
        cacheAge: 0, // We can add this later if needed
    };
};

// 🚀 Prefetch Hook for Product Details
export const usePrefetchProductDetails = () => {
    const queryClient = useQueryClient();
    const isCacheValidFn = useProductDetailStore((state) => state.isCacheValid);
    const addToPrefetchQueue = useProductDetailStore((state) => state.addToPrefetchQueue);
    const removeFromPrefetchQueue = useProductDetailStore((state) => state.removeFromPrefetchQueue);
    const setCachedProduct = useProductDetailStore((state) => state.setCachedProduct);

    const prefetchProduct = async (productId: number) => {
        // Check if already cached and valid
        if (isCacheValidFn(productId)) {
            console.log('🎯 Product already cached, skipping prefetch:', productId);
            return;
        }

        console.log('🚀 Prefetching product:', productId);
        addToPrefetchQueue(productId);

        try {
            await queryClient.prefetchQuery({
                queryKey: ['productDetail', productId],
                queryFn: async () => {
                    const response = await productService.getProductDetail(productId);
                    if (!response.success) {
                        throw new Error(response.error || 'Ürün detayı alınamadı');
                    }

                    // Cache in Zustand
                    setCachedProduct(productId, response.data);
                    return response.data;
                },
                staleTime: 10 * 60 * 1000,
            });
        } catch (error) {
            console.warn('⚠️ Prefetch failed for product:', productId, error);
        } finally {
            removeFromPrefetchQueue(productId);
        }
    };

    const prefetchMultiple = async (productIds: number[]) => {
        const promises = productIds.map(id => prefetchProduct(id));
        await Promise.allSettled(promises);
    };

    return {
        prefetchProduct,
        prefetchMultiple,
        prefetchQueue: useProductDetailStore(state => state.prefetchQueue),
    };
};

// Ürün durumu güncelleme hook'u
export const useUpdateProductStatus = () => {
    const { refreshProductLists, refreshProductDetail } = useProductCacheRefresh();

    return useMutation({
        mutationFn: ({ productId, isApproved, message }: { productId: number; isApproved: boolean; message: string }) =>
            productService.updateProductStatus(productId, isApproved, message),
        onSuccess: (data, variables) => {
            console.log('✅ Ürün durumu başarıyla güncellendi. Cache temizleniyor...');

            // Cache yenileme fonksiyonlarını kullan
            refreshProductLists();
            refreshProductDetail(variables.productId);

            console.log(`🔄 Ürün ${variables.productId} için tüm cache'ler yenilendi`);
        },
        onError: (error) => {
            console.error('❌ Ürün durumu güncellenirken hata:', error);
        }
    });
};

// Ürün admin notunu getir
export const useProductMessage = (productId: number | null, enabled: boolean = true) => {
    return useQuery<ProductMessage | null>({
        queryKey: ['productMessage', productId],
        queryFn: async () => {
            if (!productId) return null;

            console.log('📝 Product message API çağrısı yapılıyor, productId:', productId);
            const response = await productService.getProductMessage(productId);
            if (response.success) {
                console.log('✅ Product message başarıyla alındı:', response.data);
                return response.data; // null olabilir (kayıt yok) veya data olabilir
            }

            // API hatası durumunda null döndür
            console.warn('Product message API hatası:', response.error);
            return null;
        },
        enabled: enabled && !!productId,
        staleTime: 1 * 60 * 1000, // 1 dakika boyunca taze kabul et (daha kısa süre)
        cacheTime: 5 * 60 * 1000, // 5 dakika cache'de tut
        retry: false, // Hata durumunda yeniden deneme
        refetchOnWindowFocus: true, // Pencere focus olduğunda yeniden çek
    });
};

// Cache yenileme fonksiyonları
export const useProductCacheRefresh = () => {
    const queryClient = useQueryClient();

    const refreshProductLists = () => {
        console.log('🔄 Ürün listeleri cache\'i yenileniyor...');
        queryClient.invalidateQueries({ queryKey: ['adminProducts'] });
        queryClient.invalidateQueries({ queryKey: ['adminProductStatistics'] });
    };

    const refreshProductDetail = (productId: number) => {
        console.log(`🔄 Ürün detayı cache'i yenileniyor (ID: ${productId})...`);
        queryClient.invalidateQueries({ queryKey: ['productDetail', productId] });
        queryClient.invalidateQueries({ queryKey: ['productMessage', productId] });
    };

    const refreshAllProductData = () => {
        console.log('🔄 Tüm ürün verileri cache\'i yenileniyor...');
        queryClient.invalidateQueries({ queryKey: ['adminProducts'] });
        queryClient.invalidateQueries({ queryKey: ['adminProductStatistics'] });
        queryClient.invalidateQueries({ queryKey: ['productDetail'] });
        queryClient.invalidateQueries({ queryKey: ['productMessage'] });
    };

    return {
        refreshProductLists,
        refreshProductDetail,
        refreshAllProductData
    };
};