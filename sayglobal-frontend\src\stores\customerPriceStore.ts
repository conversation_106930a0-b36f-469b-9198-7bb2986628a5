import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CustomerPriceState {
    isCustomerPrice: boolean;
    setIsCustomerPrice: (value: boolean) => void;
    resetCustomerPrice: () => void;
    _hasHydrated: boolean;
    setHasHydrated: (state: boolean) => void;
}

export const useCustomerPriceStore = create<CustomerPriceState>()(
    persist(
        (set) => ({
            isCustomerPrice: false,
            setIsCustomerPrice: (value: boolean) => set({ isCustomerPrice: value }),
            resetCustomerPrice: () => set({ isCustomerPrice: false }),
            _hasHydrated: false,
            setHasHydrated: (state: boolean) => set({ _hasHydrated: state }),
        }),
        {
            name: 'customer-price-store',
            onRehydrateStorage: () => (state) => {
                state?.setHasHydrated(true);
            },
        }
    )
);
