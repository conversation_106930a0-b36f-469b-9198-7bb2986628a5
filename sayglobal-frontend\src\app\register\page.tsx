'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useState } from "react";
import { useAuth } from "@/components/auth/AuthContext";
import { useRouter } from "next/navigation";
import { useModalActions } from "@/stores/modalStore";

export default function RegisterPage() {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        confirmPassword: "",
        phoneNumber: "",
        referansCode: "",
        role: "customer" as 'customer',
        terms: false
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [generalError, setGeneralError] = useState("");

    const { register, isLoading } = useAuth();
    const router = useRouter();
    const { openRegisterSuccessModal } = useModalActions();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        const checked = (e.target as HTMLInputElement).checked;

        setFormData(prev => ({
            ...prev,
            [name]: type === "checkbox" ? checked : value
        }));

        // Clear error for this field when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ""
            }));
        }
        setGeneralError("");
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.firstName) newErrors.firstName = "İsim gereklidir";
        if (!formData.lastName) newErrors.lastName = "Soyisim gereklidir";

        if (!formData.email) {
            newErrors.email = "E-posta gereklidir";
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = "Geçerli bir e-posta adresi giriniz";
        }

        if (!formData.password) {
            newErrors.password = "Şifre gereklidir";
        } else {
            // Backend şifre gereksinimlerini kontrol et
            const passwordErrors = [];
            if (formData.password.length < 6) passwordErrors.push("en az 6 karakter");
            if (!/[A-Z]/.test(formData.password)) passwordErrors.push("en az 1 büyük harf");
            if (!/[a-z]/.test(formData.password)) passwordErrors.push("en az 1 küçük harf");
            if (!/[0-9]/.test(formData.password)) passwordErrors.push("en az 1 rakam");
            if (!/[^a-zA-Z0-9]/.test(formData.password)) passwordErrors.push("en az 1 özel karakter (!@#$%^&*)");

            if (passwordErrors.length > 0) {
                newErrors.password = `Şifre ${passwordErrors.join(", ")} içermelidir`;
            }
        }

        if (formData.password !== formData.confirmPassword) {
            newErrors.confirmPassword = "Şifreler eşleşmiyor";
        }

        if (!formData.terms) {
            newErrors.terms = "Şartları ve koşulları kabul etmelisiniz";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setGeneralError("");

        if (validateForm()) {
            const success = await register({
                firstName: formData.firstName,
                lastName: formData.lastName,
                email: formData.email,
                password: formData.password,
                confirmPassword: formData.confirmPassword,
                phoneNumber: formData.phoneNumber || undefined,
                referansCode: formData.referansCode || undefined,
                role: formData.role
            });

            if (success) {
                // Kayıt başarılı, modal göster
                openRegisterSuccessModal();
            } else {
                setGeneralError("Kayıt işlemi başarısız! Lütfen bilgilerinizi kontrol edin ve tekrar deneyin.");
            }
        }
    };

    return (
        <div className="container mx-auto px-4 py-12">
            <div className="max-w-lg mx-auto">
                <motion.div
                    className="bg-white rounded-2xl shadow-lg overflow-hidden"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="p-8">
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold mb-2 text-gray-800">Hesap Oluştur</h1>
                            <p className="text-gray-700">
                                Say Global ailesine katılarak sağlıklı yaşama adım atın
                            </p>
                        </div>

                        {generalError && (
                            <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg"
                            >
                                {generalError}
                            </motion.div>
                        )}

                        <form onSubmit={handleSubmit}>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
                                <div>
                                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                                        İsim
                                    </label>
                                    <input
                                        id="firstName"
                                        name="firstName"
                                        type="text"
                                        value={formData.firstName}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        className={`w-full px-4 py-3 rounded-lg border ${errors.firstName ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`}
                                        placeholder="İsminizi girin"
                                    />
                                    {errors.firstName && <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>}
                                </div>

                                <div>
                                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Soyisim
                                    </label>
                                    <input
                                        id="lastName"
                                        name="lastName"
                                        type="text"
                                        value={formData.lastName}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        className={`w-full px-4 py-3 rounded-lg border ${errors.lastName ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`}
                                        placeholder="Soyisminizi girin"
                                    />
                                    {errors.lastName && <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>}
                                </div>
                            </div>

                            <div className="space-y-5">
                                <div>
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                        E-posta Adresi
                                    </label>
                                    <input
                                        id="email"
                                        name="email"
                                        type="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        className={`w-full px-4 py-3 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`}
                                        placeholder="<EMAIL>"
                                    />
                                    {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                                </div>

                                <div>
                                    <label htmlFor="referansCode" className="block text-sm font-medium text-gray-700 mb-1">
                                        Referans Kodu (Opsiyonel)
                                    </label>
                                    <input
                                        id="referansCode"
                                        name="referansCode"
                                        type="text"
                                        value={formData.referansCode}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"
                                        placeholder="Referans kodunu girin"
                                    />
                                </div>

                                <div>
                                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                        Telefon
                                    </label>
                                    <input
                                        id="phone"
                                        name="phoneNumber"
                                        type="tel"
                                        value={formData.phoneNumber}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100"
                                        placeholder="+90 ************"
                                    />
                                </div>

                                <div>
                                    <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                                        Hesap Türü
                                    </label>
                                    <select
                                        id="role"
                                        name="role"
                                        value={formData.role}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 disabled:bg-gray-100"
                                    >
                                        <option value="customer">Müşteri</option>
                                    </select>
                                    <p className="mt-1 text-xs text-gray-500">
                                        Distribütör olmak için üyelik seviyenizi yükseltmeniz, satıcı olmak için başvuru yapmanız gerekir.
                                    </p>
                                </div>

                                <div>
                                    <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                        Şifre
                                    </label>
                                    <input
                                        id="password"
                                        name="password"
                                        type="password"
                                        value={formData.password}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        autoComplete="new-password"
                                        className={`w-full px-4 py-3 rounded-lg border ${errors.password ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`}
                                        placeholder="En az 6 karakter"
                                    />
                                    {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
                                </div>

                                <div>
                                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                        Şifre Tekrar
                                    </label>
                                    <input
                                        id="confirmPassword"
                                        name="confirmPassword"
                                        type="password"
                                        value={formData.confirmPassword}
                                        onChange={handleChange}
                                        disabled={isLoading}
                                        autoComplete="new-password"
                                        className={`w-full px-4 py-3 rounded-lg border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-gray-800 placeholder-gray-500 disabled:bg-gray-100`}
                                        placeholder="Şifrenizi tekrar girin"
                                    />
                                    {errors.confirmPassword && <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>}
                                </div>

                                <div className="flex items-start">
                                    <div className="flex items-center h-5">
                                        <input
                                            id="terms"
                                            name="terms"
                                            type="checkbox"
                                            checked={formData.terms}
                                            onChange={handleChange}
                                            disabled={isLoading}
                                            className={`h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded ${errors.terms ? 'border-red-500' : ''} disabled:bg-gray-100`}
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="terms" className="font-medium text-gray-700">
                                            <span className="text-gray-700">
                                                <Link href="/terms" className="text-purple-600 hover:text-purple-800">
                                                    Şartlar ve koşulları
                                                </Link>{" "}
                                                okudum ve kabul ediyorum
                                            </span>
                                        </label>
                                        {errors.terms && <p className="mt-1 text-sm text-red-600">{errors.terms}</p>}
                                    </div>
                                </div>

                                <motion.button
                                    whileHover={{ scale: isLoading ? 1 : 1.02 }}
                                    whileTap={{ scale: isLoading ? 1 : 0.98 }}
                                    type="submit"
                                    disabled={isLoading}
                                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                >
                                    {isLoading ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Kayıt Oluşturuluyor...
                                        </>
                                    ) : (
                                        'Kayıt Ol'
                                    )}
                                </motion.button>
                            </div>
                        </form>

                        <div className="mt-8 text-center">
                            <p className="text-gray-600">
                                Zaten bir hesabınız var mı?{" "}
                                <Link
                                    href="/login"
                                    className="text-purple-600 font-medium hover:text-purple-800 transition"
                                >
                                    Giriş Yap
                                </Link>
                            </p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
} 