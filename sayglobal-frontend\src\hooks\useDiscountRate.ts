import { useQuery } from '@tanstack/react-query';
import { userService } from '@/services/api';
import { useAuth } from '@/components/auth/AuthContext';

// Discount rate response tipi
export interface DiscountRateResponse {
    discountRate: number | null;
}

// Query key factory
export const discountRateKeys = {
    all: ['discountRate'] as const,
    user: (userId: number | null) => [...discountRateKeys.all, 'user', userId] as const,
} as const;

// Kullanıcı indirim oranını getiren hook
export const useDiscountRate = () => {
    const { user, isAuthenticated } = useAuth();

    return useQuery<DiscountRateResponse>({
        queryKey: discountRateKeys.user(user?.id || null),
        queryFn: async () => {
            const response = await userService.getDiscountRate();
            if (!response.success) {
                throw new Error(response.error || 'İndirim oranı alınamadı');
            }
            return response.data;
        },
        enabled: isAuthenticated && !!user?.id, // Sadece giriş yapmış kullanıcılar için aktif
        staleTime: 5 * 60 * 1000, // 5 dakika boyunca taze kabul et
        refetchOnWindowFocus: false, // Pencere odaklandığında yeniden çekme
        refetchOnMount: true, // Component mount olduğunda yenile
        retry: (failureCount, error: any) => {
            // 401 hatası alırsak (giriş yapmamış) retry yapma
            if (error?.response?.status === 401) {
                return false;
            }
            return failureCount < 2;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    });
};
