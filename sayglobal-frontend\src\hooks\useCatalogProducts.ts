import { useQuery } from '@tanstack/react-query';
import { productService } from '@/services/api';
import { CatalogProductDetailResponse } from '@/types';
import { useCatalogProductDetailStore } from '@/stores/catalogProductDetailStore';
import { useEffect, useMemo } from 'react';

// 🚀 Optimized Catalog Product Detail Hook with Zustand Cache
export const useCatalogProductDetail = (productId: number | null) => {
    // Get store reference to avoid dependency issues
    const store = useCatalogProductDetailStore();

    const cachedProduct = useMemo(() =>
        productId ? store.getCachedProduct(productId) : null,
        [productId, store.getCachedProduct]
    );

    // Check if cache is valid
    const isCacheValid = useMemo(() =>
        productId ? store.isCacheValid(productId) : false,
        [productId, store.isCacheValid]
    );

    // TanStack Query with cache integration
    const query = useQuery<CatalogProductDetailResponse>({
        queryKey: ['catalogProductDetail', productId],
        queryFn: async () => {
            if (!productId) throw new Error('Product ID is required');

            // Check Zustand cache first
            if (isCacheValid && cachedProduct) {
                console.log('🎯 Using cached catalog product data for ID:', productId);
                return cachedProduct;
            }

            console.log('🌐 Fetching catalog product from API for ID:', productId);
            store.setLoading(true);

            try {
                const response = await productService.getCatalogProductDetail(productId);

                if (!response.success) {
                    throw new Error(response.error || 'Ürün detayı alınamadı');
                }

                // Cache the result
                store.setCachedProduct(productId, response.data);

                console.log('✅ Catalog product fetched and cached for ID:', productId);
                return response.data;
            } catch (error) {
                console.error('❌ Error fetching catalog product:', error);
                store.setError(error instanceof Error ? error.message : 'Ürün detayı alınamadı');
                throw error;
            } finally {
                store.setLoading(false);
            }
        },
        enabled: !!productId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        refetchOnWindowFocus: false,
        retry: (failureCount, error) => {
            // Don't retry on 404 errors
            if (error && 'status' in error && error.status === 404) {
                return false;
            }
            return failureCount < 2;
        },
    });

    // Update store when query data changes - with stability check
    useEffect(() => {
        if (query.data) {
            store.setProductData(query.data);
            store.setError(null);
        } else if (query.error) {
            store.setError(query.error instanceof Error ? query.error.message : 'Ürün detayı alınamadı');
            store.setProductData(null);
        }
    }, [query.data, query.error]); // Removed store functions from dependencies

    // Update loading state
    useEffect(() => {
        store.setLoading(query.isLoading);
    }, [query.isLoading]); // Removed store function from dependencies

    return {
        data: query.data,
        isLoading: query.isLoading,
        error: query.error,
        isError: query.isError,
        refetch: query.refetch,
        isFetching: query.isFetching,
        isSuccess: query.isSuccess,
    };
};

// 🎯 Hook for prefetching catalog product details
export const usePrefetchCatalogProductDetail = () => {
    const setCachedProduct = useCatalogProductDetailStore((state) => state.setCachedProduct);

    const prefetchProduct = async (productId: number) => {
        try {
            console.log('🔄 Prefetching catalog product:', productId);
            const response = await productService.getCatalogProductDetail(productId);

            if (response.success) {
                setCachedProduct(productId, response.data);
                console.log('✅ Catalog product prefetched and cached:', productId);
            }
        } catch (error) {
            console.warn('⚠️ Failed to prefetch catalog product:', productId, error);
        }
    };

    return { prefetchProduct };
};

// 🎯 Hook for getting cached catalog product without triggering fetch
export const useCachedCatalogProduct = (productId: number | null) => {
    return useCatalogProductDetailStore((state) =>
        productId ? state.getCachedProduct(productId) : null
    );
};

// 🎯 Hook for managing catalog product cache
export const useCatalogProductCache = () => {
    const clearCache = useCatalogProductDetailStore((state) => state.clearCache);
    const clearProductCache = useCatalogProductDetailStore((state) => state.clearProductCache);
    const setCachedProduct = useCatalogProductDetailStore((state) => state.setCachedProduct);
    const isCacheValid = useCatalogProductDetailStore((state) => state.isCacheValid);

    return {
        clearCache,
        clearProductCache,
        setCachedProduct,
        isCacheValid,
    };
};
