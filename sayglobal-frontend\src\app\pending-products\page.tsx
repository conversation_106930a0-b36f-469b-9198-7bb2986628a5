'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';

import {
    Package,
    Plus,
    Clock,
    CheckCircle,
    XCircle,
    Eye,
    Edit,

    ArrowLeft,
    Filter,
    Search,
    Calendar,
    User,
    Tag,
    ChevronLeft,
    ChevronRight,
    Loader2,
    RefreshCw
} from 'lucide-react';
import { MyProduct } from '@/types';
import { useMyProducts, useMyProductStatistics } from '@/hooks/useProducts';
import { useMyProductFilterStore } from '@/stores/myProductFilterStore';
import { useQueryClient } from '@tanstack/react-query';
import DealershipProductDetailModal from '@/components/DealershipProductDetailModal';

const MyProductsPage = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const queryClient = useQueryClient();
    const [page, setPage] = useState(1);
    const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
    const [showDetailModal, setShowDetailModal] = useState(false);

    // Zustand store'dan filter state'leri al
    const {
        searchTerm,
        statusFilter,
        setSearchTerm,
        setStatusFilter
    } = useMyProductFilterStore();

    // Debounced search term
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

    // Debounced search effect
    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setPage(1); // Reset to first page when searching
        }, 300);

        return () => {
            clearTimeout(handler);
        };
    }, [searchTerm]);

    // Erişim kontrolü
    useEffect(() => {
        if (!isLoading && !user) {
            router.push('/login');
            return;
        }

        if (!isLoading && user && user.role !== 'dealership' && user.role !== 'admin') {
            router.push('/login');
            return;
        }
    }, [user, isLoading, router]);

    // Sayfa yüklendiğinde istatistikleri ve ürün listesini yenile
    useEffect(() => {
        if (user && (user.role === 'dealership' || user.role === 'admin')) {
            queryClient.invalidateQueries({ queryKey: ['myProductStatistics'] });
            queryClient.invalidateQueries({ queryKey: ['myProducts'] });
            console.log('📊 Ürün yönetimi sayfası yüklendi, istatistikler ve ürün listesi yenileniyor...');
        }
    }, [user, queryClient]);

    // API'den ürünleri ve istatistikleri çek
    const { data: statisticsData, isLoading: statsLoading } = useMyProductStatistics();
    const { data: productsData, isLoading: productsLoading, isFetching } = useMyProducts(page, debouncedSearchTerm, statusFilter);

    // Helper fonksiyonlar
    const getStatusColor = (statusId: number) => {
        switch (statusId) {
            case 0: // Pending
                return 'bg-yellow-100 text-yellow-800';
            case 1: // Accepted
                return 'bg-green-100 text-green-800';
            case 2: // Rejected
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (statusId: number) => {
        switch (statusId) {
            case 0: // Pending
                return <Clock className="h-4 w-4" />;
            case 1: // Accepted
                return <CheckCircle className="h-4 w-4" />;
            case 2: // Rejected
                return <XCircle className="h-4 w-4" />;
            default:
                return <Package className="h-4 w-4" />;
        }
    };

    const getStatusText = (statusId: number) => {
        switch (statusId) {
            case 0:
                return 'Onay Bekliyor';
            case 1:
                return 'Onaylandı';
            case 2:
                return 'Reddedildi';
            default:
                return 'Bilinmiyor';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleShowDetail = (productId: number) => {
        setSelectedProductId(productId);
        setShowDetailModal(true);
    };

    const handleCloseDetailModal = () => {
        setSelectedProductId(null);
        setShowDetailModal(false);
    };

    // Pagination - Admin stilinde basit pagination
    const products = productsData?.data?.items || [];
    const totalCount = productsData?.data?.totalCount || 0;

    // API'den gelen istatistik verileri
    const totalProducts = statisticsData?.totalProductCount || 0;
    const pendingProducts = statisticsData?.pendingCount || 0;
    const approvedProducts = statisticsData?.approvedCount || 0;
    const rejectedProducts = statisticsData?.rejectedCount || 0;

    if (isLoading || statsLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || (user.role !== 'dealership' && user.role !== 'admin')) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/panel"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Panele Dön
                            </Link>
                        </div>
                        <Link
                            href="/add-product"
                            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
                        >
                            <Plus className="h-5 w-5" />
                            <span>Yeni Ürün Ekle</span>
                        </Link>
                    </div>

                    <div className="mt-4">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Ürün Yönetimi
                        </h1>
                        <p className="text-gray-600">
                            Eklediğiniz ürünlerin onay durumunu takip edin ve yeni ürünler ekleyin
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <Clock className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Onay Bekleyen</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {pendingProducts}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <CheckCircle className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Onaylanan</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {approvedProducts}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-red-100 rounded-lg">
                                <XCircle className="h-6 w-6 text-red-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Reddedilen</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {rejectedProducts}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Package className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Toplam</p>
                                <p className="text-2xl font-bold text-gray-900">{totalProducts}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                <input
                                    type="text"
                                    placeholder="Ürün ara..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                                />
                                {isFetching && (
                                    <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin" />
                                )}
                            </div>

                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(Number(e.target.value))}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600"
                            >
                                <option value={-1}>Tüm Durumlar</option>
                                <option value={0}>Onay Bekleyen</option>
                                <option value={1}>Onaylanan</option>
                                <option value={2}>Reddedilen</option>
                            </select>
                        </div>

                        <div className="text-sm text-gray-600">
                            {totalCount} ürün gösteriliyor
                        </div>
                    </div>
                </div>

                {/* Products Grid */}
                {productsLoading ? (
                    <div className="flex justify-center items-center py-12">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                        <span className="ml-2 text-gray-600">Ürünler yükleniyor...</span>
                    </div>
                ) : products.length === 0 ? (
                    <div className="text-center py-12">
                        <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz ürün yok</h3>
                        <p className="text-gray-600 mb-6">İlk ürününüzü ekleyerek başlayın</p>
                        <Link
                            href="/add-product"
                            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center space-x-2"
                        >
                            <Plus className="h-5 w-5" />
                            <span>Ürün Ekle</span>
                        </Link>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {products.map((product: MyProduct) => (
                            <motion.div
                                key={product.id}
                                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                {/* Product Image */}
                                <div className="aspect-w-16 aspect-h-12 bg-gray-200">
                                    {product.imageUrl ? (
                                        <img
                                            src={product.imageUrl}
                                            alt={product.name}
                                            className="w-full h-48 object-cover"
                                        />
                                    ) : (
                                        <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                                            <Package className="h-12 w-12 text-gray-400" />
                                        </div>
                                    )}
                                </div>

                                <div className="p-6">
                                    {/* Status Badge */}
                                    <div className="flex items-center justify-between mb-3">
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.statusId)}`}>
                                            {getStatusIcon(product.statusId)}
                                            <span className="ml-1">{getStatusText(product.statusId)}</span>
                                        </span>
                                        <span className="text-xs text-gray-500">{product.categoryName}</span>
                                    </div>

                                    {/* Product Info */}
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                        {product.name}
                                    </h3>
                                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                        {product.description}
                                    </p>

                                    {/* Price and Stock */}
                                    <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center space-x-2">
                                            <span className="text-lg font-bold text-gray-900">
                                                ₺{product.price.toFixed(2)}
                                            </span>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <Package className="h-4 w-4 text-blue-500" />
                                            <span className="text-sm font-medium text-blue-600">
                                                {product.totalStock} adet
                                            </span>
                                        </div>
                                    </div>

                                    {/* Submission Info */}
                                    <div className="text-xs text-gray-500 mb-4">
                                        <div className="flex items-center">
                                            <Calendar className="h-3 w-3 mr-1" />
                                            Oluşturuldu: {formatDate(product.createdAt)}
                                        </div>
                                        <div className="flex items-center mt-1">
                                            <User className="h-3 w-3 mr-1" />
                                            Marka: {product.brandName}
                                        </div>
                                    </div>

                                    {/* Actions */}
                                    <div className="flex space-x-2">
                                        <button
                                            onClick={() => handleShowDetail(product.id)}
                                            className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center"
                                        >
                                            <Eye className="h-4 w-4 mr-1" />
                                            Detay
                                        </button>
                                        {product.statusId === 0 && (
                                            <Link
                                                href={`/edit-product/${product.id}`}
                                                className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center"
                                            >
                                                <Edit className="h-4 w-4 mr-1" />
                                                Düzenle
                                            </Link>
                                        )}
                                    </div>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                )}

                {/* Pagination Controls - Admin stilinde */}
                <div className="bg-white rounded-lg shadow-sm mt-8">
                    <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <button
                            onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                            disabled={page === 1}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <ChevronLeft className="h-4 w-4 mr-2" />
                            Önceki
                        </button>
                        <span className="text-sm text-gray-700">
                            Sayfa <span className="font-bold">{page}</span>
                        </span>
                        <button
                            onClick={() => setPage(prev => products.length === 10 ? prev + 1 : prev)}
                            disabled={products.length < 10}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Sonraki
                            <ChevronRight className="h-4 w-4 ml-2" />
                        </button>
                    </div>
                </div>
            </div>

            {/* Detail Modal */}
            {selectedProductId && (
                <DealershipProductDetailModal
                    productId={selectedProductId}
                    isOpen={showDetailModal}
                    onClose={handleCloseDetailModal}
                />
            )}
        </div>
    );
};

export default MyProductsPage;