'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { useAdminUsers, useUserRoleStatistics, useUserCacheRefresh } from '@/hooks/useUsers';
import { useUserFilterStore } from '@/stores/userFilterStore';
import { AdminUser, UserRoleStatistics } from '@/types';
import {
    Users,
    Search,
    Filter,
    MoreVertical,
    Edit,
    Trash2,
    Eye,
    UserPlus,
    ArrowLeft,
    Shield,
    Crown,
    Briefcase,
    User as UserIcon,
    ChevronLeft,
    ChevronRight,
    Loader2,
    RefreshCw
} from 'lucide-react';
import Link from 'next/link';

const AdminUsersPage = () => {
    const { user, isLoading: authLoading } = useAuth();
    const router = useRouter();
    const queryClient = useQueryClient();
    const [page, setPage] = useState(1);
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

    const {
        searchTerm,
        roleFilter,
        statusFilter,
        setSearchTerm,
        setRoleFilter,
        setStatusFilter
    } = useUserFilterStore();

    useEffect(() => {
        if (!authLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, authLoading, router]);

    // Debounce search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setPage(1); // Reset to first page when search changes
        }, 500);

        return () => clearTimeout(timer);
    }, [searchTerm]);

    // Sayfa yüklendiğinde istatistikleri ve kullanıcı listesini yenile
    useEffect(() => {
        if (user && user.role === 'admin') {
            queryClient.invalidateQueries({ queryKey: ['userRoleStatistics'] });
            queryClient.invalidateQueries({ queryKey: ['adminUsers'] });
            console.log('📊 Kullanıcı yönetimi sayfası yüklendi, istatistikler ve kullanıcı listesi yenileniyor...');
        }
    }, [user, queryClient]);

    const { data: statisticsData, isLoading: statsLoading } = useUserRoleStatistics();
    const { data: paginatedUsers, isLoading: usersLoading, isFetching } = useAdminUsers(page, debouncedSearchTerm, roleFilter, statusFilter);

    // Cache yenileme fonksiyonları
    const { refreshUserLists } = useUserCacheRefresh();

    const users = paginatedUsers || [];
    const totalUsers = statisticsData?.totalUserCount || 0;
    const adminUsers = statisticsData?.adminCount || 0;
    const dealershipUsers = statisticsData?.dealershipCount || 0;
    const customerUsers = statisticsData?.customerCount || 0;

    const isLoading = authLoading || statsLoading || (usersLoading && !isFetching);

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }



    const getRoleIcon = (user: AdminUser) => {
        if (user.roleId === 1) {
            return <Shield className="h-4 w-4 text-red-600" />;
        } else if (user.roleId === 2) {
            return <Briefcase className="h-4 w-4 text-green-600" />;
        } else if (user.roleId === 3) {
            return <UserIcon className="h-4 w-4 text-blue-600" />;
        } else {
            return <UserIcon className="h-4 w-4 text-gray-600" />;
        }
    };

    const getRoleBadge = (user: AdminUser) => {
        if (user.roleId === 1) {
            return 'bg-red-100 text-red-800';
        } else if (user.roleId === 2) {
            return 'bg-green-100 text-green-800';
        } else if (user.roleId === 3) {
            return 'bg-blue-100 text-blue-800';
        } else {
            return 'bg-gray-100 text-gray-800';
        }
    };

    const getRoleText = (user: AdminUser) => {
        if (user.roleId === 1) {
            return 'Yönetici';
        } else if (user.roleId === 2) {
            return 'Satıcı';
        } else if (user.roleId === 3) {
            return 'Müşteri';
        } else {
            return 'Rol Yok';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR');
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    };



    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/admin"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Admin Paneli
                            </Link>
                            <span className="text-gray-300">/</span>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Kullanıcı Yönetimi
                            </h1>
                        </div>
                        <div className="flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg">
                            <Shield className="h-5 w-5 text-red-600" />
                            <span className="text-red-800 font-medium">Admin Erişimi</span>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Kullanıcı</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? (
                                        <Loader2 className="h-6 w-6 animate-spin inline" />
                                    ) : (
                                        totalUsers
                                    )}
                                </p>
                            </div>
                            <Users className="h-8 w-8 text-blue-600" />
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Yöneticiler</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? (
                                        <Loader2 className="h-6 w-6 animate-spin inline" />
                                    ) : (
                                        adminUsers
                                    )}
                                </p>
                            </div>
                            <Shield className="h-8 w-8 text-red-600" />
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Satıcılar</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? (
                                        <Loader2 className="h-6 w-6 animate-spin inline" />
                                    ) : (
                                        dealershipUsers
                                    )}
                                </p>
                            </div>
                            <Briefcase className="h-8 w-8 text-green-600" />
                        </div>
                    </motion.div>

                    <motion.div
                        className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Müşteriler</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {statsLoading ? (
                                        <Loader2 className="h-6 w-6 animate-spin inline" />
                                    ) : (
                                        customerUsers
                                    )}
                                </p>
                            </div>
                            <UserIcon className="h-8 w-8 text-blue-600" />
                        </div>
                    </motion.div>
                </div>

                {/* Filters */}
                <motion.div
                    className="bg-white rounded-xl shadow-lg p-6 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                >
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <Search className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                <input
                                    type="text"
                                    placeholder="Kullanıcı ara..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-black"
                                />
                                {isFetching && <Loader2 className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin" />}
                            </div>

                            <select
                                value={roleFilter}
                                onChange={(e) => setRoleFilter(Number(e.target.value))}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600"
                            >
                                <option value={0}>Tüm Roller</option>
                                <option value={1}>Yönetici</option>
                                <option value={2}>Satıcı</option>
                                <option value={3}>Müşteri</option>
                            </select>

                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(Number(e.target.value))}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-600"
                            >
                                <option value={0}>Tüm Durumlar</option>
                                <option value={1}>Aktif</option>
                                <option value={2}>Pasif</option>
                            </select>
                        </div>

                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => {
                                    console.log('🔄 Manuel cache yenileme başlatıldı...');
                                    refreshUserLists();
                                }}
                                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                                title="Verileri yenile"
                            >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Yenile
                            </button>
                            <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <UserPlus className="h-4 w-4 mr-2" />
                                Yeni Kullanıcı
                            </button>
                        </div>
                    </div>
                </motion.div>

                {/* Users Table */}
                <motion.div
                    className={`bg-white rounded-xl shadow-lg overflow-hidden transition-opacity ${isFetching ? 'opacity-70' : ''}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                >
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">
                            Kullanıcılar ({users.length})
                            {usersLoading && (
                                <Loader2 className="h-4 w-4 animate-spin inline ml-2" />
                            )}
                        </h3>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Kullanıcı
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Rol
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Katılım Tarihi
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Son Giriş
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Sipariş/Harcama
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Durum
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {users.map((user) => (
                                    <tr key={user.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 flex-shrink-0">
                                                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <span className="text-sm font-medium text-gray-700">
                                                            {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {user.firstName} {user.lastName}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {user.email}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadge(user)}`}>
                                                {getRoleIcon(user)}
                                                <span className="ml-1">{getRoleText(user)}</span>
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {formatDate(user.registeredAt)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('tr-TR') : 'Hiç giriş yok'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div>
                                                <div className="text-sm font-medium">{user.orderCount} sipariş</div>
                                                <div className="text-sm text-gray-500">{formatCurrency(user.totalSpent)}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.isActive
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                                }`}>
                                                {user.isActive ? 'Aktif' : 'Pasif'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex items-center space-x-2">
                                                <button className="text-blue-600 hover:text-blue-900">
                                                    <Eye className="h-4 w-4" />
                                                </button>
                                                <button className="text-green-600 hover:text-green-900">
                                                    <Edit className="h-4 w-4" />
                                                </button>
                                                <button className="text-red-600 hover:text-red-900">
                                                    <Trash2 className="h-4 w-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {users.length === 0 && !isFetching && (
                        <div className="px-6 py-12 text-center">
                            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Kullanıcı bulunamadı</h3>
                            <p className="text-gray-500">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
                        </div>
                    )}

                    {/* Pagination Controls */}
                    <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <button
                            onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                            disabled={page === 1}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <ChevronLeft className="h-4 w-4 mr-2" />
                            Önceki
                        </button>
                        <span className="text-sm text-gray-700">
                            Sayfa <span className="font-bold">{page}</span>
                        </span>
                        <button
                            onClick={() => setPage(prev => users.length === 10 ? prev + 1 : prev)}
                            disabled={users.length < 10}
                            className="flex items-center px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Sonraki
                            <ChevronRight className="h-4 w-4 ml-2" />
                        </button>
                    </div>
                </motion.div>


            </div>
        </div>
    );
};

export default AdminUsersPage; 