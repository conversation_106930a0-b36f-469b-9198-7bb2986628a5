'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, X, Users, ArrowRight } from 'lucide-react';

interface DealerApplicationSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    onGoToAccount: () => void;
}

const DealerApplicationSuccessModal: React.FC<DealerApplicationSuccessModalProps> = ({
    isOpen,
    onClose,
    onGoToAccount
}) => {
    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 z-50 flex items-center justify-center">
                    {/* Backdrop */}
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="absolute inset-0 bg-black/20 backdrop-blur-sm"
                        onClick={onClose}
                    />

                    {/* Modal */}
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9, y: -20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.9, y: -20 }}
                        transition={{ type: "spring", duration: 0.5 }}
                        className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
                    >
                        {/* Close Button */}
                        <button
                            onClick={onClose}
                            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors z-10"
                        >
                            <X className="h-5 w-5" />
                        </button>

                        {/* Success Animation Background */}
                        <div className="bg-gradient-to-br from-green-50 to-emerald-100 p-8 text-center">
                            {/* Animated Success Icon */}
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{
                                    type: "spring",
                                    stiffness: 200,
                                    damping: 10,
                                    delay: 0.2
                                }}
                                className="inline-flex items-center justify-center w-20 h-20 bg-green-500 rounded-full mb-6"
                            >
                                <CheckCircle className="h-10 w-10 text-white" />
                            </motion.div>

                            {/* Success Title */}
                            <motion.h2
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.4 }}
                                className="text-2xl font-bold text-gray-900 mb-3"
                            >
                                Başvurunuz Alındı! 🎉
                            </motion.h2>

                            {/* Success Message */}
                            <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5 }}
                                className="text-gray-600 mb-6 text-sm leading-relaxed"
                            >
                                Satıcı başvurunuz başarıyla gönderildi. Başvurunuz inceleme sürecine alınmış olup,
                                en kısa sürede size geri dönüş yapılacaktır.
                            </motion.p>

                            {/* Info Cards */}
                            <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.6 }}
                                className="space-y-3 mb-6"
                            >
                                <div className="flex items-center bg-white bg-opacity-60 rounded-lg p-3 text-left">
                                    <div className="bg-blue-100 p-2 rounded-full mr-3">
                                        <Users className="h-4 w-4 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-800">İnceleme Süreci</p>
                                        <p className="text-xs text-gray-600">Başvurunuz 1-3 iş günü içinde değerlendirilecektir</p>
                                    </div>
                                </div>

                                <div className="flex items-center bg-white bg-opacity-60 rounded-lg p-3 text-left">
                                    <div className="bg-purple-100 p-2 rounded-full mr-3">
                                        <ArrowRight className="h-4 w-4 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-800">Sonuç Bildirimi</p>
                                        <p className="text-xs text-gray-600">E-posta ve hesap paneli üzerinden bilgilendirileceksiniz</p>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Action Buttons */}
                            <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7 }}
                                className="flex flex-col space-y-3"
                            >
                                <button
                                    onClick={onGoToAccount}
                                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2"
                                >
                                    <Users className="h-4 w-4" />
                                    <span>Hesabıma Git</span>
                                </button>

                                <button
                                    onClick={onClose}
                                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors"
                                >
                                    Kapat
                                </button>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default DealerApplicationSuccessModal; 