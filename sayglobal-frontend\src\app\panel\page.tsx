'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { MembershipLevelIds } from '@/types';
import { distributorDashboard, teamMembers } from '@/data/mocks/distributor';
import {
    TrendingUp,
    Users,
    Star,
    Wallet,
    Target,
    Award,
    BarChart3,
    Calendar,
    UserCheck,
    Trophy
} from 'lucide-react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const DistributorPanel = () => {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const dashboard = distributorDashboard;

    // Erişim kontrolü
    useEffect(() => {
        if (!isLoading && (!user ||
            (user.role !== 'dealership' &&
                user.role !== 'admin' &&
                user.role !== 'customer'
            ))) {
            router.push('/login');
        }
    }, [user, isLoading, router]);

    // Kullanıcının ürün yönetimi erişimi var mı?
    const hasProductManagement = user?.role === 'dealership' || user?.role === 'admin';

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user ||
        (user.role !== 'dealership' &&
            user.role !== 'admin' &&
            user.role !== 'customer'
        )) {
        return null;
    }

    // Progress calculation for next level
    const totalPointsForNextLevel = dashboard.totalPoints + dashboard.nextLevelPoints;
    const progressPercentage = (dashboard.totalPoints / totalPointsForNextLevel) * 100;

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Kontrol Paneli
                    </h1>
                    <p className="text-gray-600">
                        Kazançlarınızı, ekibinizi ve performansınızı takip edin
                    </p>

                    {/* Rol durumu göstergesi */}
                    <div className="mt-4 flex items-center space-x-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' :
                            user.role === 'dealership' ? 'bg-green-100 text-green-800' :
                                'bg-blue-100 text-blue-800'
                            }`}>
                            {user.role === 'admin' ? 'Yönetici' :
                                user.role === 'dealership' ? 'Satıcı' : 'Müşteri'}
                        </span>

                        {!hasProductManagement && (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                Ürün Yönetimi: Erişim Yok
                            </span>
                        )}

                        {hasProductManagement && (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                Ürün Yönetimi: Aktif
                            </span>
                        )}
                    </div>


                </div>

                {/* Stats Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

                    {/* Toplam Kazanç */}
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Kazanç</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    ₺{dashboard.totalEarnings.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                                </p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-full">
                                <Wallet className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </div>

                    {/* Aylık Puan */}
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Aylık Puan</p>
                                <p className="text-2xl font-bold text-gray-900">{dashboard.monthlyPoints}</p>
                            </div>
                            <div className="bg-blue-100 p-3 rounded-full">
                                <Star className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </div>

                    {/* Ekip Sayısı */}
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Ekip Sayısı</p>
                                <p className="text-2xl font-bold text-gray-900">{dashboard.teamSize}</p>
                            </div>
                            <div className="bg-purple-100 p-3 rounded-full">
                                <Users className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </div>

                    {/* Aylık Bakiye */}
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Aylık Bakiye</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    ₺{dashboard.monthlyBalance.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                                </p>
                            </div>
                            <div className="bg-orange-100 p-3 rounded-full">
                                <TrendingUp className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Second Row Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">

                    {/* Aylık Aktiflik */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">Aylık Aktiflik</h3>
                            <UserCheck className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-gray-600">Bu Ay</span>
                                <span className="font-semibold text-gray-900">
                                    %{dashboard.monthlyActivityPercentage}
                                </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                                <div
                                    className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-300"
                                    style={{ width: `${dashboard.monthlyActivityPercentage}%` }}
                                ></div>
                            </div>
                            <p className="text-sm text-gray-500">
                                Minimum 75 puanlık sipariş ile aktif olunur
                            </p>
                        </div>
                    </div>

                    {/* Toplam Puanlar */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">Puanlarım</h3>
                            <Trophy className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-gray-600">Toplam Puan</span>
                                <span className="font-semibold text-gray-900">
                                    {dashboard.totalPoints.toLocaleString('tr-TR')}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-600">Organizasyon</span>
                                <span className="font-semibold text-gray-900">
                                    {dashboard.organizationPoints.toLocaleString('tr-TR')}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Seviye İlerlemesi */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">Seviye İlerlemesi</h3>
                            <Award className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-gray-600">Mevcut Seviye</span>
                                <span className="font-semibold text-gray-900">{dashboard.currentLevel}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                                <div
                                    className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-3 rounded-full transition-all duration-300"
                                    style={{ width: `${progressPercentage}%` }}
                                ></div>
                            </div>
                            <p className="text-sm text-gray-500">
                                {dashboard.nextLevel} seviyesi için {dashboard.nextLevelPoints} puan kaldı
                            </p>
                        </div>
                    </div>
                </div>

                {/* Charts and Team Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                    {/* Aylık Kazanç Grafiği */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-xl font-semibold text-gray-900">Aylık Kazanç Trendi</h3>
                            <BarChart3 className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="space-y-4">
                            {dashboard.monthlyEarnings.map((month, index) => (
                                <div key={index} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <span className="text-sm font-medium text-gray-700 w-16">
                                            {month.month}
                                        </span>
                                        <div className="flex-1 bg-gray-200 rounded-full h-2 w-32">
                                            <div
                                                className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full"
                                                style={{ width: `${(month.earnings / 1300) * 100}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-sm font-semibold text-gray-900">
                                            ₺{month.earnings.toFixed(2)}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                            %{month.activity} aktiflik
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Ekibim Tablosu */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-xl font-semibold text-gray-900">Ekibim</h3>
                            <Users className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="overflow-hidden">
                            <div className="space-y-3">
                                {teamMembers.slice(0, 5).map((member) => (
                                    <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                                                <span className="text-white font-semibold text-sm">
                                                    {member.firstName.charAt(0)}{member.lastName.charAt(0)}
                                                </span>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">
                                                    {member.firstName} {member.lastName}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    Seviye {member.level} • {member.points} puan
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${member.isActive
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                                }`}>
                                                {member.isActive ? 'Aktif' : 'Pasif'}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            {teamMembers.length > 5 && (
                                <div className="mt-4 text-center">
                                    <a
                                        href="/my-team"
                                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                        Tümünü Görüntüle ({teamMembers.length - 5} daha)
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Monthly Points Chart */}
                <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h3 className="text-xl font-semibold text-gray-900">Aylık Puan Trendi</h3>
                            <p className="text-sm text-gray-600">Son 6 aylık puan performansınız</p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Kazanılan Puan</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                                <span className="text-sm text-gray-600">Hedef</span>
                            </div>
                        </div>
                    </div>

                    <div className="relative">
                        {/* Chart Container */}
                        <div className="h-64 flex items-end justify-between space-x-2 mb-4">
                            {dashboard.monthlyPointsHistory.map((data, index) => {
                                const maxValue = Math.max(...dashboard.monthlyPointsHistory.map(d => Math.max(d.points, d.target)));
                                const pointsHeight = (data.points / maxValue) * 200;
                                const targetHeight = (data.target / maxValue) * 200;

                                return (
                                    <div key={index} className="flex-1 flex flex-col items-center space-y-2">
                                        {/* Chart Bars */}
                                        <div className="relative w-full h-48 flex items-end justify-center space-x-1">
                                            {/* Target Bar (Background) */}
                                            <div
                                                className="w-6 bg-gray-200 rounded-t-md relative group"
                                                style={{ height: `${targetHeight}px` }}
                                            >
                                                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                                    Hedef: {data.target}
                                                </div>
                                            </div>

                                            {/* Actual Points Bar */}
                                            <div
                                                className="w-6 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-md relative group"
                                                style={{ height: `${pointsHeight}px` }}
                                            >
                                                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                                    Puan: {data.points}
                                                </div>

                                                {/* Achievement Badge */}
                                                {data.points >= data.target && (
                                                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                                        <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                        </svg>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Month Label */}
                                        <div className="text-center">
                                            <div className="text-sm font-medium text-gray-900">{data.month}</div>
                                            <div className="text-xs text-gray-500">
                                                {data.points >= data.target ? (
                                                    <span className="text-green-600 font-medium">✓ Hedefe ulaşıldı</span>
                                                ) : (
                                                    <span className="text-gray-500">{data.target - data.points} puan kaldı</span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        {/* Chart Stats */}
                        <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                            <div className="text-center">
                                <div className="text-lg font-bold text-blue-600">
                                    {dashboard.monthlyPointsHistory[dashboard.monthlyPointsHistory.length - 1]?.points || 0}
                                </div>
                                <div className="text-sm text-gray-600">Bu Ay</div>
                            </div>
                            <div className="text-center">
                                <div className="text-lg font-bold text-green-600">
                                    {dashboard.monthlyPointsHistory.filter(d => d.points >= d.target).length}
                                </div>
                                <div className="text-sm text-gray-600">Hedefe Ulaşılan Ay</div>
                            </div>
                            <div className="text-center">
                                <div className="text-lg font-bold text-gray-900">
                                    {Math.round(dashboard.monthlyPointsHistory.reduce((sum, d) => sum + d.points, 0) / dashboard.monthlyPointsHistory.length)}
                                </div>
                                <div className="text-sm text-gray-600">Ortalama</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Monthly Activity Trend Line Chart */}
                <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h3 className="text-xl font-semibold text-gray-900">Aylık Performans Trendi</h3>
                            <p className="text-sm text-gray-600">Aktiflik yüzdesi ve ekip büyümesi</p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-0.5 bg-purple-500"></div>
                                <span className="text-sm text-gray-600">Aktiflik %</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="w-3 h-0.5 bg-green-500"></div>
                                <span className="text-sm text-gray-600">Ekip Sayısı</span>
                            </div>
                        </div>
                    </div>

                    <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                            <AreaChart
                                data={dashboard.monthlyActivityTrend}
                                margin={{
                                    top: 5,
                                    right: 30,
                                    left: 20,
                                    bottom: 5,
                                }}
                            >
                                <defs>
                                    <linearGradient id="colorActivity" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8} />
                                        <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1} />
                                    </linearGradient>
                                    <linearGradient id="colorTeam" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                                        <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
                                    </linearGradient>
                                </defs>
                                <CartesianGrid
                                    strokeDasharray="3 3"
                                    stroke="#f0f0f0"
                                    horizontal={true}
                                    vertical={false}
                                />
                                <XAxis
                                    dataKey="month"
                                    stroke="#6b7280"
                                    fontSize={12}
                                    axisLine={false}
                                    tickLine={false}
                                />
                                <YAxis
                                    yAxisId="left"
                                    stroke="#6b7280"
                                    fontSize={12}
                                    domain={[0, 100]}
                                    label={{ value: 'Aktiflik %', angle: -90, position: 'insideLeft' }}
                                    axisLine={false}
                                    tickLine={false}
                                />
                                <YAxis
                                    yAxisId="right"
                                    orientation="right"
                                    stroke="#6b7280"
                                    fontSize={12}
                                    domain={[0, 15]}
                                    label={{ value: 'Ekip Sayısı', angle: 90, position: 'insideRight' }}
                                    axisLine={false}
                                    tickLine={false}
                                />
                                <Tooltip
                                    contentStyle={{
                                        backgroundColor: 'rgba(31, 41, 55, 0.95)',
                                        border: 'none',
                                        borderRadius: '12px',
                                        color: 'white',
                                        fontSize: '12px',
                                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                                        backdropFilter: 'blur(10px)'
                                    }}
                                    formatter={(value, name) => [
                                        name === 'Aktiflik %' ? `%${value}` : value,
                                        name === 'Aktiflik %' ? 'Aktiflik' : 'Ekip Sayısı'
                                    ]}
                                    labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                                />
                                <Legend
                                    wrapperStyle={{ paddingTop: '20px', fontSize: '12px' }}
                                />
                                <Area
                                    yAxisId="left"
                                    type="monotone"
                                    dataKey="activityPercentage"
                                    stroke="#8b5cf6"
                                    strokeWidth={3}
                                    fill="url(#colorActivity)"
                                    fillOpacity={1}
                                    name="Aktiflik %"
                                    dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                                    activeDot={{
                                        r: 6,
                                        stroke: '#8b5cf6',
                                        strokeWidth: 2,
                                        fill: '#fff'
                                    }}
                                    animationBegin={0}
                                    animationDuration={2000}
                                    animationEasing="ease-out"
                                />
                                <Area
                                    yAxisId="right"
                                    type="monotone"
                                    dataKey="teamSize"
                                    stroke="#10b981"
                                    strokeWidth={3}
                                    fill="url(#colorTeam)"
                                    fillOpacity={1}
                                    name="Ekip Sayısı"
                                    dot={{ fill: '#059669', strokeWidth: 2, r: 4 }}
                                    activeDot={{
                                        r: 6,
                                        stroke: '#059669',
                                        strokeWidth: 2,
                                        fill: '#fff'
                                    }}
                                    animationBegin={500}
                                    animationDuration={2000}
                                    animationEasing="ease-out"
                                />
                            </AreaChart>
                        </ResponsiveContainer>
                    </div>

                    {/* New Members Indicators */}
                    <div className="flex justify-between mt-4 px-8">
                        {dashboard.monthlyActivityTrend.map((data, index) => (
                            <div key={index} className="text-center">
                                {data.newMembers > 0 && (
                                    <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        +{data.newMembers} yeni üye
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Chart Stats */}
                    <div className="grid grid-cols-4 gap-4 pt-6 border-t border-gray-100 mt-4">
                        <div className="text-center">
                            <div className="text-lg font-bold text-purple-600">
                                %{dashboard.monthlyActivityTrend[dashboard.monthlyActivityTrend.length - 1]?.activityPercentage || 0}
                            </div>
                            <div className="text-sm text-gray-600">Bu Ay Aktiflik</div>
                        </div>
                        <div className="text-center">
                            <div className="text-lg font-bold text-green-600">
                                {dashboard.monthlyActivityTrend[dashboard.monthlyActivityTrend.length - 1]?.teamSize || 0}
                            </div>
                            <div className="text-sm text-gray-600">Mevcut Ekip</div>
                        </div>
                        <div className="text-center">
                            <div className="text-lg font-bold text-blue-600">
                                {dashboard.monthlyActivityTrend.reduce((sum, d) => sum + d.newMembers, 0)}
                            </div>
                            <div className="text-sm text-gray-600">Toplam Yeni Üye</div>
                        </div>
                        <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">
                                %{Math.round(dashboard.monthlyActivityTrend.reduce((sum, d) => sum + d.activityPercentage, 0) / dashboard.monthlyActivityTrend.length)}
                            </div>
                            <div className="text-sm text-gray-600">Ortalama Aktiflik</div>
                        </div>
                    </div>
                </div>

                {/* Quick Actions */}
                <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-6">Hızlı İşlemler</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <a
                            href="/earnings"
                            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                        >
                            <TrendingUp className="h-6 w-6 text-blue-600 mr-3" />
                            <div>
                                <p className="font-medium text-gray-900">Kazanç Detayları</p>
                                <p className="text-sm text-gray-500">Tüm kazanç geçmişinizi görüntüleyin</p>
                            </div>
                        </a>

                        <a
                            href="/my-team"
                            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                        >
                            <Users className="h-6 w-6 text-purple-600 mr-3" />
                            <div>
                                <p className="font-medium text-gray-900">Ekip Yönetimi</p>
                                <p className="text-sm text-gray-500">Ekibinizi detaylı olarak inceleyin</p>
                            </div>
                        </a>

                        <a
                            href="/team-tree"
                            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors"
                        >
                            <BarChart3 className="h-6 w-6 text-orange-600 mr-3" />
                            <div>
                                <p className="font-medium text-gray-900">Ekip Ağacı Yönetimi</p>
                                <p className="text-sm text-gray-500">Ekip hiyerarşinizi görselleştirin</p>
                            </div>
                        </a>

                        {hasProductManagement ? (
                            <a
                                href="/pending-products"
                                className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
                            >
                                <Target className="h-6 w-6 text-green-600 mr-3" />
                                <div>
                                    <p className="font-medium text-gray-900">Ürün Yönetimi</p>
                                    <p className="text-sm text-gray-500">Ürün ekle ve onay bekleyenleri görüntüle</p>
                                </div>
                            </a>
                        ) : (
                            <div className="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50 opacity-50">
                                <Target className="h-6 w-6 text-gray-400 mr-3" />
                                <div>
                                    <p className="font-medium text-gray-500">Ürün Yönetimi</p>
                                    <p className="text-sm text-gray-400">Sadece satıcılar için</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

            </div>
        </div>
    );
};

export default DistributorPanel; 