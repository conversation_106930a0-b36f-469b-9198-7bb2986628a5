'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 🚀 Modal store imports
import {
    useReferenceRegistrationModal,
    useReferenceRegistrationData,
    useModalActions
} from '@/stores/modalStore';

interface ReferenceRegistrationModalProps {
    // 🔄 BACKWARD COMPATIBILITY: Eski props hala destekleniyor
    isOpen?: boolean;
    onClose?: () => void;
    onSubmit?: (referenceData: any) => void;
}

export default function ReferenceRegistrationModal({
    isOpen,
    onClose,
    onSubmit
}: ReferenceRegistrationModalProps) {
    // 🚀 Modal store hooks
    const isModalOpen = useReferenceRegistrationModal();
    const modalData = useReferenceRegistrationData();
    const { closeReferenceRegistrationModal } = useModalActions();

    // 🎯 HYBRID APPROACH: Store'u önceliklendire, props'u fallback o<PERSON><PERSON> kull<PERSON>
    const modalOpen = isModalOpen || isOpen || false;
    const handleClose = () => {
        // Store'dan açıldıysa store'u kapat
        if (isModalOpen) {
            closeReferenceRegistrationModal();
        }
        // Prop'dan açıldıysa callback'i çağır
        if (onClose) {
            onClose();
        }
    };

    // 🎯 Form state
    const [formData, setFormData] = useState(() => ({
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        notes: ''
    }));

    // 🔄 Store data varsa form'u doldur
    useEffect(() => {
        if (modalData) {
            setFormData(modalData);
        } else if (modalOpen) {
            // Modal açıldığında form'u temizle
            setFormData({
                firstName: '',
                lastName: '',
                email: '',
                phoneNumber: '',
                notes: ''
            });
        }
    }, [modalData, modalOpen]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Callback'i çağır (varsa)
        if (onSubmit) {
            onSubmit(formData);
        }

        // Form'u temizle
        setFormData({
            firstName: '',
            lastName: '',
            email: '',
            phoneNumber: '',
            notes: ''
        });

        // Modal'ı kapat
        handleClose();
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    return (
        <AnimatePresence>
            {modalOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={handleClose}
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="flex items-center justify-between mb-6">
                            <h2 className="text-2xl font-bold text-gray-800">Referans Kayıt Et</h2>
                            <button
                                onClick={handleClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Ad *
                                    </label>
                                    <input
                                        type="text"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleChange}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black"
                                        placeholder="Adınızı girin"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Soyad *
                                    </label>
                                    <input
                                        type="text"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleChange}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black"
                                        placeholder="Soyadınızı girin"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    E-posta *
                                </label>
                                <input
                                    type="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black"
                                    placeholder="E-posta adresini girin"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Telefon Numarası *
                                </label>
                                <input
                                    type="tel"
                                    name="phoneNumber"
                                    value={formData.phoneNumber}
                                    onChange={handleChange}
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-black"
                                    placeholder="Telefon numarasını girin"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Notlar (İsteğe bağlı)
                                </label>
                                <textarea
                                    name="notes"
                                    value={formData.notes}
                                    onChange={handleChange}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none text-black"
                                    placeholder="Ek notlar yazabilirsiniz..."
                                />
                            </div>

                            <div className="flex space-x-3 pt-4">
                                <motion.button
                                    type="button"
                                    onClick={handleClose}
                                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    İptal
                                </motion.button>
                                <motion.button
                                    type="submit"
                                    className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:shadow-lg transition-all"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    Kayıt Et
                                </motion.button>
                            </div>
                        </form>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 