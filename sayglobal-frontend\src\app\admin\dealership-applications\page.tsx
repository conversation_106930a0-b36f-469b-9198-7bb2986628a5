'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { DealershipApplication, DealershipApplicationStatus } from '@/types';
import { mockDealershipApplications } from '@/data/mocks/dealership';
import DealershipApplicationDetailModal from '@/components/DealershipApplicationDetailModal';
import AdminDealerNotificationModal from '@/components/AdminDealerNotificationModal';
import {
    Shield,
    Clock,
    CheckCircle,
    XCircle,
    Eye,
    User,
    FileText,
    Calendar,
    Search
} from 'lucide-react';

const DealershipApplicationsPage = () => {
    const { user, isLoading, updateUserRole } = useAuth();
    const router = useRouter();

    const [applications, setApplications] = useState<DealershipApplication[]>(mockDealershipApplications);
    const [selectedApplication, setSelectedApplication] = useState<DealershipApplication | null>(null);
    const [filterStatus, setFilterStatus] = useState<DealershipApplicationStatus | 'all'>('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [adminNotes, setAdminNotes] = useState('');

    // Notification modal state
    const [notificationModal, setNotificationModal] = useState({
        isOpen: false,
        type: 'success' as 'success' | 'error' | 'warning',
        title: '',
        message: ''
    });

    // Erişim kontrolü
    useEffect(() => {
        if (!isLoading && (!user || user.role !== 'admin')) {
            router.push('/login');
        }
    }, [user, isLoading, router]);

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    if (!user || user.role !== 'admin') {
        return null;
    }

    // Filtreleme ve arama
    const filteredApplications = applications.filter(app => {
        const matchesStatus = filterStatus === 'all' || app.status === filterStatus;
        const matchesSearch = searchTerm === '' ||
            app.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            app.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
            app.applicationData.companyName.toLowerCase().includes(searchTerm.toLowerCase());

        return matchesStatus && matchesSearch;
    });

    // Show notification modal
    const showNotification = (type: 'success' | 'error' | 'warning', title: string, message: string) => {
        setNotificationModal({
            isOpen: true,
            type,
            title,
            message
        });
    };

    // Close notification modal
    const closeNotificationModal = () => {
        setNotificationModal(prev => ({ ...prev, isOpen: false }));
    };

    // Başvuru onaylama/reddetme
    const handleApplicationAction = async (applicationId: number, action: 'approve' | 'reject', notes?: string) => {
        const notesToUse = notes || adminNotes;

        setIsProcessing(true);

        try {
            // İlgili başvuruyu bul
            const application = applications.find(app => app.id === applicationId);
            if (!application) {
                throw new Error('Başvuru bulunamadı');
            }

            // Backend işlemi simülasyonu
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Başvuru durumunu güncelle
            setApplications(prev => prev.map(app =>
                app.id === applicationId
                    ? {
                        ...app,
                        status: action === 'approve' ? 'approved' : 'rejected',
                        reviewedAt: new Date().toISOString(),
                        reviewedBy: user.id,
                        adminNotes: notesToUse.trim() || (action === 'approve' ? 'Başvuru onaylandı.' : 'Başvuru reddedildi.')
                    }
                    : app
            ));

            // Kullanıcının rolünü güncelle
            if (action === 'approve') {
                updateUserRole(application.userId, 'dealership', true, 'approved');
            } else if (action === 'reject') {
                updateUserRole(application.userId, 'customer', false, 'rejected');
            }

            setSelectedApplication(null);
            setAdminNotes('');

            // Show success notification
            if (action === 'approve') {
                showNotification('success', 'Başvuru Onaylandı!', 'Satıcı başvurusu başarıyla onaylandı ve kullanıcı hesabı aktif edildi.');
            } else {
                showNotification('error', 'Başvuru Reddedildi!', 'Satıcı başvurusu reddedildi ve kullanıcıya bilgilendirme e-postası gönderildi.');
            }
        } catch (error) {
            showNotification('error', 'İşlem Başarısız!', 'İşlem sırasında bir hata oluştu. Lütfen tekrar deneyin.');
        } finally {
            setIsProcessing(false);
        }
    };

    const getStatusBadge = (status: DealershipApplicationStatus) => {
        switch (status) {
            case 'pending':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <Clock className="w-3 h-3 mr-1" />
                        Beklemede
                    </span>
                );
            case 'approved':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Onaylandı
                    </span>
                );
            case 'rejected':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <XCircle className="w-3 h-3 mr-1" />
                        Reddedildi
                    </span>
                );
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                Satıcı Başvuruları
                            </h1>
                            <p className="text-gray-600">
                                Platforma katılmak isteyen satıcı başvurularını yönetin
                            </p>
                        </div>
                        <div className="flex items-center space-x-2 bg-red-100 px-4 py-2 rounded-lg">
                            <Shield className="h-5 w-5 text-red-600" />
                            <span className="text-red-800 font-medium">Admin Paneli</span>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Bekleyen</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {applications.filter(app => app.status === 'pending').length}
                                </p>
                            </div>
                            <Clock className="h-8 w-8 text-yellow-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Onaylanan</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {applications.filter(app => app.status === 'approved').length}
                                </p>
                            </div>
                            <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Reddedilen</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {applications.filter(app => app.status === 'rejected').length}
                                </p>
                            </div>
                            <XCircle className="h-8 w-8 text-red-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {applications.length}
                                </p>
                            </div>
                            <FileText className="h-8 w-8 text-blue-600" />
                        </div>
                    </div>
                </div>

                {/* Filters and Search */}
                <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <input
                                    type="text"
                                    placeholder="Ad, email veya şirket ara..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 w-64 text-black"
                                />
                            </div>

                            <select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value as DealershipApplicationStatus | 'all')}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-600"
                            >
                                <option value="all">Tüm Durumlar</option>
                                <option value="pending">Bekleyen</option>
                                <option value="approved">Onaylanan</option>
                                <option value="rejected">Reddedilen</option>
                            </select>
                        </div>

                        <div className="text-sm text-gray-600">
                            {filteredApplications.length} sonuç gösteriliyor
                        </div>
                    </div>
                </div>

                {/* Applications List */}
                <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Başvuran
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Şirket
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Kategori
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Başvuru Tarihi
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Durum
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredApplications.map((application) => (
                                    <tr key={application.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="flex-shrink-0 h-10 w-10">
                                                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <User className="h-5 w-5 text-gray-600" />
                                                    </div>
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {application.userName}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {application.userEmail}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                {application.applicationData.companyName}
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {application.applicationData.taxNumber}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                {application.applicationData.mainProductCategory}
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {application.applicationData.estimatedProductCount}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center text-sm text-gray-900">
                                                <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                                                {formatDate(application.submittedAt)}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            {getStatusBadge(application.status)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                                onClick={() => setSelectedApplication(application)}
                                                className="text-red-600 hover:text-red-900 flex items-center"
                                            >
                                                <Eye className="h-4 w-4 mr-1" />
                                                İncele
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {filteredApplications.length === 0 && (
                        <div className="text-center py-12">
                            <FileText className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Başvuru bulunamadı</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Arama kriterlerinize uygun başvuru bulunmuyor.
                            </p>
                        </div>
                    )}
                </div>

                {/* Application Detail Modal */}
                <DealershipApplicationDetailModal
                    application={selectedApplication!}
                    isOpen={!!selectedApplication}
                    onClose={() => setSelectedApplication(null)}
                    onApprove={(applicationId, notes) => handleApplicationAction(applicationId, 'approve', notes)}
                    onReject={(applicationId, notes) => handleApplicationAction(applicationId, 'reject', notes)}
                    adminNotes={adminNotes}
                    setAdminNotes={setAdminNotes}
                    isProcessing={isProcessing}
                    onShowWarning={() => showNotification('warning', 'Açıklama Gerekli', 'Red işlemi için açıklama yazmanız gerekmektedir.')}
                />

                {/* Notification Modal */}
                <AdminDealerNotificationModal
                    isOpen={notificationModal.isOpen}
                    onClose={closeNotificationModal}
                    type={notificationModal.type}
                    title={notificationModal.title}
                    message={notificationModal.message}
                />
            </div>
        </div>
    );
};

export default DealershipApplicationsPage; 