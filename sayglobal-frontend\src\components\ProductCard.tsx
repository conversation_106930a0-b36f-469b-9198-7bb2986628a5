'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FilteredProduct } from '@/types';
import { useDiscountRate } from '@/hooks/useDiscountRate';
import { useCustomerPriceStore } from '@/stores/customerPriceStore';

interface ProductCardProps {
    product: FilteredProduct;
}

export default function ProductCard({ product }: ProductCardProps) {
    const { data: discountRateData, isLoading: discountRateLoading } = useDiscountRate();

    // Global customer price state
    const { isCustomerPrice } = useCustomerPriceStore();

    // Discount rate'i al (null ise indirim yok)
    const discountRate = discountRateData?.discountRate || null;

    // PV, CV puanlarını hesapla (ratio'dan gerçek puanlara)
    const calculatePoints = (price: number, ratio: number | null) => {
        if (!ratio) return 0;
        return Math.round(price * (ratio / 100));
    };

    // Fiyat hesaplama mantı<PERSON><PERSON>
    const calculateFinalPrice = () => {
        let finalPrice = product.price;

        // Eğer customer price modu değilse ve discount rate varsa uygula
        if (!isCustomerPrice && discountRate && discountRate > 0) {
            finalPrice = finalPrice * (1 - discountRate / 100);
        }

        // Extra discount varsa uygula
        const extraDiscount = Number(product.extraDiscount);
        if (extraDiscount > 0) {
            finalPrice = finalPrice * (1 - extraDiscount / 100);
        }

        return finalPrice;
    };

    const finalPrice = calculateFinalPrice();
    const hasDiscount = (!isCustomerPrice && discountRate && discountRate > 0) || Number(product.extraDiscount) > 0;

    const pvPoints = calculatePoints(product.price, product.pv);
    const cvPoints = calculatePoints(product.price, product.cv);



    return (
        <motion.div
            whileHover={{ y: -8 }}
            transition={{ duration: 0.3 }}
            className="group"
        >
            <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 h-full flex flex-col">
                <Link href={`/product/${product.id}`} className="flex-1 flex flex-col">
                    <div className="relative h-64 overflow-hidden group">
                        <Image
                            src={product.mainImageUrl}
                            alt={product.name}
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                        />

                        {/* İndirim Badges */}
                        <div className="absolute top-3 right-3 flex flex-col gap-2">
                            {/* Membership Level Discount Badge */}
                            {!isCustomerPrice && discountRate && discountRate > 0 && (
                                <motion.div
                                    className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.1, type: "spring" }}
                                >
                                    %{discountRate} Üye İndirimi
                                </motion.div>
                            )}

                            {/* Extra Discount Badge */}
                            {(() => {
                                const discount = Number(product.extraDiscount);
                                return discount > 0 && (
                                    <motion.div
                                        className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg"
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        transition={{ delay: 0.2, type: "spring" }}
                                    >
                                        %{discount} İndirim
                                    </motion.div>
                                );
                            })()}
                        </div>

                        {/* Puan Badges */}
                        <div className="absolute top-3 left-3 flex flex-col gap-1">
                            {/* PV Badge */}
                            {pvPoints > 0 && (
                                <motion.div
                                    className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg flex items-center gap-1"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.3, type: "spring" }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                    </svg>
                                    <span className="font-bold">PV</span>
                                    <span>{pvPoints}</span>
                                </motion.div>
                            )}

                            {/* CV Badge */}
                            {cvPoints > 0 && (
                                <motion.div
                                    className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg flex items-center gap-1"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.4, type: "spring" }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                    </svg>
                                    <span className="font-bold">CV</span>
                                    <span>{cvPoints}</span>
                                </motion.div>
                            )}
                        </div>
                    </div>

                    <div className="p-5 flex-1 flex flex-col">
                        <h3 className="text-lg font-semibold mb-1 text-gray-800">{product.name}</h3>
                        <p className="text-gray-600 mb-2 text-sm">{product.brandName}</p>
                        <p className="text-gray-500 text-sm mb-4 line-clamp-2 flex-1">{product.description}</p>

                        <div className="mt-auto">
                            <div className="flex justify-between items-center mb-3">
                                <div>
                                    <span className="text-lg font-bold text-purple-700">
                                        {finalPrice.toFixed(2)} ₺
                                    </span>
                                    {hasDiscount && (
                                        <span className="text-sm text-gray-500 line-through ml-2">
                                            {product.price.toFixed(2)} ₺
                                        </span>
                                    )}
                                </div>

                                {/* Rating */}
                                {product.averageRating && product.averageRating > 0 && (
                                    <div className="flex items-center bg-yellow-50 px-2 py-1 rounded">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-5 w-5 text-yellow-400"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        <span className="ml-1 text-sm font-medium text-yellow-700">
                                            {product.averageRating.toFixed(1)}
                                        </span>
                                        <span className="ml-1 text-xs text-gray-500">
                                            ({product.totalReviewCount})
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </Link>
            </div>
        </motion.div>
    );
}
