import { useQuery, useMutation } from '@tanstack/react-query';
import { productService } from '@/services/api';
import { CategoryItem, SubCategoryItem, ProductFilterRequest, ProductFilterResponse, ReferenceDataResponse } from '@/types';

// Kategorileri getiren hook
export const useCategories = () => {
    return useQuery<CategoryItem[]>({
        queryKey: ['categories'],
        queryFn: async () => {
            const response = await productService.getCategories();
            if (!response.success) {
                throw new Error(response.error || 'Kategoriler alınamadı');
            }
            return response.data;
        },
        staleTime: 10 * 60 * 1000, // 10 dakika boyunca taze kabul et
        refetchOnWindowFocus: false, // Kategoriler sık değişmez
        refetchOnMount: false, // Mount olduğunda yeniden çekme
    });
};

// Alt kategorileri getiren hook
export const useSubCategories = (categoryId: number | null) => {
    return useQuery<SubCategoryItem[]>({
        queryKey: ['subcategories', categoryId],
        queryFn: async () => {
            if (!categoryId) return [];
            const response = await productService.getSubCategoriesByCategory(categoryId);
            if (!response.success) {
                throw new Error(response.error || 'Alt kategoriler alınamadı');
            }
            return response.data;
        },
        enabled: !!categoryId, // Sadece categoryId varsa sorguyu çalıştır
        staleTime: 10 * 60 * 1000, // 10 dakika boyunca taze kabul et
        refetchOnWindowFocus: false,
    });
};

// Ürünleri filtreleyen hook
export const useFilterProducts = () => {
    return useMutation<ProductFilterResponse, Error, ProductFilterRequest>({
        mutationFn: async (filterRequest: ProductFilterRequest) => {
            const response = await productService.filterProducts(filterRequest);
            if (!response.success) {
                throw new Error(response.error || 'Ürünler filtrelenirken hata oluştu');
            }
            return response.data;
        },
        onSuccess: (data) => {
            console.log('✅ Ürünler başarıyla filtrelendi:', data.data.length, 'ürün bulundu');
        },
        onError: (error) => {
            console.error('❌ Ürün filtreleme hatası:', error);
        }
    });
};

// Ürünleri getiren hook (query olarak)
export const useProducts = (filterRequest: ProductFilterRequest) => {
    return useQuery<ProductFilterResponse>({
        queryKey: ['products', filterRequest],
        queryFn: async () => {
            const response = await productService.filterProducts(filterRequest);
            if (!response.success) {
                throw new Error(response.error || 'Ürünler alınamadı');
            }
            return response.data;
        },
        staleTime: 2 * 60 * 1000, // 2 dakika boyunca taze kabul et
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
        enabled: true, // Her zaman aktif
    });
};

// Reference data getiren hook
export const useReferenceData = () => {
    return useQuery<ReferenceDataResponse>({
        queryKey: ['referenceData'],
        queryFn: async () => {
            const response = await productService.getReferenceData();
            if (!response.success) {
                throw new Error(response.error || 'Reference data alınamadı');
            }
            return response.data;
        },
        staleTime: 10 * 60 * 1000, // 10 dakika boyunca taze kabul et
        refetchOnWindowFocus: false, // Reference data sık değişmez
        refetchOnMount: false, // Mount olduğunda yeniden çekme
    });
};

// Cache yenileme hook'u
export const useProductsPageCacheRefresh = () => {
    const queryClient = useQuery.getQueryClient?.() || null;

    const refreshCategories = () => {
        if (queryClient) {
            queryClient.invalidateQueries({ queryKey: ['categories'] });
            console.log('🔄 Kategoriler cache\'i yenilendi');
        }
    };

    const refreshSubCategories = (categoryId?: number) => {
        if (queryClient) {
            if (categoryId) {
                queryClient.invalidateQueries({ queryKey: ['subcategories', categoryId] });
                console.log(`🔄 Kategori ${categoryId} alt kategorileri cache'i yenilendi`);
            } else {
                queryClient.invalidateQueries({ queryKey: ['subcategories'] });
                console.log('🔄 Tüm alt kategoriler cache\'i yenilendi');
            }
        }
    };

    const refreshProducts = () => {
        if (queryClient) {
            queryClient.invalidateQueries({ queryKey: ['products'] });
            console.log('🔄 Ürünler cache\'i yenilendi');
        }
    };

    const refreshAll = () => {
        refreshCategories();
        refreshSubCategories();
        refreshProducts();
    };

    return {
        refreshCategories,
        refreshSubCategories,
        refreshProducts,
        refreshAll,
    };
};
