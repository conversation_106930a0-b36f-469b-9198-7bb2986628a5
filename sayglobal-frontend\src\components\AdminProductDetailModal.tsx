'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
    Package,
    Clock,
    CheckCircle,
    XCircle,
    Tag,
    User,
    Calendar,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import { PendingProduct, ProductApprovalStatus } from '@/types';

interface AdminProductDetailModalProps {
    product: PendingProduct;
    isOpen: boolean;
    onClose: () => void;
}

const AdminProductDetailModal: React.FC<AdminProductDetailModalProps> = ({ product, isOpen, onClose }) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    // Modal açıldığında body scroll'unu kapat
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        // Cleanup function - component unmount olduğunda scroll'u geri aç
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    if (!isOpen) return null;

    const getStatusColor = (status: ProductApprovalStatus) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'approved':
                return 'bg-green-100 text-green-800';
            case 'rejected':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: ProductApprovalStatus) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'approved':
                return <CheckCircle className="h-4 w-4" />;
            case 'rejected':
                return <XCircle className="h-4 w-4" />;
            default:
                return <Package className="h-4 w-4" />;
        }
    };

    const getStatusText = (status: ProductApprovalStatus) => {
        switch (status) {
            case 'pending':
                return 'Onay Bekliyor';
            case 'approved':
                return 'Onaylandı';
            case 'rejected':
                return 'Reddedildi';
            default:
                return 'Bilinmiyor';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const nextImage = () => {
        setCurrentImageIndex((prev) =>
            prev === product.images.length - 1 ? 0 : prev + 1
        );
    };

    const prevImage = () => {
        setCurrentImageIndex((prev) =>
            prev === 0 ? product.images.length - 1 : prev - 1
        );
    };

    const goToImage = (index: number) => {
        setCurrentImageIndex(index);
    };

    return (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-lg flex items-center justify-center p-4 z-50">
            <motion.div
                className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
            >
                {/* Modal Header */}
                <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-gray-900">Ürün Detayları (Admin)</h2>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                {/* Modal Content */}
                <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Left Column - Images */}
                        <div>
                            <div className="space-y-4">
                                {/* Main Image with Navigation */}
                                <div className="relative aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                                    <img
                                        src={product.images[currentImageIndex]}
                                        alt={`${product.title} ${currentImageIndex + 1}`}
                                        className="w-full h-80 object-cover"
                                    />

                                    {/* Navigation Arrows - Only show if multiple images */}
                                    {product.images.length > 1 && (
                                        <>
                                            <button
                                                onClick={prevImage}
                                                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                                            >
                                                <ChevronLeft className="h-5 w-5" />
                                            </button>
                                            <button
                                                onClick={nextImage}
                                                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                                            >
                                                <ChevronRight className="h-5 w-5" />
                                            </button>

                                            {/* Image Counter */}
                                            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                                                {currentImageIndex + 1} / {product.images.length}
                                            </div>
                                        </>
                                    )}
                                </div>

                                {/* Thumbnail Navigation - Only show if multiple images */}
                                {product.images.length > 1 && (
                                    <div className="grid grid-cols-4 gap-2">
                                        {product.images.map((image, index) => (
                                            <button
                                                key={index}
                                                onClick={() => goToImage(index)}
                                                className={`aspect-w-1 aspect-h-1 bg-gray-200 rounded-md overflow-hidden border-2 transition-colors ${currentImageIndex === index
                                                    ? 'border-red-500'
                                                    : 'border-transparent hover:border-gray-300'
                                                    }`}
                                            >
                                                <img
                                                    src={image}
                                                    alt={`${product.title} thumbnail ${index + 1}`}
                                                    className="w-full h-20 object-cover"
                                                />
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Right Column - Product Info */}
                        <div className="space-y-6">
                            {/* Status Badge */}
                            <div className="flex items-center justify-between">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(product.status)}`}>
                                    {getStatusIcon(product.status)}
                                    <span className="ml-2">{getStatusText(product.status)}</span>
                                </span>
                                <span className="text-sm text-gray-500">{product.category}</span>
                            </div>

                            {/* Title and Brand */}
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                    {product.title}
                                </h1>
                                <p className="text-lg text-gray-600">{product.brand}</p>
                            </div>

                            {/* Price and Points */}
                            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p className="text-sm text-gray-600">Fiyat</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        ₺{product.price.toFixed(2)}
                                    </p>
                                    {product.discountPercentage != null && product.discountPercentage > 0 && (
                                        <p className="text-sm text-green-600">
                                            %{product.discountPercentage} indirim
                                        </p>
                                    )}
                                </div>
                                <div className="text-right">
                                    <p className="text-sm text-gray-600">Puan</p>
                                    <div className="flex items-center space-x-1">
                                        <Tag className="h-4 w-4 text-red-500" />
                                        <span className="text-lg font-bold text-red-600">
                                            {product.points}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Description */}
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">Açıklama</h3>
                                <p className="text-gray-700 leading-relaxed">
                                    {product.description}
                                </p>
                            </div>

                            {/* Stock */}
                            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                <div className="flex items-center space-x-2">
                                    <Package className="h-5 w-5 text-red-600" />
                                    <span className="text-sm font-medium text-red-900">Stok Adedi</span>
                                </div>
                                <span className="text-lg font-bold text-red-900">
                                    {product.stock} adet
                                </span>
                            </div>

                            {/* Submission Info */}
                            <div className="border-t border-gray-200 pt-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Gönderim Bilgileri</h3>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <User className="h-4 w-4 text-gray-400" />
                                            <span className="text-sm text-gray-600">Distribütör</span>
                                        </div>
                                        <span className="text-sm font-medium text-gray-900">
                                            {product.distributorName}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <Calendar className="h-4 w-4 text-gray-400" />
                                            <span className="text-sm text-gray-600">Gönderilme Tarihi</span>
                                        </div>
                                        <span className="text-sm font-medium text-gray-900">
                                            {formatDate(product.submittedAt)}
                                        </span>
                                    </div>
                                    {product.reviewedAt && (
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <Calendar className="h-4 w-4 text-gray-400" />
                                                <span className="text-sm text-gray-600">İnceleme Tarihi</span>
                                            </div>
                                            <span className="text-sm font-medium text-gray-900">
                                                {formatDate(product.reviewedAt)}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Admin Notes */}
                            {product.adminNotes && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div className="flex items-start space-x-2">
                                        <svg className="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 className="text-sm font-medium text-yellow-800 mb-1">Admin Notu</h4>
                                            <p className="text-sm text-yellow-700">
                                                {product.adminNotes}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Action Buttons */}
                            <div className="flex space-x-3 pt-4">
                                <button
                                    onClick={onClose}
                                    className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                                >
                                    Kapat
                                </button>
                                <Link
                                    href={`/admin/edit-product/${product.id}`}
                                    className="flex-1 px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-center"
                                    onClick={onClose}
                                >
                                    Düzenle
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>
        </div>
    );
};

export default AdminProductDetailModal; 