'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle } from 'lucide-react';

interface PlacementSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    memberName: string;
    position: 'sol' | 'sağ';
}

const PlacementSuccessModal: React.FC<PlacementSuccessModalProps> = ({
    isOpen,
    onClose,
    memberName,
    position
}) => {
    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={onClose}
                >
                    <motion.div
                        className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4"
                        initial={{ scale: 0.8, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.8, opacity: 0, y: 50 }}
                        transition={{
                            duration: 0.3,
                            type: "spring",
                            damping: 20,
                            stiffness: 300
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Success Icon */}
                        <motion.div
                            className="flex justify-center mb-6"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.1, type: "spring", damping: 15 }}
                        >
                            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                                <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.2, type: "spring", damping: 12 }}
                                >
                                    <CheckCircle className="h-12 w-12 text-green-600" />
                                </motion.div>
                            </div>
                        </motion.div>

                        {/* Success Message */}
                        <motion.div
                            className="text-center"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                        >
                            <h2 className="text-2xl font-bold text-gray-900 mb-3">
                                Başarıyla Yerleştirildi! 🎉
                            </h2>
                            <p className="text-gray-600 mb-6">
                                <span className="font-semibold text-green-600">{memberName}</span>
                                {' '}ekip ağacının <span className="font-semibold">{position}</span> tarafına
                                başarıyla yerleştirildi.
                            </p>

                            {/* Action Buttons */}
                            <div className="flex space-x-3">
                                <motion.button
                                    onClick={onClose}
                                    className="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    Harika!
                                </motion.button>
                            </div>
                        </motion.div>

                        {/* Decorative Elements */}
                        <motion.div
                            className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-emerald-500 rounded-t-2xl"
                            initial={{ scaleX: 0 }}
                            animate={{ scaleX: 1 }}
                            transition={{ delay: 0.4, duration: 0.6 }}
                        />
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default PlacementSuccessModal; 