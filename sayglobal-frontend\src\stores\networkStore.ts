import { create } from 'zustand';

type NetworkStatus = 'online' | 'offline' | 'reconnecting';

interface NetworkState {
    status: NetworkStatus;
    setStatus: (status: NetworkStatus) => void;
}

export const useNetworkStore = create<NetworkState>((set) => ({
    status: (typeof window !== 'undefined' && !navigator.onLine) ? 'offline' : 'online', // Browser offline durumunu kontrol et
    setStatus: (status) => set({ status }),
})); 