'use client';

import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 🚀 Modal store imports
import {
    useSetDefaultConfirmationModal,
    useSetDefaultConfirmationData,
    useModalActions
} from '@/stores/modalStore';

interface SetDefaultConfirmationModalProps {
    // 🔄 BACKWARD COMPATIBILITY: Eski props hala destekleniyor
    isOpen?: boolean;
    onClose?: () => void;
    onConfirm?: () => void;
    targetAddress?: {
        id: number;
        title: string;
        fullAddress: string;
        city: string;
        district: string;
    };
    deletedDefaultAddress?: {
        id: number;
        title: string;
    };
}

export default function SetDefaultConfirmationModal({
    isOpen,
    onClose,
    onConfirm,
    targetAddress: propTargetAddress,
    deletedDefaultAddress: propDeletedDefaultAddress
}: SetDefaultConfirmationModalProps) {
    // 🚀 Modal store hooks
    const isModalOpen = useSetDefaultConfirmationModal();
    const modalData = useSetDefaultConfirmationData();
    const { closeSetDefaultConfirmationModal } = useModalActions();

    // 🎯 HYBRID APPROACH: Store'u önceliklendire, props'u fallback olarak kullan
    const modalOpen = isModalOpen || isOpen || false;
    const targetAddress = modalData?.targetAddress || propTargetAddress;
    const deletedDefaultAddress = modalData?.deletedDefaultAddress || propDeletedDefaultAddress;

    // 🔒 Zorunlu mod: Varsayılan adres silindiğinde
    const isForced = !!deletedDefaultAddress;

    const handleClose = () => {
        // Store'dan açıldıysa store'u kapat
        if (isModalOpen) {
            closeSetDefaultConfirmationModal();
        }

        // Prop'dan açıldıysa callback'i çağır
        if (onClose) {
            onClose();
        }
    };

    const handleConfirm = () => {
        // Callback'i çağır
        if (onConfirm) {
            onConfirm();
        }

        // Modal'ı kapat
        handleClose();
    };

    // 🚫 ESC tuşu ile kapanmasını engelle (sadece zorunlu modda)
    useEffect(() => {
        if (modalOpen && isForced) {
            const handleKeyDown = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    e.stopPropagation();
                }
            };

            document.addEventListener('keydown', handleKeyDown, true);
            return () => {
                document.removeEventListener('keydown', handleKeyDown, true);
            };
        }
    }, [modalOpen, isForced]);

    return (
        <AnimatePresence>
            {modalOpen && targetAddress && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/20 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    onClick={isForced ? undefined : handleClose} // Zorunlu değilse backdrop tıklaması ile kapanabilir
                >
                    {/* Backdrop */}
                    <div className="absolute inset-0" />

                    {/* Modal Content */}
                    <motion.div
                        className="relative bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl"
                        initial={{ scale: 0.7, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.7, opacity: 0, y: 50 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 25,
                            duration: 0.5
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center">
                                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                                    <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                                <h2 className="text-2xl font-bold text-gray-800">
                                    Varsayılan Adres
                                </h2>
                            </div>
                            {!isForced && (
                                <motion.button
                                    onClick={handleClose}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </motion.button>
                            )}
                        </div>

                        {/* Content */}
                        <div className="mb-8">
                            {isForced ? (
                                // 🔒 Zorunlu mod: Silinme durumu
                                <div className="space-y-4">
                                    <p className="text-gray-700 leading-relaxed">
                                        <span className="font-semibold text-red-600">"{deletedDefaultAddress?.title}"</span> adresiniz silindi.
                                    </p>
                                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                        <p className="text-amber-800 text-sm">
                                            <span className="font-semibold">"{targetAddress.title}"</span> adresiniz otomatik olarak varsayılan adres yapılacak.
                                        </p>
                                    </div>
                                </div>
                            ) : (
                                // 🆓 Normal mod: Manuel seçim
                                <p className="text-gray-700 leading-relaxed">
                                    <span className="font-semibold text-purple-600">"{targetAddress.title}"</span> adresini varsayılan adres olarak ayarlamak ister misiniz?
                                </p>
                            )}

                            {/* Address Preview */}
                            <div className="mt-6 bg-gray-50 rounded-lg p-4">
                                <div className="flex items-start space-x-3">
                                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-semibold text-gray-900">{targetAddress.title}</h4>
                                        <p className="text-sm text-gray-600 mt-1">
                                            {targetAddress.district}, {targetAddress.city}
                                        </p>
                                        <p className="text-sm text-gray-500 mt-1">
                                            {targetAddress.fullAddress}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        {isForced ? (
                            // 🔒 Zorunlu mod: Sadece Tamam butonu
                            <div className="flex flex-col space-y-3">
                                <motion.button
                                    onClick={handleConfirm}
                                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    Tamam, Bu Adresi Varsayılan Yap
                                </motion.button>

                                <div className="text-xs text-gray-500 text-center space-y-1">
                                    <p>Bir varsayılan adres seçmeniz zorunludur</p>
                                    <p className="text-purple-600">
                                        💡 Dilerseniz sonradan varsayılan adresinizi değiştirebilirsiniz
                                    </p>
                                </div>
                            </div>
                        ) : (
                            // 🆓 Normal mod: Tamam + İptal butonları
                            <div className="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0">
                                <motion.button
                                    onClick={handleConfirm}
                                    className="flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    Evet, Varsayılan Yap
                                </motion.button>

                                <motion.button
                                    onClick={handleClose}
                                    className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    İptal
                                </motion.button>
                            </div>
                        )}
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 