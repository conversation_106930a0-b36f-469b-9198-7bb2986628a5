'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { mockOrderDetails } from '@/data/mocks/account';
import { OrderDetails, OrderDetailItem, TimelineStep } from '@/types';
import {
    ArrowLeft,
    Package,
    Truck,
    MapPin,
    CreditCard,
    CheckCircle,
    Clock,
    Phone,
    Receipt,
    Star,
    Download
} from 'lucide-react';

const OrderDetailsPage = () => {
    const { orderId } = useParams();
    const orderDetails: OrderDetails | undefined = mockOrderDetails[orderId as string];

    if (!orderDetails) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="text-center">
                    <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Sipar<PERSON>ş bulunamadı</h2>
                    <p className="text-gray-600 mb-6">Bu sipariş numarası mevcut değil veya erişim izniniz bulunmuyor.</p>
                    <Link
                        href="/account?tab=orders"
                        className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300"
                    >
                        Sipariş Geçmişine Dön
                    </Link>
                </div>
            </div>
        );
    }

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'teslim edildi':
                return <CheckCircle className="h-5 w-5 text-green-600" />;
            case 'kargoya verildi':
                return <Truck className="h-5 w-5 text-blue-600" />;
            case 'hazırlanıyor':
                return <Package className="h-5 w-5 text-yellow-600" />;
            default:
                return <Clock className="h-5 w-5 text-gray-600" />;
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(price);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
            <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/account?tab=orders"
                                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Sipariş Geçmişi
                            </Link>
                            <span className="text-gray-300">/</span>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Sipariş Detayı
                            </h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <button className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <Download className="h-4 w-4 mr-2" />
                                Faturayı İndir
                            </button>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Sol taraf - Ana içerik */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Sipariş Özeti */}
                        <motion.div
                            className="bg-white rounded-xl shadow-lg p-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                        >
                            <div className="flex items-center justify-between mb-6">
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">Sipariş #{orderDetails.id}</h2>
                                    <p className="text-gray-600">{orderDetails.orderDate} tarihinde verildi</p>
                                </div>
                                <div className="flex items-center space-x-2">
                                    {getStatusIcon(orderDetails.status)}
                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${orderDetails.statusClass}`}>
                                        {orderDetails.status}
                                    </span>
                                </div>
                            </div>

                            {orderDetails.trackingNumber && (
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="font-medium text-blue-900">Takip Numarası</p>
                                            <p className="text-blue-700 font-mono">{orderDetails.trackingNumber}</p>
                                        </div>
                                        <button className="text-blue-600 hover:text-blue-800 font-medium">
                                            Kargo Takip
                                        </button>
                                    </div>
                                </div>
                            )}
                        </motion.div>

                        {/* Sipariş Takip Durumu */}
                        <motion.div
                            className="bg-white rounded-xl shadow-lg p-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                        >
                            <h3 className="text-lg font-semibold text-gray-900 mb-6">Sipariş Durumu</h3>
                            <div className="space-y-4">
                                {orderDetails.timeline.map((step: TimelineStep, index: number) => (
                                    <div key={index} className="flex items-start space-x-4">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                            </div>
                                        </div>
                                        <div className="flex-1">
                                            <p className="font-medium text-gray-900">{step.status}</p>
                                            <p className="text-sm text-gray-600">{step.date}</p>
                                            <p className="text-sm text-gray-500 mt-1">{step.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </motion.div>

                        {/* Sipariş Ürünleri */}
                        <motion.div
                            className="bg-white rounded-xl shadow-lg p-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                        >
                            <h3 className="text-lg font-semibold text-gray-900 mb-6">Sipariş Ürünleri</h3>
                            <div className="space-y-4">
                                {orderDetails.items.map((item: OrderDetailItem, index: number) => (
                                    <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <Package className="h-8 w-8 text-gray-400" />
                                        </div>
                                        <div className="flex-1">
                                            <h4 className="font-medium text-gray-900">{item.name}</h4>
                                            <p className="text-sm text-gray-600">{item.brand}</p>
                                            <div className="flex items-center space-x-4 mt-2">
                                                <span className="text-sm text-gray-500">
                                                    Adet: {item.quantity}
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    Birim: {formatPrice(item.unitPrice)}
                                                </span>
                                                <span className="text-sm text-purple-600 font-medium">
                                                    +{item.points} puan
                                                </span>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-semibold text-gray-900">
                                                {formatPrice(item.totalPrice)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Fiyat Detayları */}
                            <div className="border-t border-gray-200 mt-6 pt-6">
                                <div className="space-y-3">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-gray-600">Ara Toplam</span>
                                        <span className="text-gray-900">{formatPrice(orderDetails.pricing.subtotal)}</span>
                                    </div>
                                    {orderDetails.pricing.discount > 0 && (
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">İndirim</span>
                                            <span className="text-green-600">-{formatPrice(orderDetails.pricing.discount)}</span>
                                        </div>
                                    )}
                                    <div className="flex justify-between text-sm">
                                        <span className="text-gray-600">Kargo</span>
                                        <span className="text-gray-900">{formatPrice(orderDetails.pricing.shippingCost)}</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span className="text-gray-600">KDV</span>
                                        <span className="text-gray-900">{formatPrice(orderDetails.pricing.tax)}</span>
                                    </div>
                                    <div className="border-t border-gray-200 pt-3">
                                        <div className="flex justify-between">
                                            <span className="text-lg font-semibold text-gray-900">Toplam</span>
                                            <span className="text-lg font-semibold text-gray-900">
                                                {formatPrice(orderDetails.pricing.total)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>

                    {/* Sağ taraf - Yan bilgiler */}
                    <div className="space-y-8">
                        {/* Teslimat Bilgileri */}
                        <motion.div
                            className="bg-white rounded-xl shadow-lg p-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                        >
                            <div className="flex items-center mb-4">
                                <MapPin className="h-5 w-5 text-gray-600 mr-2" />
                                <h3 className="text-lg font-semibold text-gray-900">Teslimat Bilgileri</h3>
                            </div>
                            <div className="space-y-3">
                                <div>
                                    <p className="font-medium text-gray-900">{orderDetails.shippingInfo.address.name}</p>
                                    <p className="text-sm text-gray-600 flex items-center mt-1">
                                        <Phone className="h-4 w-4 mr-1" />
                                        {orderDetails.shippingInfo.address.phone}
                                    </p>
                                </div>
                                <div className="text-sm text-gray-600">
                                    <p>{orderDetails.shippingInfo.address.address}</p>
                                    <p>{orderDetails.shippingInfo.address.district}, {orderDetails.shippingInfo.address.city}</p>
                                    <p>{orderDetails.shippingInfo.address.postalCode}</p>
                                </div>
                                <div className="pt-3 border-t border-gray-200">
                                    <p className="text-sm">
                                        <span className="font-medium text-gray-600">Kargo Yöntemi: </span>
                                        <span className="text-gray-600">{orderDetails.shippingInfo.method}</span>
                                    </p>
                                    {orderDetails.deliveryDate && (
                                        <p className="text-sm mt-1">
                                            <span className="font-medium text-gray-600">Teslim Tarihi: </span>
                                            <span className="text-gray-600">{orderDetails.deliveryDate}</span>
                                        </p>
                                    )}
                                </div>
                            </div>
                        </motion.div>

                        {/* Ödeme Bilgileri */}
                        <motion.div
                            className="bg-white rounded-xl shadow-lg p-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5 }}
                        >
                            <div className="flex items-center mb-4">
                                <CreditCard className="h-5 w-5 text-gray-600 mr-2" />
                                <h3 className="text-lg font-semibold text-gray-900">Ödeme Bilgileri</h3>
                            </div>
                            <div className="space-y-3">
                                <div>
                                    <p className="font-medium text-gray-900">Ödeme Yöntemi</p>
                                    <p className="text-sm text-gray-600">{orderDetails.paymentInfo.method}</p>
                                    {orderDetails.paymentInfo.cardLast4 && (
                                        <p className="text-sm text-gray-500 mt-1">
                                            **** **** **** {orderDetails.paymentInfo.cardLast4}
                                        </p>
                                    )}
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Ödeme Tarihi</p>
                                    <p className="text-sm text-gray-600">{orderDetails.paymentInfo.paymentDate}</p>
                                </div>
                            </div>
                        </motion.div>

                        {/* İşlemler */}
                        <motion.div
                            className="bg-white rounded-xl shadow-lg p-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                        >
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">İşlemler</h3>
                            <div className="space-y-3">
                                <button className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                    <Receipt className="h-4 w-4 mr-2" />
                                    Fatura İndir
                                </button>
                                <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    <Star className="h-4 w-4 mr-2" />
                                    Ürünleri Değerlendir
                                </button>
                                <Link
                                    href="/account?tab=orders"
                                    className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    Sipariş Geçmişine Dön
                                </Link>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default OrderDetailsPage; 