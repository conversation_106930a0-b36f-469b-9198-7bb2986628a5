import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { CatalogProductDetailResponse } from '@/types';

// 🎯 Cache variables for selectors to prevent unnecessary re-renders
let cachedSelectedVariant: any = null;
let lastSelectedVariantKey: string | null = null;
let cachedCurrentImage: any = null;
let lastCurrentImageKey: string | null = null;
let cachedVariantImages: any[] = [];
let lastVariantId: number | null = null;

// 🎯 Static empty array to prevent unnecessary re-renders
const EMPTY_ARRAY: any[] = [];

// 🎯 Catalog Product Detail Store State
interface CatalogProductDetailState {
    // Product data
    productData: CatalogProductDetailResponse | null;
    selectedVariantIndex: number;
    currentImageIndex: number;
    slideDirection: number; // 1 for next, -1 for previous

    // UI state
    isLoading: boolean;
    error: string | null;

    // Cache
    cache: Map<number, { data: CatalogProductDetailResponse; timestamp: number }>;

    // Quantity
    quantity: number;
}

// 🎯 Catalog Product Detail Store Actions
interface CatalogProductDetailActions {
    // Data actions
    setProductData: (data: CatalogProductDetailResponse | null) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;

    // Variant and image navigation
    setSelectedVariant: (index: number) => void;
    setCurrentImage: (index: number) => void;
    nextImage: () => void;
    prevImage: () => void;

    // Quantity actions
    setQuantity: (quantity: number) => void;
    increaseQuantity: () => void;
    decreaseQuantity: () => void;

    // Cache actions
    setCachedProduct: (productId: number, data: CatalogProductDetailResponse) => void;
    getCachedProduct: (productId: number) => CatalogProductDetailResponse | null;
    isCacheValid: (productId: number, maxAge?: number) => boolean;
    clearCache: () => void;
    clearProductCache: (productId: number) => void;

    // Reset actions
    resetState: () => void;
}

// 🎯 Combined store type
type CatalogProductDetailStore = CatalogProductDetailState & CatalogProductDetailActions;

// 🎯 Initial state
const initialState: CatalogProductDetailState = {
    productData: null,
    selectedVariantIndex: 0,
    currentImageIndex: 0,
    slideDirection: 0,
    isLoading: false,
    error: null,
    cache: new Map(),
    quantity: 1,
};

// 🐻 Create store
export const useCatalogProductDetailStore = create<CatalogProductDetailStore>()(
    devtools(
        (set, get) => ({
            // Initial state
            ...initialState,

            // Data actions
            setProductData: (data: CatalogProductDetailResponse | null) => {
                const currentState = get();
                // Only update if data actually changed
                if (currentState.productData !== data) {
                    set({
                        productData: data,
                        selectedVariantIndex: 0,
                        currentImageIndex: 0,
                        slideDirection: 0,
                        error: null,
                    }, false, 'catalogProductDetail/setProductData');
                }
            },

            setLoading: (loading: boolean) => {
                set({ isLoading: loading }, false, 'catalogProductDetail/setLoading');
            },

            setError: (error: string | null) => {
                set({ error, isLoading: false }, false, 'catalogProductDetail/setError');
            },

            // Variant and image navigation
            setSelectedVariant: (index: number) => {
                const state = get();
                const maxIndex = state.productData?.data.variants.length || 0;
                if (index >= 0 && index < maxIndex) {
                    set({
                        selectedVariantIndex: index,
                        currentImageIndex: 0, // Reset image index when variant changes
                    }, false, 'catalogProductDetail/setSelectedVariant');
                }
            },

            setCurrentImage: (index: number) => {
                const state = get();
                const currentVariant = state.productData?.data.variants[state.selectedVariantIndex];
                const maxIndex = currentVariant?.images.length || 0;
                if (index >= 0 && index < maxIndex) {
                    set({ currentImageIndex: index }, false, 'catalogProductDetail/setCurrentImage');
                }
            },

            nextImage: () => {
                const state = get();
                const currentVariant = state.productData?.data.variants[state.selectedVariantIndex];
                const maxIndex = currentVariant?.images.length || 0;
                if (maxIndex > 0) {
                    const nextIndex = (state.currentImageIndex + 1) % maxIndex;
                    set({
                        currentImageIndex: nextIndex,
                        slideDirection: 1
                    }, false, 'catalogProductDetail/nextImage');
                }
            },

            prevImage: () => {
                const state = get();
                const currentVariant = state.productData?.data.variants[state.selectedVariantIndex];
                const maxIndex = currentVariant?.images.length || 0;
                if (maxIndex > 0) {
                    const prevIndex = state.currentImageIndex === 0 ? maxIndex - 1 : state.currentImageIndex - 1;
                    set({
                        currentImageIndex: prevIndex,
                        slideDirection: -1
                    }, false, 'catalogProductDetail/prevImage');
                }
            },

            // Quantity actions
            setQuantity: (quantity: number) => {
                if (quantity >= 1) {
                    set({ quantity }, false, 'catalogProductDetail/setQuantity');
                }
            },

            increaseQuantity: () => {
                const state = get();
                set({ quantity: state.quantity + 1 }, false, 'catalogProductDetail/increaseQuantity');
            },

            decreaseQuantity: () => {
                const state = get();
                if (state.quantity > 1) {
                    set({ quantity: state.quantity - 1 }, false, 'catalogProductDetail/decreaseQuantity');
                }
            },

            // Cache actions
            setCachedProduct: (productId: number, data: CatalogProductDetailResponse) => {
                const state = get();
                const newCache = new Map(state.cache);
                newCache.set(productId, {
                    data,
                    timestamp: Date.now(),
                });
                set({ cache: newCache }, false, 'catalogProductDetail/setCachedProduct');
            },

            getCachedProduct: (productId: number) => {
                const state = get();
                const cached = state.cache.get(productId);
                return cached?.data || null;
            },

            isCacheValid: (productId: number, maxAge: number = 5 * 60 * 1000) => {
                const state = get();
                const cached = state.cache.get(productId);
                if (!cached) return false;
                return Date.now() - cached.timestamp < maxAge;
            },

            clearCache: () => {
                set({ cache: new Map() }, false, 'catalogProductDetail/clearCache');
            },

            clearProductCache: (productId: number) => {
                const state = get();
                const newCache = new Map(state.cache);
                newCache.delete(productId);
                set({ cache: newCache }, false, 'catalogProductDetail/clearProductCache');
            },

            // Reset actions
            resetState: () => {
                // Clear selector caches
                cachedSelectedVariant = null;
                lastSelectedVariantKey = null;
                cachedCurrentImage = null;
                lastCurrentImageKey = null;
                cachedVariantImages = EMPTY_ARRAY;
                lastVariantId = null;

                set({
                    ...initialState,
                    cache: new Map(), // Reset cache as well
                }, false, 'catalogProductDetail/resetState');
            },
        }),
        {
            name: 'catalog-product-detail-store',
        }
    )
);

// 🎯 Selector hooks for better performance
export const useCatalogProductData = () =>
    useCatalogProductDetailStore((state) => state.productData);

export const useSelectedVariantIndex = () =>
    useCatalogProductDetailStore((state) => state.selectedVariantIndex);

export const useSlideDirection = () =>
    useCatalogProductDetailStore((state) => state.slideDirection);

export const useCurrentImageIndex = () =>
    useCatalogProductDetailStore((state) => state.currentImageIndex);

export const useCatalogProductLoading = () =>
    useCatalogProductDetailStore((state) => state.isLoading);

export const useCatalogProductError = () =>
    useCatalogProductDetailStore((state) => state.error);

export const useCatalogProductQuantity = () =>
    useCatalogProductDetailStore((state) => state.quantity);

// 🎯 Computed selectors with proper memoization

export const useSelectedVariant = () =>
    useCatalogProductDetailStore((state) => {
        const { productData, selectedVariantIndex } = state;

        // If no product data, return null immediately without caching
        if (!productData?.data) {
            return null;
        }

        const currentKey = `${productData.data.id}-${selectedVariantIndex}`;

        // If key hasn't changed, return cached variant
        if (currentKey === lastSelectedVariantKey && cachedSelectedVariant !== null) {
            return cachedSelectedVariant;
        }

        // Update cache
        const variant = productData.data.variants[selectedVariantIndex] || null;
        cachedSelectedVariant = variant;
        lastSelectedVariantKey = currentKey;

        return variant;
    });



export const useCurrentImage = () =>
    useCatalogProductDetailStore((state) => {
        const { productData, selectedVariantIndex, currentImageIndex } = state;

        // If no product data, return null immediately without caching
        if (!productData?.data) {
            return null;
        }

        const currentKey = `${productData.data.id}-${selectedVariantIndex}-${currentImageIndex}`;

        console.log('🔍 useCurrentImage called:', {
            currentKey,
            lastCurrentImageKey,
            hasCache: !!cachedCurrentImage
        });

        // If key hasn't changed, return cached image
        if (currentKey === lastCurrentImageKey && cachedCurrentImage !== null) {
            console.log('✅ Returning cached image');
            return cachedCurrentImage;
        }

        // Update cache
        const variant = productData.data.variants[selectedVariantIndex];
        const image = variant?.images[currentImageIndex] || null;
        cachedCurrentImage = image;
        lastCurrentImageKey = currentKey;

        console.log('🔄 Updating image cache');

        return image;
    });



export const useVariantImages = () =>
    useCatalogProductDetailStore((state) => {
        const { productData, selectedVariantIndex } = state;

        // If no product data, return static empty array to prevent re-renders
        if (!productData?.data) {
            return EMPTY_ARRAY;
        }

        const variant = productData.data.variants[selectedVariantIndex];

        // If variant hasn't changed, return cached images
        if (variant?.id === lastVariantId) {
            return cachedVariantImages;
        }

        // Update cache
        const images = variant?.images ?? EMPTY_ARRAY;
        cachedVariantImages = images;
        lastVariantId = variant?.id ?? null;

        return images;
    });
