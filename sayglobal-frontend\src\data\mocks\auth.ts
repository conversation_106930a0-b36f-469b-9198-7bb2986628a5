import { AuthUser, MembershipLevelIds } from '@/types';

// <PERSON><PERSON> - her rolden birer tane
export const mockUsers: (AuthUser & { password: string })[] = [
    {
        id: 1,
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'admin123',
        phone: '+90 ************',
        role: 'admin',
        membershipLevel: MembershipLevelIds.None,
        joinDate: '2023-01-01',
        isActive: true,
        isDealershipApproved: false
    },
    {
        id: 2,
        firstName: 'Mehmet',
        lastName: 'Elektronik',
        email: '<EMAIL>',
        password: 'dealer123',
        phone: '+90 ************',
        role: 'dealership',
        membershipLevel: MembershipLevelIds.Bronz,
        joinDate: '2023-06-15',
        isActive: true,
        isDealershipApproved: true
    },
    {
        id: 3,
        firstName: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        lastName: 'Ali',
        email: '<EMAIL>',
        password: 'cust123',
        phone: '+90 ************',
        role: 'customer',
        membershipLevel: MembershipLevelIds.None,
        joinDate: '2024-01-10',
        isActive: true,
        isDealershipApproved: false
    },
    {
        id: 4,
        firstName: 'Distribütör',
        lastName: 'Ayşe',
        email: '<EMAIL>',
        password: 'dist123',
        phone: '+90 ************',
        role: 'customer',
        membershipLevel: MembershipLevelIds.Gumus,
        joinDate: '2023-08-15',
        isActive: true,
        isDealershipApproved: false
    }
];

// Test için kolay giriş bilgileri
export const testCredentials = {
    admin: {
        email: '<EMAIL>',
        password: 'admin123'
    },
    dealership: {
        email: '<EMAIL>',
        password: 'dealer123'
    },
    distributor: {
        email: '<EMAIL>',
        password: 'dist123'
    },
    customer: {
        email: '<EMAIL>',
        password: 'cust123'
    }
}; 