'use client';

import React, { useState } from 'react';
import { teamMembers, distributorDashboard } from '@/data/mocks/distributor';
import TeamMemberDetailModal from '@/components/TeamMemberDetailModal';
import { TeamMember } from '@/types';
import {
    Users,
    Search,
    Filter,
    UserCheck,
    UserX,
    Calendar,
    Star,
    Award,
    TrendingUp,
    Target,
    ArrowUpDown,
    Mail,
    Phone,
    ExternalLink,
    User
} from 'lucide-react';

const MyTeamPage = () => {
    const [filteredMembers, setFilteredMembers] = useState(teamMembers);
    const [searchTerm, setSearchTerm] = useState('');
    const [levelFilter, setLevelFilter] = useState('all');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortBy, setSortBy] = useState<'name' | 'level' | 'points' | 'joinDate'>('joinDate');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
    const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

    // Modal açma fonksiyonu
    const handleViewMember = (member: TeamMember) => {
        setSelectedMember(member);
        setIsDetailModalOpen(true);
    };

    // Modal kapatma fonksiyonu
    const handleCloseModal = () => {
        setIsDetailModalOpen(false);
        setSelectedMember(null);
    };

    // Filtreleme fonksiyonu
    const handleFilter = () => {
        let filtered = teamMembers;

        if (searchTerm) {
            filtered = filtered.filter(member =>
                `${member.firstName} ${member.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (levelFilter !== 'all') {
            filtered = filtered.filter(member => member.level === parseInt(levelFilter));
        }

        if (statusFilter !== 'all') {
            filtered = filtered.filter(member =>
                statusFilter === 'active' ? member.isActive : !member.isActive
            );
        }

        setFilteredMembers(filtered);
    };

    // Sıralama fonksiyonu
    const handleSort = (field: 'name' | 'level' | 'points' | 'joinDate') => {
        const newOrder = sortBy === field && sortOrder === 'desc' ? 'asc' : 'desc';
        setSortBy(field);
        setSortOrder(newOrder);

        const sorted = [...filteredMembers].sort((a, b) => {
            let aValue: any, bValue: any;

            switch (field) {
                case 'name':
                    aValue = `${a.firstName} ${a.lastName}`;
                    bValue = `${b.firstName} ${b.lastName}`;
                    break;
                case 'level':
                    aValue = a.level;
                    bValue = b.level;
                    break;
                case 'points':
                    aValue = a.points;
                    bValue = b.points;
                    break;
                case 'joinDate':
                    aValue = new Date(a.joinDate);
                    bValue = new Date(b.joinDate);
                    break;
                default:
                    return 0;
            }

            if (newOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        setFilteredMembers(sorted);
    };

    // Filtreleri uygula
    React.useEffect(() => {
        handleFilter();
    }, [searchTerm, levelFilter, statusFilter]);

    // İstatistikler
    const activeMembers = teamMembers.filter(m => m.isActive).length;
    const totalPoints = teamMembers.reduce((sum, member) => sum + member.points, 0);
    const averagePoints = Math.round(totalPoints / teamMembers.length);

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                Ekip Yönetimi
                            </h1>
                            <p className="text-gray-600">
                                Ekibinizdeki üyeleri yönetin ve performanslarını takip edin
                            </p>
                        </div>
                        <div className="mt-4 sm:mt-0">
                            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Yeni Üye Davet Et
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Ekip</p>
                                <p className="text-2xl font-bold text-gray-900">{teamMembers.length}</p>
                            </div>
                            <div className="bg-purple-100 p-3 rounded-full">
                                <Users className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Aktif Üyeler</p>
                                <p className="text-2xl font-bold text-gray-900">{activeMembers}</p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-full">
                                <UserCheck className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Puan</p>
                                <p className="text-2xl font-bold text-gray-900">{totalPoints.toLocaleString('tr-TR')}</p>
                            </div>
                            <div className="bg-yellow-100 p-3 rounded-full">
                                <Star className="h-6 w-6 text-yellow-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Ortalama Puan</p>
                                <p className="text-2xl font-bold text-gray-900">{averagePoints}</p>
                            </div>
                            <div className="bg-blue-100 p-3 rounded-full">
                                <TrendingUp className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">

                        {/* Search */}
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Üye ara..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 placeholder:text-gray-600 text-black"
                            />
                        </div>

                        {/* Level Filter */}
                        <select
                            value={levelFilter}
                            onChange={(e) => setLevelFilter(e.target.value)}
                            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-600"
                        >
                            <option value="all">Tüm Seviyeler</option>
                            <option value="1">Seviye 1</option>
                            <option value="2">Seviye 2</option>
                            <option value="3">Seviye 3</option>
                        </select>

                        {/* Status Filter */}
                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-600"
                        >
                            <option value="all">Tüm Durumlar</option>
                            <option value="active">Aktif</option>
                            <option value="inactive">Pasif</option>
                        </select>

                        {/* Reset Filters */}
                        <button
                            onClick={() => {
                                setSearchTerm('');
                                setLevelFilter('all');
                                setStatusFilter('all');
                                setFilteredMembers(teamMembers);
                            }}
                            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                        >
                            Filtreleri Temizle
                        </button>
                    </div>
                </div>

                {/* Team Members Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {filteredMembers.map((member) => (
                        <div key={member.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">

                            {/* Card Header */}
                            <div className="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                            <span className="text-white font-bold text-lg">
                                                {member.firstName.charAt(0)}{member.lastName.charAt(0)}
                                            </span>
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-white">
                                                {member.firstName} {member.lastName}
                                            </h3>
                                            <p className="text-purple-100 text-sm">
                                                Seviye {member.level}
                                            </p>
                                        </div>
                                    </div>
                                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${member.isActive
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                        }`}>
                                        {member.isActive ? 'Aktif' : 'Pasif'}
                                    </span>
                                </div>
                            </div>

                            {/* Card Body */}
                            <div className="p-6">
                                <div className="space-y-4">

                                    {/* Stats */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="text-center">
                                            <div className="flex items-center justify-center mb-1">
                                                <Star className="h-4 w-4 text-yellow-500 mr-1" />
                                                <span className="text-lg font-bold text-gray-900">
                                                    {member.points}
                                                </span>
                                            </div>
                                            <p className="text-sm text-gray-500">Puan</p>
                                        </div>
                                        <div className="text-center">
                                            <div className="flex items-center justify-center mb-1">
                                                <Calendar className="h-4 w-4 text-blue-500 mr-1" />
                                                <span className="text-lg font-bold text-gray-900">
                                                    {Math.floor((new Date().getTime() - new Date(member.joinDate).getTime()) / (1000 * 60 * 60 * 24))}
                                                </span>
                                            </div>
                                            <p className="text-sm text-gray-500">Gün</p>
                                        </div>
                                    </div>

                                    {/* Join Date */}
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-gray-500">Katılım Tarihi:</span>
                                        <span className="font-medium text-gray-900">
                                            {new Date(member.joinDate).toLocaleDateString('tr-TR')}
                                        </span>
                                    </div>

                                    {/* Level Badge */}
                                    <div className="flex items-center justify-center">
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${member.level === 1
                                            ? 'bg-green-100 text-green-800'
                                            : member.level === 2
                                                ? 'bg-blue-100 text-blue-800'
                                                : 'bg-gray-100 text-gray-800'
                                            }`}>
                                            <Award className="h-3 w-3 mr-1" />
                                            Seviye {member.level} Distribütör
                                        </span>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex space-x-2 pt-4">
                                        <button
                                            onClick={() => handleViewMember(member)}
                                            className="flex-1 bg-purple-100 text-purple-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-200 transition-colors"
                                        >
                                            <User className="h-4 w-4 inline mr-1" />
                                            Detaylar
                                        </button>
                                        <button className="flex-1 bg-blue-100 text-blue-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors">
                                            <Mail className="h-4 w-4 inline mr-1" />
                                            Mesaj
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Empty State */}
                {filteredMembers.length === 0 && (
                    <div className="text-center py-12">
                        <div className="text-gray-500">
                            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Üye bulunamadı
                            </h3>
                            <p className="text-gray-500">
                                Seçilen kriterlere uygun üye bulunmuyor.
                            </p>
                        </div>
                    </div>
                )}

                {/* Detailed Table View Toggle */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-xl font-semibold text-gray-900">Detaylı Ekip Listesi</h3>
                        <div className="flex space-x-2">
                            <button
                                onClick={() => handleSort('name')}
                                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                            >
                                İsim <ArrowUpDown className="h-4 w-4 ml-1" />
                            </button>
                            <button
                                onClick={() => handleSort('points')}
                                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                            >
                                Puan <ArrowUpDown className="h-4 w-4 ml-1" />
                            </button>
                            <button
                                onClick={() => handleSort('joinDate')}
                                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                            >
                                Tarih <ArrowUpDown className="h-4 w-4 ml-1" />
                            </button>
                        </div>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Üye
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Seviye
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Puan
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Katılım Tarihi
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Durum
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredMembers.map((member) => (
                                    <tr key={member.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full flex items-center justify-center mr-3">
                                                    <span className="text-white font-semibold text-sm">
                                                        {member.firstName.charAt(0)}{member.lastName.charAt(0)}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {member.firstName} {member.lastName}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${member.level === 1
                                                ? 'bg-green-100 text-green-800'
                                                : member.level === 2
                                                    ? 'bg-blue-100 text-blue-800'
                                                    : 'bg-gray-100 text-gray-800'
                                                }`}>
                                                Seviye {member.level}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <Star className="h-4 w-4 text-yellow-500 mr-1" />
                                                <span className="text-sm font-medium text-gray-900">
                                                    {member.points.toLocaleString('tr-TR')}
                                                </span>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {new Date(member.joinDate).toLocaleDateString('tr-TR')}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${member.isActive
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                                }`}>
                                                {member.isActive ? 'Aktif' : 'Pasif'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex space-x-2">
                                                <button
                                                    onClick={() => handleViewMember(member)}
                                                    className="text-purple-600 hover:text-purple-900 transition-colors"
                                                >
                                                    Detaylar
                                                </button>
                                                <button className="text-blue-600 hover:text-blue-900 transition-colors">
                                                    Mesaj
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Back to Panel */}
                <div className="mt-8 text-center">
                    <a
                        href="/panel"
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
                    >
                        Distribütör Paneline Dön
                    </a>
                </div>

            </div>

            {/* Team Member Detail Modal */}
            <TeamMemberDetailModal
                member={selectedMember}
                isOpen={isDetailModalOpen}
                onClose={handleCloseModal}
            />
        </div>
    );
};

export default MyTeamPage; 