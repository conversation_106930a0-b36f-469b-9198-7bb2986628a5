# SayGlobal Backend - <PERSON>eri<PERSON><PERSON><PERSON> Yapısı

## <PERSON><PERSON> Bakış
Bu belge, SayGlobal MLM E-ticaret sisteminin PostgreSQL veritabanı yapısını detaylandırmaktadır. Sistem ASP.NET Core Identity ile kurulmuş ve 31 ana tablo içermektedir.

## Ana Modüller

### 1. 🔐 Kimlik ve Yetkilendirme (ASP.NET Identity)
**AspNetUsers** - <PERSON> tablosu
- `Id` (PK): integer - Kullanıcı ID'si
- `FirstName`: text - Ad
- `LastName`: text - Soyad
- `Email`: varchar(256) - E-posta adresi
- `UserName`: varchar(256) - Kullan<PERSON><PERSON><PERSON> adı
- `PhoneNumber`: text - Telefon numarası
- `IsActive`: boolean - Aktiflik durumu
- `RegisteredAt`: timestamptz - Kayıt tarihi
- `MembershipLevelId`: integer - Üyelik seviyesi ID'si
- `CareerRankId`: integer - <PERSON><PERSON><PERSON> sıralaması ID'si
- `ReferenceId`: integer - <PERSON><PERSON><PERSON> kull<PERSON>ıcı ID'si
- Diğer ASP.NET Identity alanları (password, security stamp, vb.)

**AspNetRoles** - Roller
- `Id` (PK): integer
- `Name`: varchar(256)
- `NormalizedName`: varchar(256)

**AspNetUserRoles** - Kullanıcı-Rol ilişkisi
- `UserId` (PK, FK): AspNetUsers
- `RoleId` (PK, FK): AspNetRoles

**AspNetUserClaims, AspNetUserLogins, AspNetUserTokens, AspNetRoleClaims** - Identity yardımcı tabloları

**RefreshTokens** - JWT refresh token'ları
- `Id` (PK): integer
- `Token`: text
- `UserId` (FK): AspNetUsers
- `ExpiryDate`: timestamptz
- `IsRevoked`: boolean
- `IpAddress`: text
- `UserAgent`: text

### 2. 🛒 E-ticaret Modülü

#### Kategori ve Ürün Yönetimi
**Category** - Ana kategoriler
- `Id` (PK): integer
- `Name`: varchar(100)

**SubCategory** - Alt kategoriler
- `Id` (PK): integer
- `Name`: varchar(100)
- `CategoryId` (FK): Category

**Brand** - Markalar
- `Id` (PK): integer
- `Name`: varchar(100)
- `LogoUrl`: varchar(500)

**BrandCategory** - Marka-Kategori ilişkisi
- `Id` (PK): integer
- `BrandId` (FK): Brand
- `CategoryId` (FK): Category

**Product** - Ürünler
- `Id` (PK): integer
- `Name`: varchar(255)
- `BrandId` (FK): Brand
- `CategoryId` (FK): Category
- `Stock`: integer
- `Description`: text
- `Status`: smallint

**ProductImage** - Ürün görselleri
- `Id` (PK): integer
- `ProductId` (FK): Product
- `ImageUrl`: varchar(500)
- `IsMain`: boolean
- `SortOrder`: integer

#### Özellik Sistemi
**FeatureDefinition** - Özellik tanımları (Renk, Beden, vb.)
- `Id` (PK): integer
- `Name`: varchar(100)
- `Description`: varchar(255)

**FeatureValue** - Özellik değerleri (Kırmızı, XL, vb.)
- `Id` (PK): integer
- `FeatureDefinitionId` (FK): FeatureDefinition
- `Value`: varchar(100)

**CategoryFeature** - Kategori-Özellik ilişkisi
- `Id` (PK): integer
- `CategoryId` (FK): Category
- `FeatureDefinitionId` (FK): FeatureDefinition

**ProductFeature** - Ürün-Özellik değeri ilişkisi
- `Id` (PK): integer
- `ProductId` (FK): Product
- `FeatureValueId` (FK): FeatureValue

#### Sepet ve Sipariş Sistemi
**Cart** - Kullanıcı sepetleri
- `Id` (PK): integer
- `UserId` (FK): AspNetUsers
- `CreatedAt`: timestamp

**CartItem** - Sepet öğeleri
- `Id` (PK): integer
- `CartId` (FK): Cart
- `ProductId` (FK): Product
- `ProductFeatureId` (FK): ProductFeature (nullable)
- `Quantity`: integer
- `UnitPrice`: decimal(10,2)

**Order** - Siparişler
- `Id` (PK): integer
- `UserId` (FK): AspNetUsers
- `OrderDate`: timestamp
- `TotalAmount`: decimal(10,2)
- `Status`: integer

**OrderItem** - Sipariş öğeleri
- `Id` (PK): integer
- `OrderId` (FK): Order
- `ProductId` (FK): Product
- `Quantity`: integer
- `UnitPrice`: decimal(10,2)

### 3. 🎯 MLM Sistemi

#### Üyelik Seviyələri
**MemberShipLevels** - Üyelik paketleri
- `Id` (PK): integer
- `Name`: text
- `Price`: integer
- `DiscountRate`: integer - İndirim oranı
- `ReferralRate`: integer - Referans komisyon oranı
- `MatchIncomeRate`: integer - Eşleşme gelir oranı (nullable)

**UserMemberships** - Kullanıcı üyelik geçmişi
- `Id` (PK): integer
- `UserId` (FK): AspNetUsers
- `MembershipLevelId` (FK): MemberShipLevels
- `Time`: timestamptz

#### Kariyer Sistemi
**CareerLevels** - Kariyer seviyeleri
- `Id` (PK): integer
- `Name`: text
- `ShortBranchPV`: integer - Kısa dal PV gereksinimi
- `CareerReward`: integer - Kariyer ödülü (nullable)

**CareerDepthPercents** - Kariyer derinlik komisyon oranları
- `Id` (PK): integer
- `CareerId` (FK): CareerLevels
- `DepthLevel`: integer - Derinlik seviyesi
- `Percent`: integer - Komisyon yüzdesi
- **UNIQUE**: (CareerId, DepthLevel)

**UserCareers** - Kullanıcı kariyer geçmişi
- `Id` (PK): integer
- `UserId` (FK): AspNetUsers
- `CareerId` (FK): CareerLevels
- `CreatedAt`: timestamptz

#### Puan Sistemi
**Points** - Aylık kullanıcı puanları
- `Id` (PK): integer
- `UserId` (FK): AspNetUsers
- `Pv`: integer - Personal Volume
- `Cv`: integer - Commision Volume
- `Month`: integer
- `Year`: integer
- **UNIQUE**: (UserId, Month, Year)

### 4. 🔗 Referans ve Bağlantı Systemi

**RefLinks** - Referans bağlantıları
- `Id` (PK): integer
- `UserId` (FK): AspNetUsers
- `Content`: text - Link içeriği
- `StartTime`: timestamptz
- `EndTime`: timestamptz
- `Type`: integer - Link tipi

### 5. 📊 Sistem Yönetimi

**Logs** - Sistem logları
- `Id` (PK): integer
- `Timestamp`: timestamptz
- `Path`: text - İstek yolu
- `Method`: text - HTTP metodu
- `QueryString`: text (nullable)
- `Body`: text (nullable)
- `UserId`: text (nullable)
- `ErrorMessage`: text (nullable)
- `StackTrace`: text (nullable)
- `StatusCode`: integer (nullable)
- `RemoteIp`: text (nullable)
- `UserAgent`: text (nullable)

**__EFMigrationsHistory** - Entity Framework migration geçmişi
- `MigrationId` (PK): varchar(150)
- `ProductVersion`: varchar(32)

## Temel İlişkiler

### Kullanıcı İlişkileri
- **AspNetUsers** → **Cart** (1:N)
- **AspNetUsers** → **Order** (1:N)
- **AspNetUsers** → **Points** (1:N)
- **AspNetUsers** → **UserCareers** (1:N)
- **AspNetUsers** → **UserMemberships** (1:N)
- **AspNetUsers** → **RefLinks** (1:N)

### Ürün İlişkileri
- **Category** → **Product** (1:N)
- **Category** → **SubCategory** (1:N)
- **Brand** → **Product** (1:N)
- **Product** → **ProductImage** (1:N)
- **Product** → **ProductFeature** (1:N)
- **Product** → **CartItem** (1:N)
- **Product** → **OrderItem** (1:N)

### MLM İlişkileri
- **CareerLevels** → **CareerDepthPercents** (1:N)
- **CareerLevels** → **UserCareers** (1:N)
- **MemberShipLevels** → **UserMemberships** (1:N)

### Özellik İlişkileri
- **FeatureDefinition** → **FeatureValue** (1:N)
- **FeatureDefinition** → **CategoryFeature** (1:N)
- **FeatureValue** → **ProductFeature** (1:N)

## Önemli Notlar

1. **MLM Yapısı**: Sistem çok seviyeli pazarlama modeli için tasarlanmış
2. **Referans Sistemi**: Her kullanıcının `ReferenceId` alanı ile referans zinciri
3. **Puan Sistemi**: PV (Personal Volume) ve CV (Commission Volume) takibi
4. **Kariyer Sistemi**: Derinlik bazlı komisyon hesaplama
5. **Üyelik Seviyeleri**: Farklı indirim ve komisyon oranları
6. **Ürün Özellikleri**: Esnek özellik sistemi (renk, beden, vb.)
7. **Sepet Sistemi**: Kullanıcı bazlı sepet yönetimi
8. **Sipariş Takibi**: Detaylı sipariş ve öğe takibi
9. **Log Sistemi**: Kapsamlı API çağrı ve hata takibi

## Veri Tipleri ve Kısıtlamalar

- **Primary Key'ler**: Çoğunlukla `integer` tipinde auto-increment
- **Foreign Key'ler**: Referential integrity korunmuş
- **Unique Constraints**: 
  - Points: (UserId, Month, Year)
  - CareerDepthPercents: (CareerId, DepthLevel)
- **Decimal Precision**: Para birimi alanları `decimal(10,2)` formatında
- **Timestamp'ler**: Timezone aware (`timestamptz`) kullanımı

Bu yapı, kapsamlı bir MLM e-ticaret platformu için gerekli tüm temel bileşenleri içermektedir.

---

# API Endpoint'leri ve Servisler

Aşağıda [SayGlobalWeb API](https://sayglobalweb.com/swagger/v1/swagger.json) swagger dokümantasyonundan alınan mevcut endpoint'ler listelenmektedir.

## 🔐 Account Controller - Kimlik Doğrulama API'leri

### Authentication Endpoints
- **POST** `/api/Account/register` - Kullanıcı kaydı
  - **Request**: `RegisterrRequest`
    - `email`: string
    - `password`: string
    - `firstName`: string
    - `lastName`: string
    - `phoneNumber`: string
    - `referansCode`: string (nullable)

- **POST** `/api/Account/login` - Kullanıcı girişi
  - **Request**: `LoginnRequest`
    - `email`: string
    - `password`: string

- **POST** `/api/Account/refresh` - JWT token yenileme
  - **Request**: string (refresh token)

- **POST** `/api/Account/logout` - Kullanıcı çıkışı
  - **Request**: `LogoutRequest`
    - `refreshToken`: string (nullable)

### Admin Management
- **POST** `/api/Account/make-admin` - Kullanıcıyı admin yapma
  - **Request**: string (userId veya email)

### MLM Reference System
- **POST** `/api/Account/account/add-reference` - Referans kodu ekleme
  - **Request**: `AddReferenceCodeRequest`
    - `referansCode`: string (nullable)

### Debug/Test Endpoints
- **GET** `/api/Account/test` - Test endpoint'i
- **GET** `/api/Account/debug-claims` - JWT claims debug

## 🛠️ AdminTools Controller - Admin Araçları

- **POST** `/api/admin/tools/create-roles` - Sistem rollerini oluşturma

## 📦 Products Controller - Ürün Yönetimi API'leri

### Ürün İşlemleri
- **POST** `/api/Products/addproduct` - Yeni ürün ekleme
  - **Request**: `CreateProductRequest`
    - `name`: string
    - `brandId`: integer
    - `categoryId`: integer
    - `stock`: integer
    - `description`: string (nullable)
    - `featureValueIds`: integer[] (nullable)

- **POST** `/api/Products/productstatus` - Ürün durumu değiştirme (onay/red)
  - **Request**: `ChangeProductStatusRequest`
    - `productId`: integer
    - `isApproved`: boolean

### Ürün Görsel Yönetimi
- **POST** `/api/Products/addimage` - Ürüne görsel ekleme
  - **Request**: multipart/form-data
    - `ProductId`: integer
    - `IsMain`: boolean
    - `SortOrder`: integer
    - `File`: binary (image file)

- **DELETE** `/api/Products/image/{imageId}` - Ürün görselini silme
  - **Parameter**: `imageId` (integer, path)

- **PUT** `/api/Products/image/replace/{imageId}` - Ürün görselini değiştirme
  - **Parameter**: `imageId` (integer, path)
  - **Request**: multipart/form-data
    - `file`: binary (image file)

### Marka ve Kategori Yönetimi
- **POST** `/api/Products/createbrand` - Yeni marka oluşturma
  - **Request**: `CreateBrandRequest`
    - `name`: string (required, max 100 char)
    - `logoUrl`: string (nullable, max 500 char)
    - `categoryIds`: integer[] (nullable)

- **POST** `/api/Products/createcategory` - Yeni kategori oluşturma
  - **Request**: `CreateCategoryRequest`
    - `name`: string (required, max 100 char)
    - `subCategoryNames`: string[] (nullable)
    - `featureIds`: integer[] (nullable)

### Özellik Sistemi Yönetimi
- **POST** `/api/Products/createdefinition` - Yeni özellik tanımı oluşturma
  - **Request**: `CreateFeatureDefinitionRequest`
    - `name`: string (required, max 100 char)
    - `description`: string (nullable, max 255 char)
    - `initialValues`: string[] (nullable)

- **POST** `/api/Products/createvalue` - Özellik değeri oluşturma
  - **Request**: `CreateFeatureValueRequest`
    - `featureDefinitionId`: integer
    - `value`: string (nullable)

- **POST** `/api/Products/createfeature` - Kategori-özellik ilişkisi oluşturma
  - **Request**: `CreateCategoryFeatureRequest`
    - `categoryId`: integer
    - `featureDefinitionId`: integer

- **POST** `/api/Products/createproductvalue` - Ürün-özellik değeri ilişkisi
  - **Request**: `CreateProductFeatureRequest`
    - `productId`: integer
    - `featureValueId`: integer

## 🔒 Güvenlik Yapılandırması

- **Authentication**: Bearer Token (JWT)
- **Authorization Header**: `Bearer {token}`
- **Token Format**: JWT
- **Security Scheme**: HTTP Bearer

## 📋 Request/Response Modelleri

Tüm API endpoint'leri JSON formatında veri alışverişi yapmaktadır. Content-Type seçenekleri:
- `application/json`
- `text/json`
- `application/*+json`

Multipart dosya yükleme endpoint'leri:
- `multipart/form-data`

## ⚠️ Eksik Endpoint'ler

Mevcut API'de şu önemli endpoint'ler henüz bulunmuyor:
- **GET** operasyonları (ürün listesi, detay, kategori listesi, vb.)
- **Cart/Sepet** API'leri
- **Order/Sipariş** API'leri
- **MLM/Kariyer** sistemi API'leri
- **User Profile** yönetimi API'leri
- **Points/Puan** sistemi API'leri
- **Reports/Raporlama** API'leri

## 🚀 API Entegrasyon Planı

1. **Öncelikli Entegrasyonlar**:
   - Authentication sistemi (login/register/refresh)
   - Ürün listeleme ve detay API'leri (henüz mevcut değil)
   - Kategori listeleme API'leri (henüz mevcut değil)

2. **İkinci Aşama**:
   - Sepet yönetimi API'leri
   - Sipariş işlemleri API'leri
   - MLM/Referans sistemi API'leri

3. **Üçüncü Aşama**:
   - Admin paneli API'leri
   - Raporlama API'leri
   - İleri seviye MLM işlemleri 