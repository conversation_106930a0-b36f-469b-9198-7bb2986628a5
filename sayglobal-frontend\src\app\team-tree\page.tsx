'use client';

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { teamTreeData, teamTreeStats } from '@/data/mocks/distributor';
import { TeamTreeNode } from '@/types';
import PendingPlacementModal from '@/components/PendingPlacementModal';
import PlacementSuccessModal from '@/components/PlacementSuccessModal';
import { hierarchy, tree as d3Tree } from 'd3-hierarchy';
import {
    Users,
    TrendingUp,
    Star,
    Award,
    Activity,
    Calendar,
    ChevronDown,
    ChevronUp,
    User,
    Crown,
    UserPlus,
    ZoomIn,
    ZoomOut,
    RotateCcw
} from 'lucide-react';

// D3 için hiyerarşi düğümü tipi
interface HierarchyPointNode<T> {
    x: number;
    y: number;
    data: T;
    children?: Array<HierarchyPointNode<T>>;
    parent?: HierarchyPointNode<T> | null;
}

// <PERSON><PERSON><PERSON><PERSON> bekleyen üye tipi
interface PendingMember {
    id: number;
    name: string;
    email: string;
    phone: string;
    joinDate: string;
    sponsorId: number;
    sponsorName: string;
}

// Mock data - yerleşim bekleyen üyeler
const pendingMembersData: PendingMember[] = [
    {
        id: 201,
        name: 'Ayşe Demir',
        email: '<EMAIL>',
        phone: '+90 ************',
        joinDate: '2024-01-15',
        sponsorId: 1,
        sponsorName: 'Sen (Distribütör)'
    },
    {
        id: 202,
        name: 'Mehmet Kaya',
        email: '<EMAIL>',
        phone: '+90 ************',
        joinDate: '2024-01-16',
        sponsorId: 2,
        sponsorName: 'Ahmet Yılmaz'
    },
    {
        id: 203,
        name: 'Fatma Öz',
        email: '<EMAIL>',
        phone: '+90 ************',
        joinDate: '2024-01-17',
        sponsorId: 1,
        sponsorName: 'Sen (Distribütör)'
    }
];

// Ekip ağacı bileşeni
const TeamTreeNodeComponent: React.FC<{
    d3Node: HierarchyPointNode<TeamTreeNode>;
    onToggleExpand: (nodeId: number) => void;
    isExpanded: boolean;
}> = ({ d3Node, onToggleExpand, isExpanded }) => {

    const node = d3Node.data;
    const hasChildren = node.children && (node.children.left || node.children.right);

    const handleToggleExpand = () => {
        onToggleExpand(node.id);
    };

    return (
        <div
            className="absolute transition-all duration-500"
            style={{
                left: `${d3Node.x}px`,
                top: `${d3Node.y}px`,
                transform: 'translate(-50%, -50%)',
            }}
        >
            <div
                className={`relative bg-white rounded-xl shadow-lg p-4 w-64 border-2 transition-all duration-300 hover:shadow-xl ${node.isActive
                    ? 'border-green-400 hover:border-green-500'
                    : 'border-red-300 hover:border-red-400'
                    } ${node.id === 1 ? 'ring-2 ring-blue-400' : ''}`}
            >
                {/* Kişi bilgileri */}
                <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${node.id === 1
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                        : node.isActive
                            ? 'bg-gradient-to-r from-green-400 to-green-600'
                            : 'bg-gradient-to-r from-gray-400 to-gray-600'
                        }`}>
                        {node.id === 1 ? (
                            <Crown className="h-6 w-6 text-white" />
                        ) : (
                            <User className="h-6 w-6 text-white" />
                        )}
                    </div>
                    <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-sm">{node.name}</h3>
                        <p className="text-xs text-gray-500">Seviye {node.level}</p>
                        {/* Kol badge'i - sadece root node değilse göster */}
                        {node.id !== 1 && node.position && (
                            <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${node.position === 'left'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-purple-100 text-purple-800'
                                }`}>
                                {node.position === 'left' ? 'Sol Kol' : 'Sağ Kol'}
                            </div>
                        )}
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${node.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                        }`}>
                        {node.isActive ? 'Aktif' : 'Pasif'}
                    </div>
                </div>

                {/* İstatistikler */}
                <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-blue-50 p-2 rounded">
                        <div className="font-semibold text-blue-700">{node.points}</div>
                        <div className="text-blue-600">Puan</div>
                    </div>
                    <div className="bg-green-50 p-2 rounded">
                        <div className="font-semibold text-green-700">
                            ₺{node.totalEarnings.toLocaleString('tr-TR')}
                        </div>
                        <div className="text-green-600">Kazanç</div>
                    </div>
                </div>

                {/* Katılma tarihi */}
                <div className="mt-2 pt-2 border-t border-gray-100">
                    <div className="flex items-center text-xs text-gray-500">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(node.joinDate).toLocaleDateString('tr-TR')}
                    </div>
                </div>

                {/* Genişletme butonu */}
                {hasChildren && (
                    <button
                        onClick={handleToggleExpand}
                        className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-white border-2 border-gray-300 rounded-full p-1 hover:border-blue-400 transition-colors"
                    >
                        {isExpanded ? (
                            <ChevronUp className="h-4 w-4 text-gray-600" />
                        ) : (
                            <ChevronDown className="h-4 w-4 text-gray-600" />
                        )}
                    </button>
                )}
            </div>
        </div>
    );
};

// SVG çizgi bileşeni
const TreeConnection: React.FC<{
    source: HierarchyPointNode<TeamTreeNode>;
    destination: HierarchyPointNode<TeamTreeNode>;
}> = ({ source, destination }) => {

    const x1 = source.x;
    const y1 = source.y;
    const x2 = destination.x;
    const y2 = destination.y;

    const pathData = `M ${x1} ${y1} C ${x1} ${(y1 + y2) / 2}, ${x2} ${(y1 + y2) / 2}, ${x2} ${y2}`;

    return (
        <path d={pathData} fill="none" stroke="#d1d5db" strokeWidth="2" className="transition-all duration-500" />
    );
};

const TeamTreePage = () => {
    const stats = teamTreeStats;
    const [treeData, setTreeData] = useState<TeamTreeNode>(teamTreeData);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [pendingMembers, setPendingMembers] = useState<PendingMember[]>(pendingMembersData);
    const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
    const [successMessage, setSuccessMessage] = useState({ memberName: '', position: 'sol' as 'sol' | 'sağ' });

    // Zoom state - başlangıç %52
    const [zoom, setZoom] = useState(0.52);
    const [panX, setPanX] = useState(0);
    const [panY, setPanY] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    // Pan/drag state
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [lastPan, setLastPan] = useState({ x: 0, y: 0 });

    // İlk yükleme kontrolü
    const [isInitialLoad, setIsInitialLoad] = useState(true);

    // Expanded nodes'ları takip et - başlangıçta level 0 ve 1 açık
    const [collapsedNodes, setCollapsedNodes] = useState<Set<number>>(() => {
        const collapsed = new Set<number>();
        // Gerekirse başlangıçta bazı nodeları kapalı tutmak için buraya mantık eklenebilir.
        return collapsed;
    });

    // Expand/collapse toggle
    const handleToggleExpand = useCallback((nodeId: number) => {
        setCollapsedNodes(prev => {
            const newSet = new Set(prev);
            if (newSet.has(nodeId)) {
                newSet.delete(nodeId);
            } else {
                newSet.add(nodeId);
            }
            return newSet;
        });
    }, []);

    const layout = useMemo(() => {
        const nodeWidth = 340; // Kart genişliği + padding
        const nodeHeight = 240; // Kart yüksekliği + padding

        const treeLayout = d3Tree<TeamTreeNode>()
            .nodeSize([nodeWidth, nodeHeight]) // size yerine nodeSize kullan
            .separation((a, b) => {
                // Aynı parent'a sahip kardeş düğümler için daha fazla boşluk
                if (a.parent === b.parent) {
                    return 1.5; // Kardeş düğümler arası mesafe
                }
                // Farklı dallardan gelen düğümler için daha da fazla boşluk
                return 2.0;
            });

        const root = hierarchy(treeData, (d: TeamTreeNode) => {
            if (collapsedNodes.has(d.id)) {
                return undefined;
            }
            const children: TeamTreeNode[] = [];
            if (d.children?.left) children.push(d.children.left);
            if (d.children?.right) children.push(d.children.right);
            return children.length > 0 ? children : undefined;
        });

        const layoutResult = treeLayout(root);

        // Debug: Layout sonuçlarını kontrol et
        console.log('D3 Layout sonuçları:', {
            nodeCount: layoutResult.descendants().length,
            nodePositions: layoutResult.descendants().map(n => ({
                id: n.data.id,
                name: n.data.name,
                x: Math.round(n.x),
                y: Math.round(n.y),
                depth: n.depth
            }))
        });

        return layoutResult;
    }, [treeData, collapsedNodes]);

    // Tüm visible nodeları bul (collapsed olanları hariç tut)
    const { allNodes, allLinks } = useMemo(() => {
        const nodes: HierarchyPointNode<TeamTreeNode>[] = [];
        const links: { source: HierarchyPointNode<TeamTreeNode>, target: HierarchyPointNode<TeamTreeNode> }[] = [];

        const traverse = (node: HierarchyPointNode<TeamTreeNode>) => {
            nodes.push(node);

            if (!collapsedNodes.has(node.data.id) && node.children) {
                node.children.forEach(child => {
                    links.push({ source: node, target: child });
                    traverse(child);
                });
            }
        };

        traverse(layout);
        return { allNodes: nodes, allLinks: links };
    }, [layout, collapsedNodes]);

    // Container boyutlarını hesapla
    const { width, height, offsetX, offsetY } = useMemo(() => {
        if (allNodes.length === 0) {
            return { width: 800, height: 600, offsetX: 0, offsetY: 0 };
        }

        const margin = 150; // Daha fazla margin
        const nodeWidth = 340; // Layout'ta kullandığımız değer
        const nodeHeight = 240; // Layout'ta kullandığımız değer

        // Node'ların gerçek pozisyonlarını al
        const minX = Math.min(...allNodes.map(d => d.x));
        const maxX = Math.max(...allNodes.map(d => d.x));
        const minY = Math.min(...allNodes.map(d => d.y));
        const maxY = Math.max(...allNodes.map(d => d.y));

        // nodeSize kullandığımız için koordinatlar node merkezlerini temsil ediyor
        // Bu yüzden node boyutlarının yarısını eklememiz gerekiyor
        const totalWidth = (maxX - minX) + nodeWidth + (2 * margin);
        const totalHeight = (maxY - minY) + nodeHeight + (2 * margin);

        // Offset hesaplaması - node'ları merkeze almak için
        const offsetX = -minX + (nodeWidth / 2) + margin;
        const offsetY = -minY + (nodeHeight / 2) + margin;

        console.log('Container boyutları hesaplandı:', {
            nodeCount: allNodes.length,
            bounds: { minX, maxX, minY, maxY },
            totalWidth,
            totalHeight,
            offsetX,
            offsetY,
            nodePositions: allNodes.map(d => ({
                id: d.data.id,
                x: Math.round(d.x),
                y: Math.round(d.y)
            }))
        });

        return {
            width: totalWidth,
            height: totalHeight,
            offsetX,
            offsetY
        };
    }, [allNodes]);

    // Sadece ilk yükleme için merkeze alma - direkt hesaplama
    useEffect(() => {
        if (isInitialLoad && containerRef.current && allNodes.length > 0 && width > 0 && height > 0 && offsetX !== undefined && offsetY !== undefined) {
            const containerWidth = containerRef.current.clientWidth;
            const containerHeight = containerRef.current.clientHeight;

            if (containerWidth === 0 || containerHeight === 0) return;

            // Ağacın gerçek sınırlarını hesapla
            const minX = Math.min(...allNodes.map(d => d.x));
            const maxX = Math.max(...allNodes.map(d => d.x));
            const minY = Math.min(...allNodes.map(d => d.y));
            const maxY = Math.max(...allNodes.map(d => d.y));

            // Ağacın genişlik ve yüksekliği
            const treeWidth = maxX - minX;
            const treeHeight = maxY - minY;

            // Ağacın sol üst köşesi (offset'ler dahil)
            const treeLeft = minX + offsetX;
            const treeTop = minY + offsetY;

            // Container merkezine getirmek için gerekli pan değerleri
            const newPanX = (containerWidth / 2 - treeLeft - treeWidth / 2) / zoom;
            const newPanY = (containerHeight / 2 - treeTop - treeHeight / 2) / zoom;

            console.log('Direkt merkeze alma:', {
                containerSize: { width: containerWidth, height: containerHeight },
                treeBounds: { minX, maxX, minY, maxY },
                treeSize: { width: treeWidth, height: treeHeight },
                treePosition: { left: treeLeft, top: treeTop },
                offset: { x: offsetX, y: offsetY },
                zoom,
                calculatedPan: { x: newPanX, y: newPanY }
            });

            // Direkt olarak tüm değerleri ayarla
            setPanX(newPanX);
            setPanY(newPanY);
            setLastPan({ x: newPanX, y: newPanY });
            setIsInitialLoad(false); // Artık hiç çalışmasın
        }
    }, [isInitialLoad, allNodes.length, width, height, offsetX, offsetY, zoom]);

    // Container'a wheel event listener ekle (sayfa scroll'unu engelle ama zoom'a izin ver)
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const handleWheelZoom = (e: WheelEvent) => {
            e.preventDefault();
            e.stopPropagation();

            // Zoom faktörü
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const newZoom = Math.max(0.3, Math.min(3, zoom * delta));

            // Zoom'u güncelle
            setZoom(newZoom);

            return false;
        };

        // Passive: false ile event'i tamamen kontrol et
        container.addEventListener('wheel', handleWheelZoom, { passive: false });

        return () => {
            container.removeEventListener('wheel', handleWheelZoom);
        };
    }, [zoom]); // zoom dependency ekle

    // Zoom butonları için pozisyon koruyarak zoom
    const handleZoomIn = useCallback(() => {
        const newZoom = Math.min(zoom * 1.2, 3);
        setZoom(newZoom);
        // Pan değerlerini değiştirme
    }, [zoom]);

    const handleZoomOut = useCallback(() => {
        const newZoom = Math.max(zoom / 1.2, 0.3);
        setZoom(newZoom);
        // Pan değerlerini değiştirme
    }, [zoom]);

    // Mouse drag events
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        if (e.button === 0) { // Sol tık
            setIsDragging(true);
            setDragStart({ x: e.clientX, y: e.clientY });
            setLastPan({ x: panX, y: panY });
            e.preventDefault();
        }
    }, [panX, panY]);

    const handleMouseMove = useCallback((e: React.MouseEvent) => {
        if (isDragging) {
            const deltaX = (e.clientX - dragStart.x) / zoom;
            const deltaY = (e.clientY - dragStart.y) / zoom;

            setPanX(lastPan.x + deltaX);
            setPanY(lastPan.y + deltaY);
        }
    }, [isDragging, dragStart, lastPan, zoom]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
    }, []);

    // Touch zoom için state
    const [lastTouchDistance, setLastTouchDistance] = useState<number | null>(null);
    const [touchStartPan, setTouchStartPan] = useState<{ x: number, y: number } | null>(null);

    // Touch events (hem zoom hem drag için)
    const handleTouchStart = useCallback((e: React.TouchEvent) => {
        if (e.touches.length === 2) {
            // İki parmak - zoom
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const distance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            setLastTouchDistance(distance);
        } else if (e.touches.length === 1) {
            // Tek parmak - drag
            const touch = e.touches[0];
            setTouchStartPan({ x: touch.clientX, y: touch.clientY });
            setLastPan({ x: panX, y: panY });
        }
    }, [panX, panY]);

    const handleTouchMove = useCallback((e: React.TouchEvent) => {
        if (e.touches.length === 2 && lastTouchDistance) {
            // İki parmak - zoom
            e.preventDefault();
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const distance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );

            const scale = distance / lastTouchDistance;
            const newZoom = Math.max(0.3, Math.min(3, zoom * scale));

            // Sadece zoom değiştir, pan değerlerini koru
            setZoom(newZoom);
            setLastTouchDistance(distance);
        } else if (e.touches.length === 1 && touchStartPan) {
            // Tek parmak - drag
            e.preventDefault();
            const touch = e.touches[0];
            const deltaX = (touch.clientX - touchStartPan.x) / zoom;
            const deltaY = (touch.clientY - touchStartPan.y) / zoom;

            setPanX(lastPan.x + deltaX);
            setPanY(lastPan.y + deltaY);
        }
    }, [lastTouchDistance, touchStartPan, lastPan, zoom]);

    const handleTouchEnd = useCallback(() => {
        setLastTouchDistance(null);
        setTouchStartPan(null);
    }, []);

    // Deep clone fonksiyonu
    const deepClone = (obj: any): any => {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => deepClone(item));
        if (typeof obj === 'object') {
            const copy: any = {};
            Object.keys(obj).forEach(key => {
                copy[key] = deepClone(obj[key]);
            });
            return copy;
        }
    };

    const getAncestors = (tree: TeamTreeNode, nodeId: number): number[] => {
        const path: number[] = [];

        const findPath = (node: TeamTreeNode, targetId: number): boolean => {
            if (node.id === targetId) {
                return true;
            }

            if (node.children) {
                const children = [node.children.left, node.children.right].filter(Boolean) as TeamTreeNode[];
                for (const child of children) {
                    if (findPath(child, targetId)) {
                        path.push(node.id);
                        return true;
                    }
                }
            }
            return false;
        };

        findPath(tree, nodeId);
        return path.reverse();
    };

    // Sol branch'te en derin node'u bulup boş pozisyon döndürme (MLM Mantığı)
    const findLeftBranchEmptyPosition = (tree: TeamTreeNode): { parentId: number; position: 'left' | 'right' } | null => {
        const leftBranch = tree.children?.left;
        if (!leftBranch) {
            // Sol kol boşsa, direkt root'un soluna ekle
            return { parentId: tree.id, position: 'left' };
        }

        let parentNode = leftBranch;
        // Sol omurga boyunca en derine in
        while (parentNode.children?.left) {
            parentNode = parentNode.children.left;
        }

        // En dipteki node'un soluna ekle
        return { parentId: parentNode.id, position: 'left' };
    };

    // Sağ branch'te en derin node'u bulup boş pozisyon döndürme (MLM Mantığı)
    const findRightBranchEmptyPosition = (tree: TeamTreeNode): { parentId: number; position: 'left' | 'right' } | null => {
        const rightBranch = tree.children?.right;
        if (!rightBranch) {
            // Sağ kol boşsa, direkt root'un sağına ekle
            return { parentId: tree.id, position: 'right' };
        }

        let parentNode = rightBranch;
        // Sağ omurga boyunca en derine in
        while (parentNode.children?.right) {
            parentNode = parentNode.children.right;
        }

        // En dipteki node'un sağına ekle
        return { parentId: parentNode.id, position: 'right' };
    };

    // Node bulma fonksiyonu
    const findNodeById = (tree: TeamTreeNode, nodeId: number): TeamTreeNode | null => {
        if (tree.id === nodeId) return tree;

        if (tree.children?.left) {
            const found = findNodeById(tree.children.left, nodeId);
            if (found) return found;
        }

        if (tree.children?.right) {
            const found = findNodeById(tree.children.right, nodeId);
            if (found) return found;
        }

        return null;
    };

    // Üye yerleştirme fonksiyonu
    const placeMember = (memberId: number, direction: 'left' | 'right') => {
        const member = pendingMembers.find(m => m.id === memberId);
        if (!member) return;

        const emptyPosition = direction === 'left'
            ? findLeftBranchEmptyPosition(treeData)
            : findRightBranchEmptyPosition(treeData);

        if (!emptyPosition) {
            alert('Ağaçta uygun boş pozisyon bulunamadı!');
            return;
        }

        const parentNode = findNodeById(treeData, emptyPosition.parentId);
        const nodeLevel = parentNode ? parentNode.level + 1 : 1;

        // Basit unique ID - artık çakışma yok
        const newNode: TeamTreeNode = {
            id: member.id, // Artık direkt kullanabiliriz
            name: member.name,
            level: nodeLevel,
            points: 0,
            monthlyPoints: 0,
            totalEarnings: 0,
            isActive: true,
            joinDate: member.joinDate,
            parentId: emptyPosition.parentId,
            position: emptyPosition.position
        };

        console.log('Yeni üye ekleniyor:', {
            id: newNode.id,
            name: newNode.name,
            parentId: emptyPosition.parentId,
            position: emptyPosition.position
        });

        const newTree = deepClone(treeData);
        insertNodeToTree(newTree, emptyPosition.parentId, newNode, emptyPosition.position);
        setTreeData(newTree);

        // Yeni node'un görünür olması için tüm atalarını expand et
        const ancestors = getAncestors(newTree, newNode.id);
        setCollapsedNodes(prev => {
            const newSet = new Set(prev);
            ancestors.forEach(id => newSet.delete(id));
            newSet.delete(emptyPosition.parentId);
            return newSet;
        });

        setPendingMembers(prev => prev.filter(m => m.id !== memberId));

        setSuccessMessage({
            memberName: member.name,
            position: direction === 'left' ? 'sol' : 'sağ'
        });
        setIsSuccessModalOpen(true);
        setIsModalOpen(false);
    };

    // Node'u tree'ye ekleme fonksiyonu
    const insertNodeToTree = (tree: TeamTreeNode, parentId: number, newNode: TeamTreeNode, position: 'left' | 'right'): boolean => {
        if (tree.id === parentId) {
            if (!tree.children) {
                tree.children = {};
            }
            tree.children[position] = newNode;
            return true;
        }

        if (tree.children?.left && insertNodeToTree(tree.children.left, parentId, newNode, position)) {
            return true;
        }

        if (tree.children?.right && insertNodeToTree(tree.children.right, parentId, newNode, position)) {
            return true;
        }

        return false;
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Ekip Ağacı Yönetimi
                    </h1>
                    <p className="text-gray-600">
                        Ekibinizdeki tüm bağlantıları ve hiyerarşiyi görselleştirin
                    </p>
                </div>

                {/* İstatistikler */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
                    <div className="bg-white rounded-xl shadow-lg p-4 border-l-4 border-blue-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Üye</p>
                                <p className="text-xl font-bold text-gray-900">{stats.totalMembers}</p>
                            </div>
                            <Users className="h-6 w-6 text-blue-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-4 border-l-4 border-green-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Aktif Üye</p>
                                <p className="text-xl font-bold text-gray-900">{stats.activeMembers}</p>
                            </div>
                            <Activity className="h-6 w-6 text-green-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-4 border-l-4 border-purple-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Seviye</p>
                                <p className="text-xl font-bold text-gray-900">{stats.totalLevels}</p>
                            </div>
                            <Award className="h-6 w-6 text-purple-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-4 border-l-4 border-yellow-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Puan</p>
                                <p className="text-xl font-bold text-gray-900">{stats.totalPoints.toLocaleString('tr-TR')}</p>
                            </div>
                            <Star className="h-6 w-6 text-yellow-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-4 border-l-4 border-indigo-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Toplam Kazanç</p>
                                <p className="text-lg font-bold text-gray-900">₺{stats.totalEarnings.toLocaleString('tr-TR')}</p>
                            </div>
                            <TrendingUp className="h-6 w-6 text-indigo-600" />
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-4 border-l-4 border-orange-500">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Aylık Büyüme</p>
                                <p className="text-xl font-bold text-gray-900">%{stats.monthlyGrowth}</p>
                            </div>
                            <TrendingUp className="h-6 w-6 text-orange-600" />
                        </div>
                    </div>
                </div>

                {/* Kontrol Paneli */}
                <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
                    <div className="flex items-center justify-between mb-4">
                        <div>
                            <h2 className="text-xl font-semibold text-gray-900">Yerleştirme Kontrolleri</h2>
                            <p className="text-sm text-gray-600">Yeni katılan üyeleri ağaca yerleştirin</p>
                        </div>
                        <div className="flex items-center space-x-3">
                            {pendingMembers.length > 0 && (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {pendingMembers.length} üye yerleşim bekliyor
                                </span>
                            )}
                            <button
                                onClick={() => setIsModalOpen(true)}
                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                            >
                                <UserPlus className="h-4 w-4 mr-2" />
                                Yerleşim Bekleyenler ({pendingMembers.length})
                            </button>
                        </div>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-start space-x-2">
                            <UserPlus className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div>
                                <h3 className="font-medium text-blue-900">Yerleştirme Sistemi</h3>
                                <p className="text-sm text-blue-700 mt-1">
                                    • Yeni üyeler otomatik olarak sisteme eklenir ve yerleşim bekler<br />
                                    • "Sola Ekle" üyeyi ağacın en sol boş pozisyonuna yerleştirir<br />
                                    • "Sağa Ekle" üyeyi ağacın en sağ boş pozisyonuna yerleştirir<br />
                                    • Yerleştirme işlemi üst seviyelerden alt seviyelere doğru yapılır
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Ağaç Yapısı */}
                <div className="bg-white rounded-xl shadow-lg p-8 mb-8" style={{ overflow: 'hidden' }}>
                    <div className="mb-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-2xl font-semibold text-gray-900 mb-2">Ekip Hiyerarşisi</h2>
                                <p className="text-gray-600">
                                    Ekibinizdeki tüm üyeler ve aralarındaki bağlantılar. Düğümlere tıklayarak alt seviyeleri daraltıp genişletebilirsiniz.
                                </p>
                            </div>

                            {/* Zoom Kontrolleri */}
                            <div className="flex items-center space-x-2">
                                <div className="bg-gray-100 rounded-lg p-1 flex items-center space-x-1">
                                    <button
                                        onClick={handleZoomOut}
                                        className="p-2 rounded-md hover:bg-gray-200 transition-colors"
                                        title="Küçült"
                                    >
                                        <ZoomOut className="h-4 w-4 text-gray-600" />
                                    </button>
                                    <span className="text-sm font-medium text-gray-700 min-w-[60px] text-center">
                                        {Math.round(zoom * 100)}%
                                    </span>
                                    <button
                                        onClick={handleZoomIn}
                                        className="p-2 rounded-md hover:bg-gray-200 transition-colors"
                                        title="Büyült"
                                    >
                                        <ZoomIn className="h-4 w-4 text-gray-600" />
                                    </button>
                                    <div className="w-px h-6 bg-gray-300 mx-1"></div>
                                    <button
                                        onClick={() => {
                                            setZoom(0.52); // %52 zoom'a sıfırla
                                            setPanX(0);
                                            setPanY(0);
                                            setLastPan({ x: 0, y: 0 });
                                            setIsInitialLoad(true); // Merkeze alma için tekrar aktif et
                                        }}
                                        className="p-2 rounded-md hover:bg-gray-200 transition-colors"
                                        title="Sıfırla"
                                    >
                                        <RotateCcw className="h-4 w-4 text-gray-600" />
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="mt-4 text-sm text-gray-500">
                            💡 <strong>İpucu:</strong> Mouse tekerleği ile zoom yapabilir, mobilde iki parmakla büyütüp küçültebilirsiniz.
                        </div>
                    </div>

                    <div
                        ref={containerRef}
                        className="relative border border-gray-200 rounded-lg"
                        style={{
                            width: '100%',
                            height: '600px',
                            overflow: 'hidden',
                            touchAction: 'none',
                            cursor: isDragging ? 'grabbing' : 'grab'
                        }}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        onMouseLeave={handleMouseUp}
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                    >
                        <div
                            className="absolute inset-0"
                            style={{
                                transform: `scale(${zoom}) translate(${panX}px, ${panY}px)`,
                                transformOrigin: 'center center',
                                transition: isDragging ? 'none' : 'transform 0.1s ease-out',
                                width: width,
                                height: height,
                                pointerEvents: 'none'
                            }}
                        >
                            <svg className="absolute top-0 left-0" width={width} height={height}>
                                <g transform={`translate(${offsetX}, ${offsetY})`}>
                                    {allLinks.map((link, i) => (
                                        <TreeConnection
                                            key={`link-${link.source.data.id}-to-${link.target.data.id}`}
                                            source={link.source as any}
                                            destination={link.target as any}
                                        />
                                    ))}
                                </g>
                            </svg>
                            <div className="relative" style={{ transform: `translate(${offsetX}px, ${offsetY}px)` }}>
                                {allNodes.map((d3Node) => (
                                    <div
                                        key={`node-${d3Node.data.id}`}
                                        style={{ pointerEvents: 'auto' }}
                                    >
                                        <TeamTreeNodeComponent
                                            d3Node={d3Node as any}
                                            onToggleExpand={handleToggleExpand}
                                            isExpanded={!collapsedNodes.has(d3Node.data.id)}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Zoom seviyesi göstergesi */}
                        <div className="absolute bottom-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
                            {Math.round(zoom * 100)}%
                        </div>

                        {/* Sürükleme ipucu */}
                        <div className="absolute top-4 left-4 bg-blue-500 bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
                            🖱️ Sürükle & Zoom
                        </div>
                    </div>
                </div>

                {/* Renk Kodları ve Bilgiler */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Renk Kodları ve Açıklamalar</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded border-2 border-blue-400"></div>
                            <span className="text-sm text-gray-700">Distribütör (Sen)</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-green-600 rounded border-2 border-green-400"></div>
                            <span className="text-sm text-gray-700">Aktif Üye</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 bg-gradient-to-r from-gray-400 to-gray-600 rounded border-2 border-red-300"></div>
                            <span className="text-sm text-gray-700">Pasif Üye</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <ChevronDown className="h-4 w-4 text-gray-600" />
                            <span className="text-sm text-gray-700">Alt seviyeyi genişlet</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <ChevronUp className="h-4 w-4 text-gray-600" />
                            <span className="text-sm text-gray-700">Alt seviyeyi daralt</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <UserPlus className="h-4 w-4 text-blue-600" />
                            <span className="text-sm text-gray-700">Yerleşim bekleyen üye</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <div className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">Sol Kol</div>
                            <span className="text-sm text-gray-700">Sol kolda yer alan üye</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <div className="px-2 py-0.5 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">Sağ Kol</div>
                            <span className="text-sm text-gray-700">Sağ kolda yer alan üye</span>
                        </div>
                    </div>
                </div>

                {/* Modals */}
                <PendingPlacementModal
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    pendingMembers={pendingMembers}
                    onPlaceLeft={(memberId) => placeMember(memberId, 'left')}
                    onPlaceRight={(memberId) => placeMember(memberId, 'right')}
                />

                <PlacementSuccessModal
                    isOpen={isSuccessModalOpen}
                    onClose={() => setIsSuccessModalOpen(false)}
                    memberName={successMessage.memberName}
                    position={successMessage.position}
                />

            </div>
        </div>
    );
};

export default TeamTreePage; 